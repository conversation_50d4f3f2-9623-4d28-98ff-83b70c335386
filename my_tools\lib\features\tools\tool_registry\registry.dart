import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../app/router/routes.dart';
import 'models/tool_definition.dart';

/// 工具注册表Provider
final toolRegistryProvider = Provider<ToolRegistry>((ref) {
  return ToolRegistry();
});

/// 已注册工具列表Provider
final registeredToolsProvider = Provider<List<ToolDefinition>>((ref) {
  final registry = ref.watch(toolRegistryProvider);
  return registry.getAllTools();
});

/// 按分类分组的工具Provider
final toolsByCategoryProvider = Provider<Map<ToolCategory, List<ToolDefinition>>>((ref) {
  final tools = ref.watch(registeredToolsProvider);
  final Map<ToolCategory, List<ToolDefinition>> grouped = {};
  
  for (final tool in tools) {
    grouped.putIfAbsent(tool.category, () => []).add(tool);
  }
  
  return grouped;
});

/// 工具注册表
/// 管理所有可用工具的注册和获取
class ToolRegistry {
  static final List<ToolDefinition> _tools = [];

  /// 注册工具
  void registerTool(ToolDefinition tool) {
    // 检查是否已存在相同ID的工具
    final existingIndex = _tools.indexWhere((t) => t.id == tool.id);
    if (existingIndex != -1) {
      _tools[existingIndex] = tool; // 更新现有工具
    } else {
      _tools.add(tool); // 添加新工具
    }
  }

  /// 批量注册工具
  void registerTools(List<ToolDefinition> tools) {
    for (final tool in tools) {
      registerTool(tool);
    }
  }

  /// 获取所有工具
  List<ToolDefinition> getAllTools() {
    return List.unmodifiable(_tools.where((tool) => tool.isEnabled));
  }

  /// 根据分类获取工具
  List<ToolDefinition> getToolsByCategory(ToolCategory category) {
    return _tools.where((tool) => tool.category == category && tool.isEnabled).toList();
  }

  /// 根据ID获取工具
  ToolDefinition? getToolById(String id) {
    try {
      return _tools.firstWhere((tool) => tool.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 搜索工具
  List<ToolDefinition> searchTools(String query) {
    final lowerQuery = query.toLowerCase();
    return _tools.where((tool) {
      return tool.isEnabled &&
          (tool.name.toLowerCase().contains(lowerQuery) ||
           tool.description.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// 初始化默认工具
  static void initializeDefaultTools() {
    final registry = ToolRegistry();
    
    // 注册AES加密工具
    registry.registerTool(
      const ToolDefinition(
        id: 'aes_crypto',
        name: 'AES加密工具',
        description: '支持多种模式的AES加密解密工具',
        icon: Icons.security,
        route: AppRoutes.aesCrypto,
        category: ToolCategory.security,
        isEnabled: true,
      ),
    );

    // 注册JavaScript执行器工具
    registry.registerTool(
      const ToolDefinition(
        id: 'js_executor',
        name: 'JavaScript执行器',
        description: '在线执行JavaScript代码，支持代码片段管理',
        icon: Icons.code,
        route: AppRoutes.jsExecutor,
        category: ToolCategory.text,
        isEnabled: true,
      ),
    );

    // 未来可以在这里添加更多工具
    // registry.registerTool(...);
  }
}
