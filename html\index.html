<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AES 加密解密工具</title>

    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#666666">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="AES工具">

    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="icon.svg">
    <link rel="apple-touch-icon" href="icon.svg">

    <script src="./crypto-js.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON>goe UI', <PERSON><PERSON>, sans-serif;
            background: #ffffff;
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        /* 上方输入区域 */
        .input-section {
            border: 1px solid #e0e0e0;
            margin: 20px;
            background: #ffffff;
        }

        .input-header {
            background: #f8f9fa;
            padding: 12px 16px;
            font-size: 14px;
            color: #666;
            border-bottom: 1px solid #e0e0e0;
        }

        .input-textarea {
            width: 100%;
            min-height: 120px;
            border: none;
            padding: 16px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 14px;
            resize: vertical;
            background: transparent;
            outline: none;
            line-height: 1.6;
        }

        .input-actions {
            padding: 12px 16px;
            border-top: 1px solid #e0e0e0;
            background: #f8f9fa;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            background: #ffffff;
            font-size: 12px;
            cursor: pointer;
            color: #666;
            text-decoration: none;
        }

        .action-btn:hover {
            background: #f8f9fa;
        }

        /* 中间选项区域 */
        .options-section {
            padding: 20px;
            background: #ffffff;
            border-top: 1px solid #e0e0e0;
            border-bottom: 1px solid #e0e0e0;
        }

        .options-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .option-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .option-number {
            background: #666;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .option-select {
            padding: 6px 8px;
            border: 1px solid #e0e0e0;
            font-size: 14px;
            background: white;
            outline: none;
        }

        .option-select:focus {
            border-color: #666;
        }

        .option-input {
            padding: 6px 8px;
            border: 1px solid #e0e0e0;
            font-size: 14px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            outline: none;
        }

        .option-input:focus {
            border-color: #666;
        }

        .add-btn {
            background: #666;
            color: white;
            border: none;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-btn:hover {
            background: #555;
        }

        /* 下方输出区域 */
        .output-section {
            border: 1px solid #e0e0e0;
            margin: 20px;
            background: #ffffff;
        }

        .output-header {
            background: #f8f9fa;
            padding: 12px 16px;
            font-size: 14px;
            color: #666;
            border-bottom: 1px solid #e0e0e0;
        }

        .output-textarea {
            width: 100%;
            min-height: 120px;
            border: none;
            padding: 16px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 14px;
            resize: vertical;
            background: transparent;
            outline: none;
            color: #333;
            line-height: 1.6;
        }

        .output-actions {
            padding: 12px 16px;
            border-top: 1px solid #e0e0e0;
            background: #f8f9fa;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            
            .options-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .option-group {
                width: 100%;
                justify-content: space-between;
            }
            
            .option-select,
            .option-input {
                flex: 1;
                min-width: 120px;
            }
        }

        /* 状态提示 */
        .status-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .status-message.show {
            opacity: 1;
        }

        .status-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 上方输入区域 -->
        <div class="input-section">
            <div class="input-header">
                原文
            </div>
            <textarea id="inputText" class="input-textarea" placeholder="请输入要加密或解密的文本..."></textarea>
            <div class="input-actions">
                <button class="action-btn" onclick="clearInput()">🗑️ 全部</button>
                <button class="action-btn" onclick="copyInput()">💾 保存</button>
                <button class="action-btn" onclick="pasteInput()">📋 复制</button>
            </div>
        </div>

        <!-- 中间选项区域 -->
        <div class="options-section">
            <div class="options-row">
                <div class="option-group">
                    <select id="algorithm" class="option-select">
                        <option value="AES">AES</option>
                    </select>
                </div>
                

                
                <div class="option-group">
                    <select id="mode" class="option-select">
                        <option value="ECB" selected>ECB</option>
                        <option value="CBC">CBC</option>
                        <option value="CFB">CFB</option>
                        <option value="OFB">OFB</option>
                    </select>
                </div>
                
                <div class="option-group">
                    <select id="keySize" class="option-select">
                        <option value="128" selected>128</option>
                        <option value="192">192</option>
                        <option value="256">256</option>
                    </select>
                </div>
                
                <div class="option-group">
                    <select id="padding" class="option-select">
                        <option value="Pkcs7" selected>Pkcs7</option>
                    </select>
                </div>
                
                <div class="option-group">
                    <select id="outputFormat" class="option-select">
                        <option value="Base64" selected>Base64</option>
                        <option value="Hex">Hex</option>
                        <option value="Utf8">Utf8</option>
                    </select>
                </div>
                
            </div>
            
            <div class="options-row">
                <div class="option-group">
                    <label>Key</label>
                    <input type="text" id="keyInput" class="option-input" placeholder="密钥" value="">
                </div>
                
                <div class="option-group">
                    <select class="option-select">
                        <option>Utf8</option>
                        <option>Hex</option>
                        <option>Base64</option>
                    </select>
                </div>
                
                <div class="option-group">
                    <label>IV</label>
                    <input type="text" id="ivInput" class="option-input" placeholder="初始向量" disabled>
                </div>
                
                <div class="option-group">
                    <select class="option-select">
                        <option>Utf8</option>
                        <option>Hex</option>
                        <option>Base64</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 下方输出区域 -->
        <div class="output-section">
            <div class="output-header">
                请输入
            </div>
            <textarea id="outputText" class="output-textarea" placeholder="输入密文自动解密到上方，或显示加密结果..."></textarea>
            <div class="output-actions">
                <button class="action-btn" onclick="clearOutput()">🗑️ 全部</button>
                <button class="action-btn" onclick="copyOutput()">💾 保存</button>
                <button class="action-btn" onclick="copyOutput()">📋 复制</button>
            </div>
        </div>
    </div>

    <!-- 状态提示 -->
    <div id="statusMessage" class="status-message"></div>

    <script>
        let isProcessing = false; // 防止循环处理
        let lastActiveField = 'input'; // 记录最后活跃的输入框：'input' 或 'output'

        // 上方输入框变化 - 自动加密到下方
        document.getElementById('inputText').addEventListener('input', function() {
            if (isProcessing) return;
            lastActiveField = 'input';
            processInputToOutput();
        });

        // 下方输出框变化 - 自动解密到上方
        document.getElementById('outputText').addEventListener('input', function() {
            if (isProcessing) return;
            lastActiveField = 'output';
            processOutputToInput();
        });

        // 选项变化时重新处理 - 根据最后活跃的字段决定处理方向
        function handleOptionChange() {
            if (lastActiveField === 'output') {
                processOutputToInput();
            } else {
                processInputToOutput();
            }
        }

        document.getElementById('mode').addEventListener('change', function() {
            const mode = this.value;
            const ivInput = document.getElementById('ivInput');
            if (mode === 'ECB') {
                ivInput.disabled = true;
                ivInput.value = '';
            } else {
                ivInput.disabled = false;
            }
            handleOptionChange();
        });
        document.getElementById('keySize').addEventListener('change', handleOptionChange);
        document.getElementById('padding').addEventListener('change', handleOptionChange);
        document.getElementById('outputFormat').addEventListener('change', handleOptionChange);
        document.getElementById('keyInput').addEventListener('input', handleOptionChange);
        document.getElementById('ivInput').addEventListener('input', handleOptionChange);

        // 上方输入 → 下方加密输出
        function processInputToOutput() {
            const inputText = document.getElementById('inputText').value.trim();

            if (!inputText) {
                isProcessing = true;
                document.getElementById('outputText').value = '';
                isProcessing = false;
                return;
            }

            try {
                isProcessing = true;
                const result = encryptText(inputText);
                document.getElementById('outputText').value = result;
                isProcessing = false;
                showStatus('加密成功', 'success');
            } catch (error) {
                isProcessing = true;
                document.getElementById('outputText').value = '加密错误: ' + error.message;
                isProcessing = false;
                showStatus('加密失败: ' + error.message, 'error');
            }
        }

        // 下方输入 → 上方解密输出
        function processOutputToInput() {
            const outputText = document.getElementById('outputText').value.trim();

            if (!outputText) {
                isProcessing = true;
                document.getElementById('inputText').value = '';
                isProcessing = false;
                return;
            }

            try {
                isProcessing = true;
                const result = decryptText(outputText);
                document.getElementById('inputText').value = result;
                isProcessing = false;
                showStatus('解密成功', 'success');
            } catch (error) {
                isProcessing = true;
                document.getElementById('inputText').value = '解密错误: ' + error.message;
                isProcessing = false;
                showStatus('解密失败: ' + error.message, 'error');
            }
        }

        function encryptText(plaintext) {
            const keyInput = document.getElementById('keyInput').value.trim();
            const mode = document.getElementById('mode').value;
            const keySize = parseInt(document.getElementById('keySize').value);
            const outputFormat = document.getElementById('outputFormat').value;
            const ivInput = document.getElementById('ivInput').value.trim();

            // 处理密钥 - 使用padding填充（与原版本保持一致）
            let key;
            const keyBytes = keySize / 8;

            if (keyInput.length === keyBytes * 2 && /^[0-9a-fA-F]+$/.test(keyInput)) {
                // 如果是正确长度的十六进制字符串
                key = CryptoJS.enc.Hex.parse(keyInput);
            } else {
                // 字符串密钥，使用padding填充到指定长度
                let keyString = keyInput;

                if (keyString.length < keyBytes) {
                    // 如果密钥长度不足，用null字符填充
                    const paddingLength = keyBytes - keyString.length;
                    keyString += '\0'.repeat(paddingLength);
                } else if (keyString.length > keyBytes) {
                    // 如果密钥长度超出，截取到指定长度
                    keyString = keyString.substring(0, keyBytes);
                }

                key = CryptoJS.enc.Utf8.parse(keyString);
            }

            // 解析IV（ECB模式不需要IV）
            let iv = null;
            if (mode !== 'ECB' && ivInput) {
                if (ivInput.length === 32 && /^[0-9a-fA-F]+$/.test(ivInput)) {
                    // 十六进制IV
                    iv = CryptoJS.enc.Hex.parse(ivInput);
                } else {
                    // 字符串IV，扩展到16字节
                    let ivString = ivInput;
                    while (ivString.length < 16) {
                        ivString += ivInput;
                    }
                    ivString = ivString.substring(0, 16);
                    iv = CryptoJS.enc.Utf8.parse(ivString);
                }
            }

            // 获取加密模式
            let cryptoMode;
            switch (mode) {
                case 'CBC': cryptoMode = CryptoJS.mode.CBC; break;
                case 'ECB': cryptoMode = CryptoJS.mode.ECB; break;
                case 'CFB': cryptoMode = CryptoJS.mode.CFB; break;
                case 'OFB': cryptoMode = CryptoJS.mode.OFB; break;
                default: cryptoMode = CryptoJS.mode.ECB;
            }

            // 加密
            const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
                iv: iv,
                mode: cryptoMode,
                padding: CryptoJS.pad.Pkcs7
            });

            // 返回指定格式
            if (outputFormat === 'Base64') {
                return encrypted.toString();
            } else if (outputFormat === 'Hex') {
                return encrypted.ciphertext.toString(CryptoJS.enc.Hex);
            } else {
                return encrypted.toString();
            }
        }

        function decryptText(ciphertext) {
            const keyInput = document.getElementById('keyInput').value.trim();
            const mode = document.getElementById('mode').value;
            const keySize = parseInt(document.getElementById('keySize').value);
            const ivInput = document.getElementById('ivInput').value.trim();

            // 处理密钥 - 使用padding填充（与原版本保持一致）
            let key;
            const keyBytes = keySize / 8;

            if (keyInput.length === keyBytes * 2 && /^[0-9a-fA-F]+$/.test(keyInput)) {
                // 如果是正确长度的十六进制字符串
                key = CryptoJS.enc.Hex.parse(keyInput);
            } else {
                // 字符串密钥，使用padding填充到指定长度
                let keyString = keyInput;

                if (keyString.length < keyBytes) {
                    // 如果密钥长度不足，用null字符填充
                    const paddingLength = keyBytes - keyString.length;
                    keyString += '\0'.repeat(paddingLength);
                } else if (keyString.length > keyBytes) {
                    // 如果密钥长度超出，截取到指定长度
                    keyString = keyString.substring(0, keyBytes);
                }

                key = CryptoJS.enc.Utf8.parse(keyString);
            }

            // 解析IV（ECB模式不需要IV）
            let iv = null;
            if (mode !== 'ECB' && ivInput) {
                if (ivInput.length === 32 && /^[0-9a-fA-F]+$/.test(ivInput)) {
                    // 十六进制IV
                    iv = CryptoJS.enc.Hex.parse(ivInput);
                } else {
                    // 字符串IV，扩展到16字节
                    let ivString = ivInput;
                    while (ivString.length < 16) {
                        ivString += ivInput;
                    }
                    ivString = ivString.substring(0, 16);
                    iv = CryptoJS.enc.Utf8.parse(ivString);
                }
            }

            // 获取加密模式
            let cryptoMode;
            switch (mode) {
                case 'CBC': cryptoMode = CryptoJS.mode.CBC; break;
                case 'ECB': cryptoMode = CryptoJS.mode.ECB; break;
                case 'CFB': cryptoMode = CryptoJS.mode.CFB; break;
                case 'OFB': cryptoMode = CryptoJS.mode.OFB; break;
                default: cryptoMode = CryptoJS.mode.ECB;
            }

            // 解密
            const decrypted = CryptoJS.AES.decrypt(ciphertext, key, {
                iv: iv,
                mode: cryptoMode,
                padding: CryptoJS.pad.Pkcs7
            });

            return decrypted.toString(CryptoJS.enc.Utf8);
        }

        function clearInput() {
            document.getElementById('inputText').value = '';
            document.getElementById('outputText').value = '';
        }

        function clearOutput() {
            document.getElementById('outputText').value = '';
        }

        function copyInput() {
            const inputText = document.getElementById('inputText').value;
            navigator.clipboard.writeText(inputText).then(() => {
                showStatus('输入内容已复制', 'success');
            });
        }

        function copyOutput() {
            const outputText = document.getElementById('outputText').value;
            navigator.clipboard.writeText(outputText).then(() => {
                showStatus('输出内容已复制', 'success');
            });
        }

        function pasteInput() {
            navigator.clipboard.readText().then(text => {
                document.getElementById('inputText').value = text;
                processInputToOutput();
                showStatus('内容已粘贴', 'success');
            });
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status-message ${type} show`;

            setTimeout(() => {
                statusEl.classList.remove('show');
            }, 3000);
        }

        // 初始化时处理一次
        processInputToOutput();

        // 注册Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('./sw.js')
                    .then(function(registration) {
                        console.log('SW注册成功: ', registration.scope);
                    }, function(err) {
                        console.log('SW注册失败: ', err);
                    });
            });
        }

        // PWA安装提示
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            showInstallButton();
        });

        function showInstallButton() {
            // 可以在这里显示安装按钮
            console.log('PWA可以安装');
        }

        // 检测是否已安装
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA已安装');
        });
    </script>
</body>
</html>
