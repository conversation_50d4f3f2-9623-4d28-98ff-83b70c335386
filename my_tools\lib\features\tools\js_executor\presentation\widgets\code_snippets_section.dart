import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/js_executor_providers.dart';
import '../../domain/models/js_code_snippet.dart';
import '../../../../../shared/utils/app_logger.dart';

/// 代码片段管理区域
class CodeSnippetsSection extends ConsumerStatefulWidget {
  const CodeSnippetsSection({super.key});

  @override
  ConsumerState<CodeSnippetsSection> createState() => _CodeSnippetsSectionState();
}

class _CodeSnippetsSectionState extends ConsumerState<CodeSnippetsSection> {
  late final TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredSnippetsAsync = ref.watch(filteredSnippetsProvider);
    final selectedSnippet = ref.watch(selectedSnippetProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索框
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: '搜索代码片段...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        ref.read(searchQueryProvider.notifier).state = '';
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              ref.read(searchQueryProvider.notifier).state = value;
            },
          ),
          const SizedBox(height: 16),

          // 代码片段列表
          Expanded(
            child: filteredSnippetsAsync.when(
              data: (snippets) {
                if (snippets.isEmpty) {
                  return _buildEmptyState();
                }

                return ListView.builder(
                  itemCount: snippets.length,
                  itemBuilder: (context, index) {
                    final snippet = snippets[index];
                    final isSelected = selectedSnippet?.id == snippet.id;

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      color: isSelected ? Colors.blue.shade50 : null,
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isSelected 
                              ? Colors.blue 
                              : Colors.grey.shade400,
                          child: Icon(
                            Icons.code,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        title: Text(
                          snippet.name,
                          style: TextStyle(
                            fontWeight: isSelected 
                                ? FontWeight.bold 
                                : FontWeight.normal,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (snippet.description.isNotEmpty)
                              Text(snippet.description),
                            const SizedBox(height: 4),
                            Text(
                              '更新时间: ${_formatDateTime(snippet.updatedAt)}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                        trailing: PopupMenuButton<String>(
                          onSelected: (value) => _handleMenuAction(value, snippet),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit, size: 18),
                                  SizedBox(width: 8),
                                  Text('编辑'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'duplicate',
                              child: Row(
                                children: [
                                  Icon(Icons.copy, size: 18),
                                  SizedBox(width: 8),
                                  Text('复制'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, size: 18, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('删除', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                        ),
                        onTap: () => _selectSnippet(snippet),
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) {
                AppLogger.error('加载代码片段失败', error, stackTrace);
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('加载失败: ${error.toString()}'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          ref.invalidate(filteredSnippetsProvider);
                        },
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    final searchQuery = ref.watch(searchQueryProvider);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            searchQuery.isEmpty ? Icons.code_off : Icons.search_off,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            searchQuery.isEmpty 
                ? '暂无代码片段\n请在代码编辑页面创建'
                : '未找到匹配的代码片段',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey,
            ),
          ),
          if (searchQuery.isNotEmpty) ...[
            const SizedBox(height: 16),
            TextButton(
              onPressed: () {
                _searchController.clear();
                ref.read(searchQueryProvider.notifier).state = '';
              },
              child: const Text('清除搜索'),
            ),
          ],
        ],
      ),
    );
  }

  /// 选择代码片段
  void _selectSnippet(JsCodeSnippet snippet) {
    ref.read(selectedSnippetProvider.notifier).state = snippet;
    AppLogger.info('选择代码片段', {
      'snippet_id': snippet.id,
      'snippet_name': snippet.name,
    });
  }

  /// 处理菜单操作
  void _handleMenuAction(String action, JsCodeSnippet snippet) {
    switch (action) {
      case 'edit':
        _editSnippet(snippet);
        break;
      case 'duplicate':
        _duplicateSnippet(snippet);
        break;
      case 'delete':
        _deleteSnippet(snippet);
        break;
    }
  }

  /// 编辑代码片段
  void _editSnippet(JsCodeSnippet snippet) {
    final nameController = TextEditingController(text: snippet.name);
    final descriptionController = TextEditingController(text: snippet.description);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑代码片段'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(labelText: '名称'),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(labelText: '描述'),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              final name = nameController.text.trim();
              if (name.isEmpty) return;

              final updatedSnippet = snippet.copyWith(
                name: name,
                description: descriptionController.text.trim(),
              );

              final manager = ref.read(snippetManagerProvider);
              final success = await manager.updateSnippet(updatedSnippet);

              if (context.mounted) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success ? '更新成功' : '更新失败'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 复制代码片段
  void _duplicateSnippet(JsCodeSnippet snippet) {
    final newSnippet = snippet.copyWith(
      id: 0, // 新ID将由数据库生成
      name: '${snippet.name} (副本)',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    ref.read(selectedSnippetProvider.notifier).state = newSnippet;
    ref.read(codeEditorProvider.notifier).state = newSnippet.code;
  }

  /// 删除代码片段
  void _deleteSnippet(JsCodeSnippet snippet) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除代码片段 "${snippet.name}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              final manager = ref.read(snippetManagerProvider);
              final success = await manager.deleteSnippet(snippet.id);

              if (context.mounted) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success ? '删除成功' : '删除失败'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
