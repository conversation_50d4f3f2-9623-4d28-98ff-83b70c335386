// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crypto_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CryptoResultImpl _$$CryptoResultImplFromJson(Map<String, dynamic> json) =>
    _$CryptoResultImpl(
      success: json['success'] as bool,
      data: json['data'] as String? ?? '',
      error: json['error'] as String?,
      operation:
          $enumDecodeNullable(_$CryptoOperationEnumMap, json['operation']) ??
              CryptoOperation.encrypt,
    );

Map<String, dynamic> _$$CryptoResultImplToJson(_$CryptoResultImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data,
      'error': instance.error,
      'operation': _$CryptoOperationEnumMap[instance.operation]!,
    };

const _$CryptoOperationEnumMap = {
  CryptoOperation.encrypt: 'encrypt',
  CryptoOperation.decrypt: 'decrypt',
};
