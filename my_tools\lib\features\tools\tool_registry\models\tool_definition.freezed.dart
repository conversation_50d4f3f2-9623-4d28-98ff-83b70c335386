// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tool_definition.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ToolDefinition {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  IconData get icon => throw _privateConstructorUsedError;
  String get route => throw _privateConstructorUsedError;
  ToolCategory get category => throw _privateConstructorUsedError;
  bool get isEnabled => throw _privateConstructorUsedError;

  /// Create a copy of ToolDefinition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ToolDefinitionCopyWith<ToolDefinition> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ToolDefinitionCopyWith<$Res> {
  factory $ToolDefinitionCopyWith(
          ToolDefinition value, $Res Function(ToolDefinition) then) =
      _$ToolDefinitionCopyWithImpl<$Res, ToolDefinition>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      IconData icon,
      String route,
      ToolCategory category,
      bool isEnabled});
}

/// @nodoc
class _$ToolDefinitionCopyWithImpl<$Res, $Val extends ToolDefinition>
    implements $ToolDefinitionCopyWith<$Res> {
  _$ToolDefinitionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ToolDefinition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? icon = null,
    Object? route = null,
    Object? category = null,
    Object? isEnabled = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as IconData,
      route: null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as ToolCategory,
      isEnabled: null == isEnabled
          ? _value.isEnabled
          : isEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ToolDefinitionImplCopyWith<$Res>
    implements $ToolDefinitionCopyWith<$Res> {
  factory _$$ToolDefinitionImplCopyWith(_$ToolDefinitionImpl value,
          $Res Function(_$ToolDefinitionImpl) then) =
      __$$ToolDefinitionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      IconData icon,
      String route,
      ToolCategory category,
      bool isEnabled});
}

/// @nodoc
class __$$ToolDefinitionImplCopyWithImpl<$Res>
    extends _$ToolDefinitionCopyWithImpl<$Res, _$ToolDefinitionImpl>
    implements _$$ToolDefinitionImplCopyWith<$Res> {
  __$$ToolDefinitionImplCopyWithImpl(
      _$ToolDefinitionImpl _value, $Res Function(_$ToolDefinitionImpl) _then)
      : super(_value, _then);

  /// Create a copy of ToolDefinition
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? icon = null,
    Object? route = null,
    Object? category = null,
    Object? isEnabled = null,
  }) {
    return _then(_$ToolDefinitionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as IconData,
      route: null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as ToolCategory,
      isEnabled: null == isEnabled
          ? _value.isEnabled
          : isEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ToolDefinitionImpl implements _ToolDefinition {
  const _$ToolDefinitionImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.icon,
      required this.route,
      required this.category,
      this.isEnabled = false});

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final IconData icon;
  @override
  final String route;
  @override
  final ToolCategory category;
  @override
  @JsonKey()
  final bool isEnabled;

  @override
  String toString() {
    return 'ToolDefinition(id: $id, name: $name, description: $description, icon: $icon, route: $route, category: $category, isEnabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ToolDefinitionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, description, icon, route, category, isEnabled);

  /// Create a copy of ToolDefinition
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ToolDefinitionImplCopyWith<_$ToolDefinitionImpl> get copyWith =>
      __$$ToolDefinitionImplCopyWithImpl<_$ToolDefinitionImpl>(
          this, _$identity);
}

abstract class _ToolDefinition implements ToolDefinition {
  const factory _ToolDefinition(
      {required final String id,
      required final String name,
      required final String description,
      required final IconData icon,
      required final String route,
      required final ToolCategory category,
      final bool isEnabled}) = _$ToolDefinitionImpl;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  IconData get icon;
  @override
  String get route;
  @override
  ToolCategory get category;
  @override
  bool get isEnabled;

  /// Create a copy of ToolDefinition
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ToolDefinitionImplCopyWith<_$ToolDefinitionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
