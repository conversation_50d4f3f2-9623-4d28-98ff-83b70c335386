import 'package:objectbox/objectbox.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'js_code_snippet.freezed.dart';

/// JavaScript代码片段模型
@freezed
class JsCodeSnippet with _$JsCodeSnippet {
  const factory JsCodeSnippet({
    @Default(0) int id,
    required String name,
    required String code,
    @Default('') String description,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(true) bool isEnabled,
  }) = _JsCodeSnippet;
}

/// ObjectBox实体类（用于数据库存储）
@Entity()
class JsCodeSnippetEntity {
  @Id()
  int id = 0;
  
  String name = '';
  String code = '';
  String description = '';
  
  @Property(type: PropertyType.date)
  DateTime? createdAt;
  
  @Property(type: PropertyType.date)
  DateTime? updatedAt;
  
  bool isEnabled = true;

  JsCodeSnippetEntity();

  JsCodeSnippetEntity.fromModel(JsCodeSnippet snippet)
      : id = snippet.id,
        name = snippet.name,
        code = snippet.code,
        description = snippet.description,
        createdAt = snippet.createdAt,
        updatedAt = snippet.updatedAt,
        isEnabled = snippet.isEnabled;

  JsCodeSnippet toModel() {
    return JsCodeSnippet(
      id: id,
      name: name,
      code: code,
      description: description,
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
      isEnabled: isEnabled,
    );
  }
}
