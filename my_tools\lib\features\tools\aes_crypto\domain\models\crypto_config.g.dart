// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crypto_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CryptoConfigImpl _$$CryptoConfigImplFromJson(Map<String, dynamic> json) =>
    _$CryptoConfigImpl(
      key: json['key'] as String? ?? '',
      iv: json['iv'] as String? ?? '',
      mode: $enumDecodeNullable(_$AesModeEnumMap, json['mode']) ?? AesMode.ecb,
      keySize: $enumDecodeNullable(_$AesKeySizeEnumMap, json['keySize']) ??
          AesKeySize.aes128,
      padding: $enumDecodeNullable(_$AesPaddingEnumMap, json['padding']) ??
          AesPadding.pkcs7,
      inputFormat:
          $enumDecodeNullable(_$EncodingFormatEnumMap, json['inputFormat']) ??
              EncodingFormat.base64,
      outputFormat:
          $enumDecodeNullable(_$EncodingFormatEnumMap, json['outputFormat']) ??
              EncodingFormat.base64,
    );

Map<String, dynamic> _$$CryptoConfigImplToJson(_$CryptoConfigImpl instance) =>
    <String, dynamic>{
      'key': instance.key,
      'iv': instance.iv,
      'mode': _$AesModeEnumMap[instance.mode]!,
      'keySize': _$AesKeySizeEnumMap[instance.keySize]!,
      'padding': _$AesPaddingEnumMap[instance.padding]!,
      'inputFormat': _$EncodingFormatEnumMap[instance.inputFormat]!,
      'outputFormat': _$EncodingFormatEnumMap[instance.outputFormat]!,
    };

const _$AesModeEnumMap = {
  AesMode.ecb: 'ecb',
  AesMode.cbc: 'cbc',
  AesMode.cfb: 'cfb',
  AesMode.ofb: 'ofb',
  AesMode.gcm: 'gcm',
};

const _$AesKeySizeEnumMap = {
  AesKeySize.aes128: '128',
  AesKeySize.aes192: '192',
  AesKeySize.aes256: '256',
};

const _$AesPaddingEnumMap = {
  AesPadding.pkcs7: 'pkcs7',
  AesPadding.pkcs5: 'pkcs5',
  AesPadding.none: 'none',
};

const _$EncodingFormatEnumMap = {
  EncodingFormat.base64: 'base64',
  EncodingFormat.hex: 'hex',
  EncodingFormat.utf8: 'utf8',
};
