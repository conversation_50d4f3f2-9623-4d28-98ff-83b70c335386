import '../models/js_code_snippet.dart';

/// 代码存储服务接口
abstract class CodeStorageService {
  /// 保存代码片段
  /// 
  /// [snippet] - 要保存的代码片段
  /// 
  /// 返回保存后的代码片段（包含生成的ID）
  Future<JsCodeSnippet> saveSnippet(JsCodeSnippet snippet);
  
  /// 更新代码片段
  /// 
  /// [snippet] - 要更新的代码片段
  /// 
  /// 返回更新后的代码片段
  Future<JsCodeSnippet> updateSnippet(JsCodeSnippet snippet);
  
  /// 删除代码片段
  /// 
  /// [id] - 要删除的代码片段ID
  /// 
  /// 返回是否删除成功
  Future<bool> deleteSnippet(int id);
  
  /// 获取所有代码片段
  /// 
  /// 返回所有已保存的代码片段列表
  Future<List<JsCodeSnippet>> getAllSnippets();
  
  /// 根据ID获取代码片段
  /// 
  /// [id] - 代码片段ID
  /// 
  /// 返回对应的代码片段，如果不存在返回null
  Future<JsCodeSnippet?> getSnippetById(int id);
  
  /// 搜索代码片段
  /// 
  /// [query] - 搜索关键词
  /// 
  /// 返回匹配的代码片段列表
  Future<List<JsCodeSnippet>> searchSnippets(String query);
  
  /// 获取启用的代码片段
  /// 
  /// 返回所有启用的代码片段列表
  Future<List<JsCodeSnippet>> getEnabledSnippets();
  
  /// 清理资源
  void dispose();
}
