import 'package:freezed_annotation/freezed_annotation.dart';

part 'crypto_result.freezed.dart';
part 'crypto_result.g.dart';

/// 加密解密结果模型
@freezed
class CryptoResult with _$CryptoResult {
  const factory CryptoResult({
    required bool success,
    @Default('') String data,
    String? error,
    @Default(CryptoOperation.encrypt) CryptoOperation operation,
  }) = _CryptoResult;

  factory CryptoResult.fromJson(Map<String, dynamic> json) =>
      _$CryptoResultFromJson(json);
}

/// 加密操作类型
enum CryptoOperation {
  @JsonValue('encrypt')
  encrypt('加密'),
  @JsonValue('decrypt')
  decrypt('解密');

  const CryptoOperation(this.displayName);
  final String displayName;
}

/// 加密结果扩展方法
extension CryptoResultExtension on CryptoResult {
  /// 是否为成功结果
  bool get isSuccess => success && error == null;
  
  /// 是否为失败结果
  bool get isFailure => !success || error != null;
  
  /// 获取错误信息
  String get errorMessage => error ?? '未知错误';
}
