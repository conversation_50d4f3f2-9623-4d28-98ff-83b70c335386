// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'js_execution_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$JsExecutionResultImpl _$$JsExecutionResultImplFromJson(
        Map<String, dynamic> json) =>
    _$JsExecutionResultImpl(
      success: json['success'] as bool,
      result: json['result'] as String? ?? '',
      error: json['error'] as String?,
      executionTimeMs: (json['executionTimeMs'] as num?)?.toInt() ?? 0,
      executedAt: json['executedAt'] == null
          ? null
          : DateTime.parse(json['executedAt'] as String),
    );

Map<String, dynamic> _$$JsExecutionResultImplToJson(
        _$JsExecutionResultImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'result': instance.result,
      'error': instance.error,
      'executionTimeMs': instance.executionTimeMs,
      'executedAt': instance.executedAt?.toIso8601String(),
    };
