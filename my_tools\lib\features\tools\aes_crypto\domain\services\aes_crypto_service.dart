import '../models/crypto_config.dart';
import '../models/crypto_result.dart';

/// AES加密服务接口
abstract class AesCryptoService {
  /// 加密文本
  Future<CryptoResult> encrypt(String plaintext, CryptoConfig config);
  
  /// 解密文本
  Future<CryptoResult> decrypt(String ciphertext, CryptoConfig config);
  
  /// 自动处理（根据输入内容判断是加密还是解密）
  Future<CryptoResult> autoProcess(String input, CryptoConfig config);
  
  /// 验证配置是否有效
  bool validateConfig(CryptoConfig config);
  
  /// 生成随机IV
  String generateRandomIV(AesMode mode);
  
  /// 处理密钥（填充到指定长度）
  String processKey(String key, AesKeySize keySize);
}
