// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'crypto_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CryptoResult _$CryptoResultFromJson(Map<String, dynamic> json) {
  return _CryptoResult.fromJson(json);
}

/// @nodoc
mixin _$CryptoResult {
  bool get success => throw _privateConstructorUsedError;
  String get data => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  CryptoOperation get operation => throw _privateConstructorUsedError;

  /// Serializes this CryptoResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CryptoResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CryptoResultCopyWith<CryptoResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CryptoResultCopyWith<$Res> {
  factory $CryptoResultCopyWith(
          CryptoResult value, $Res Function(CryptoResult) then) =
      _$CryptoResultCopyWithImpl<$Res, CryptoResult>;
  @useResult
  $Res call(
      {bool success, String data, String? error, CryptoOperation operation});
}

/// @nodoc
class _$CryptoResultCopyWithImpl<$Res, $Val extends CryptoResult>
    implements $CryptoResultCopyWith<$Res> {
  _$CryptoResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CryptoResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = null,
    Object? error = freezed,
    Object? operation = null,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      operation: null == operation
          ? _value.operation
          : operation // ignore: cast_nullable_to_non_nullable
              as CryptoOperation,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CryptoResultImplCopyWith<$Res>
    implements $CryptoResultCopyWith<$Res> {
  factory _$$CryptoResultImplCopyWith(
          _$CryptoResultImpl value, $Res Function(_$CryptoResultImpl) then) =
      __$$CryptoResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success, String data, String? error, CryptoOperation operation});
}

/// @nodoc
class __$$CryptoResultImplCopyWithImpl<$Res>
    extends _$CryptoResultCopyWithImpl<$Res, _$CryptoResultImpl>
    implements _$$CryptoResultImplCopyWith<$Res> {
  __$$CryptoResultImplCopyWithImpl(
      _$CryptoResultImpl _value, $Res Function(_$CryptoResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of CryptoResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = null,
    Object? error = freezed,
    Object? operation = null,
  }) {
    return _then(_$CryptoResultImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      operation: null == operation
          ? _value.operation
          : operation // ignore: cast_nullable_to_non_nullable
              as CryptoOperation,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CryptoResultImpl implements _CryptoResult {
  const _$CryptoResultImpl(
      {required this.success,
      this.data = '',
      this.error,
      this.operation = CryptoOperation.encrypt});

  factory _$CryptoResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$CryptoResultImplFromJson(json);

  @override
  final bool success;
  @override
  @JsonKey()
  final String data;
  @override
  final String? error;
  @override
  @JsonKey()
  final CryptoOperation operation;

  @override
  String toString() {
    return 'CryptoResult(success: $success, data: $data, error: $error, operation: $operation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CryptoResultImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.operation, operation) ||
                other.operation == operation));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, error, operation);

  /// Create a copy of CryptoResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CryptoResultImplCopyWith<_$CryptoResultImpl> get copyWith =>
      __$$CryptoResultImplCopyWithImpl<_$CryptoResultImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CryptoResultImplToJson(
      this,
    );
  }
}

abstract class _CryptoResult implements CryptoResult {
  const factory _CryptoResult(
      {required final bool success,
      final String data,
      final String? error,
      final CryptoOperation operation}) = _$CryptoResultImpl;

  factory _CryptoResult.fromJson(Map<String, dynamic> json) =
      _$CryptoResultImpl.fromJson;

  @override
  bool get success;
  @override
  String get data;
  @override
  String? get error;
  @override
  CryptoOperation get operation;

  /// Create a copy of CryptoResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CryptoResultImplCopyWith<_$CryptoResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
