import 'package:freezed_annotation/freezed_annotation.dart';

part 'js_execution_result.freezed.dart';
part 'js_execution_result.g.dart';

/// JavaScript执行结果模型
@freezed
class JsExecutionResult with _$JsExecutionResult {
  const factory JsExecutionResult({
    required bool success,
    @Default('') String result,
    String? error,
    @Default(0) int executionTimeMs,
    DateTime? executedAt,
  }) = _JsExecutionResult;

  /// 创建成功结果
  factory JsExecutionResult.createSuccess({
    required String result,
    int executionTimeMs = 0,
  }) {
    return JsExecutionResult(
      success: true,
      result: result,
      executionTimeMs: executionTimeMs,
      executedAt: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory JsExecutionResult.createError({
    required String error,
    int executionTimeMs = 0,
  }) {
    return JsExecutionResult(
      success: false,
      error: error,
      executionTimeMs: executionTimeMs,
      executedAt: DateTime.now(),
    );
  }

  factory JsExecutionResult.fromJson(Map<String, dynamic> json) =>
      _$JsExecutionResultFromJson(json);
}

/// 扩展方法
extension JsExecutionResultX on JsExecutionResult {
  /// 是否执行成功
  bool get isSuccess => success;
  
  /// 是否执行失败
  bool get isFailure => !success;
  
  /// 获取显示文本
  String get displayText {
    if (isSuccess) {
      return result.isEmpty ? '(无输出)' : result;
    } else {
      return error ?? '未知错误';
    }
  }
}
