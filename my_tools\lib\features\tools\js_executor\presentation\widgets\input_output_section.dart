import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/js_executor_providers.dart';
import '../../domain/models/js_execution_result.dart';
import '../../../../../shared/utils/app_logger.dart';

/// 输入输出区域
class InputOutputSection extends ConsumerStatefulWidget {
  const InputOutputSection({super.key});

  @override
  ConsumerState<InputOutputSection> createState() => _InputOutputSectionState();
}

class _InputOutputSectionState extends ConsumerState<InputOutputSection> {
  late final TextEditingController _inputController;
  late final TextEditingController _outputController;

  @override
  void initState() {
    super.initState();
    _inputController = TextEditingController();
    _outputController = TextEditingController();
  }

  @override
  void dispose() {
    _inputController.dispose();
    _outputController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 监听输入文本变化
    ref.listen<String>(inputTextProvider, (previous, next) {
      if (next != _inputController.text) {
        _inputController.text = next;
      }
    });

    // 监听输出文本变化
    ref.listen<String>(outputTextProvider, (previous, next) {
      if (next != _outputController.text) {
        _outputController.text = next;
      }
    });

    final isExecuting = ref.watch(isExecutingProvider);
    final executionResult = ref.watch(executionResultProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 执行状态指示器
          if (isExecuting || executionResult != null) ...[
            Card(
              color: isExecuting 
                  ? Colors.orange.shade50 
                  : (executionResult?.isSuccess == true 
                      ? Colors.green.shade50 
                      : Colors.red.shade50),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    if (isExecuting)
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    else
                      Icon(
                        executionResult?.isSuccess == true 
                            ? Icons.check_circle 
                            : Icons.error,
                        color: executionResult?.isSuccess == true 
                            ? Colors.green 
                            : Colors.red,
                        size: 20,
                      ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isExecuting 
                            ? '正在执行...' 
                            : (executionResult?.isSuccess == true 
                                ? '执行成功 (${executionResult?.executionTimeMs}ms)'
                                : '执行失败'),
                        style: TextStyle(
                          color: isExecuting 
                              ? Colors.orange.shade700
                              : (executionResult?.isSuccess == true 
                                  ? Colors.green.shade700 
                                  : Colors.red.shade700),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 输入区域
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.input),
                    const SizedBox(width: 8),
                    Text(
                      '输入数据',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () => _clearInput(),
                      icon: const Icon(Icons.clear, size: 18),
                      label: const Text('清空'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextField(
                      controller: _inputController,
                      maxLines: null,
                      expands: true,
                      decoration: const InputDecoration(
                        hintText: '在这里输入数据...\n\n例如：\n• 文本内容\n• JSON数据\n• URL列表\n• 任何需要处理的数据',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                      ),
                      onChanged: (value) {
                        ref.read(inputTextProvider.notifier).state = value;
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 输出区域
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.output),
                    const SizedBox(width: 8),
                    Text(
                      '输出结果',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: _outputController.text.isNotEmpty 
                          ? () => _copyOutput() 
                          : null,
                      icon: const Icon(Icons.copy, size: 18),
                      label: const Text('复制'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                      color: executionResult?.isFailure == true 
                          ? Colors.red.shade50 
                          : Colors.grey.shade50,
                    ),
                    child: TextField(
                      controller: _outputController,
                      maxLines: null,
                      expands: true,
                      readOnly: true,
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                        color: executionResult?.isFailure == true 
                            ? Colors.red.shade700 
                            : null,
                      ),
                      decoration: InputDecoration(
                        hintText: '执行结果将显示在这里...',
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.all(16),
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 快速操作按钮
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _loadSampleData(),
                  icon: const Icon(Icons.data_object),
                  label: const Text('加载示例数据'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _clearAll(),
                  icon: const Icon(Icons.clear_all),
                  label: const Text('清空所有'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 清空输入
  void _clearInput() {
    _inputController.clear();
    ref.read(inputTextProvider.notifier).state = '';
    AppLogger.debug('清空输入数据');
  }

  /// 复制输出
  void _copyOutput() {
    final output = _outputController.text;
    if (output.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: output));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('输出结果已复制到剪贴板'),
          duration: Duration(seconds: 2),
        ),
      );
      AppLogger.debug('复制输出结果', {'length': output.length});
    }
  }

  /// 加载示例数据
  void _loadSampleData() {
    const sampleData = '''https://www.example.com/page1
这是一个包含多个URL的示例文本。
访问 https://github.com/flutter/flutter 了解更多信息。
还有这个链接：http://www.google.com
以及 https://pub.dev/packages/flutter_riverpod''';

    _inputController.text = sampleData;
    ref.read(inputTextProvider.notifier).state = sampleData;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已加载示例数据'),
        duration: Duration(seconds: 2),
      ),
    );
    
    AppLogger.debug('加载示例数据');
  }

  /// 清空所有
  void _clearAll() {
    _inputController.clear();
    _outputController.clear();
    ref.read(inputTextProvider.notifier).state = '';
    ref.read(outputTextProvider.notifier).state = '';
    ref.read(executionResultProvider.notifier).state = null;
    
    AppLogger.debug('清空所有数据');
  }
}
