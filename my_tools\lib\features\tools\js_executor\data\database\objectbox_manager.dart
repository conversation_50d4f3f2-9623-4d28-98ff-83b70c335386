import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import '../../../../../shared/utils/app_logger.dart';
import '../../domain/models/js_code_snippet.dart';
import '../../../../../objectbox.g.dart';

/// ObjectBox数据库管理器
class ObjectBoxManager {
  static ObjectBoxManager? _instance;
  static ObjectBoxManager get instance {
    _instance ??= ObjectBoxManager._internal();
    return _instance!;
  }

  late final Store _store;
  Store get store => _store;

  ObjectBoxManager._internal();

  /// 初始化数据库
  Future<void> initialize() async {
    try {
      AppLogger.info('开始初始化ObjectBox数据库');

      // 获取应用文档目录
      final docsDir = await getApplicationDocumentsDirectory();
      final dbPath = path.join(docsDir.path, 'my_tools_objectbox');
      
      AppLogger.debug('数据库路径', {'path': dbPath});

      // 创建Store
      _store = await openStore(directory: dbPath);
      
      AppLogger.info('ObjectBox数据库初始化成功');
      
      // 初始化默认数据
      await _initializeDefaultData();
      
    } catch (e, stackTrace) {
      AppLogger.error('ObjectBox数据库初始化失败', e, stackTrace);
      rethrow;
    }
  }

  /// 初始化默认数据
  Future<void> _initializeDefaultData() async {
    try {
      final box = _store.box<JsCodeSnippetEntity>();
      
      // 检查是否已有数据
      if (box.count() > 0) {
        AppLogger.debug('数据库已有数据，跳过初始化默认数据');
        return;
      }

      AppLogger.info('初始化默认JavaScript代码片段');

      // 创建默认代码片段
      final defaultSnippets = [
        JsCodeSnippetEntity()
          ..name = '提取HTTP URL'
          ..description = '从文本中提取所有HTTP和HTTPS链接'
          ..code = '''// 从输入文本中提取所有HTTP/HTTPS URL
if (!input) {
  return '请提供输入文本';
}

const urlRegex = /https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)/g;
const urls = input.match(urlRegex);

if (!urls || urls.length === 0) {
  return '未找到任何URL';
}

return urls.join('\\n');'''
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..isEnabled = true,

        JsCodeSnippetEntity()
          ..name = 'JSON格式化'
          ..description = '格式化JSON字符串，使其更易读'
          ..code = '''// 格式化JSON字符串
if (!input) {
  return '请提供JSON字符串';
}

try {
  const parsed = JSON.parse(input);
  return JSON.stringify(parsed, null, 2);
} catch (error) {
  return '无效的JSON格式: ' + error.message;
}'''
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..isEnabled = true,

        JsCodeSnippetEntity()
          ..name = '文本统计'
          ..description = '统计文本的字符数、单词数、行数等信息'
          ..code = '''// 统计文本信息
if (!input) {
  return '请提供输入文本';
}

const text = input.toString();
const lines = text.split('\\n');
const words = text.trim().split(/\\s+/).filter(word => word.length > 0);
const chars = text.length;
const charsNoSpaces = text.replace(/\\s/g, '').length;

const stats = {
  '字符数': chars,
  '字符数(不含空格)': charsNoSpaces,
  '单词数': words.length,
  '行数': lines.length,
  '段落数': text.split('\\n\\n').filter(p => p.trim().length > 0).length
};

return JSON.stringify(stats, null, 2);'''
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..isEnabled = true,

        JsCodeSnippetEntity()
          ..name = 'Base64编解码'
          ..description = '对文本进行Base64编码或解码'
          ..code = r'''// Base64编解码工具
if (!input) {
  return '请提供输入文本';
}

try {
  // 尝试解码（如果是Base64格式）
  if (/^[A-Za-z0-9+/]*={0,2}$/.test(input.trim())) {
    try {
      const decoded = atob(input.trim());
      return '解码结果:\n' + decoded;
    } catch (e) {
      // 如果解码失败，则进行编码
    }
  }

  // 进行编码
  const encoded = btoa(input);
  return '编码结果:\n' + encoded;
} catch (error) {
  return '处理失败: ' + error.message;
}'''
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..isEnabled = true,
      ];

      // 保存默认代码片段
      for (final snippet in defaultSnippets) {
        box.put(snippet);
      }

      AppLogger.info('默认JavaScript代码片段初始化完成', {
        'count': defaultSnippets.length,
      });

    } catch (e, stackTrace) {
      AppLogger.error('初始化默认数据失败', e, stackTrace);
    }
  }

  /// 关闭数据库
  Future<void> close() async {
    try {
      _store.close();
      AppLogger.info('ObjectBox数据库已关闭');
    } catch (e, stackTrace) {
      AppLogger.error('关闭ObjectBox数据库失败', e, stackTrace);
    }
  }
}
