arguments=--init-script C\:\\Users\\Administrator\\AppData\\Roaming\\Code\\User\\globalStorage\\redhat.java\\1.43.1\\config_win\\org.eclipse.osgi\\58\\0\\.cp\\gradle\\init\\init.gradle
auto.sync=true
build.scans.enabled=false
connection.gradle.distribution=GRADLE_DISTRIBUTION(WRAPPER)
connection.project.dir=
eclipse.preferences.version=1
gradle.user.home=D\:/Dev/Env/repo
java.home=D\:/Dev/Env/jdk8
jvm.arguments=
offline.mode=false
override.workspace.settings=true
show.console.view=true
show.executions.view=true
