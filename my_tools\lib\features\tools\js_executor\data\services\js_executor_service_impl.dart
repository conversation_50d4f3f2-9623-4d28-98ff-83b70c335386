import 'package:flutter_js/flutter_js.dart';

import '../../domain/models/js_execution_result.dart';
import '../../domain/services/js_executor_service.dart';
import '../../../../../shared/utils/app_logger.dart';

/// JavaScript执行服务实现
class JsExecutorServiceImpl implements JsExecutorService {
  late final JavascriptRuntime _jsRuntime;
  int _timeoutMs = 5000; // 默认5秒超时

  JsExecutorServiceImpl() {
    _initializeRuntime();
  }

  /// 初始化JavaScript运行时
  void _initializeRuntime() {
    try {
      _jsRuntime = getJavascriptRuntime();
      AppLogger.info('JavaScript运行时初始化成功');
    } catch (e, stackTrace) {
      AppLogger.error('JavaScript运行时初始化失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<JsExecutionResult> executeCode(String code, {String? input}) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      AppLogger.info('开始执行JavaScript代码', {
        'code_length': code.length,
        'has_input': input != null,
        'input_length': input?.length ?? 0,
      });

      // 准备执行环境
      String wrappedCode = _wrapCode(code, input);
      
      // 执行代码
      final result = _jsRuntime.evaluate(wrappedCode);
      
      stopwatch.stop();
      final executionTime = stopwatch.elapsedMilliseconds;
      
      AppLogger.info('JavaScript代码执行成功', {
        'execution_time_ms': executionTime,
        'result_type': result.runtimeType.toString(),
      });

      // 处理结果
      String resultString = _processResult(result);
      
      return JsExecutionResult.createSuccess(
        result: resultString,
        executionTimeMs: executionTime,
      );
      
    } catch (e, stackTrace) {
      stopwatch.stop();
      final executionTime = stopwatch.elapsedMilliseconds;
      
      AppLogger.error('JavaScript代码执行失败', e, stackTrace);
      
      return JsExecutionResult.createError(
        error: _formatError(e),
        executionTimeMs: executionTime,
      );
    }
  }

  @override
  Future<String?> validateSyntax(String code) async {
    try {
      // 尝试解析代码语法
      final testCode = '''
        try {
          new Function(${_escapeString(code)});
          'SYNTAX_OK';
        } catch (e) {
          'SYNTAX_ERROR: ' + e.message;
        }
      ''';
      
      final result = _jsRuntime.evaluate(testCode);
      final resultString = result.toString();
      
      if (resultString == 'SYNTAX_OK') {
        return null; // 语法正确
      } else if (resultString.startsWith('SYNTAX_ERROR: ')) {
        return resultString.substring(14); // 返回错误信息
      } else {
        return '未知语法错误';
      }
    } catch (e) {
      return '语法验证失败: ${e.toString()}';
    }
  }

  @override
  void setTimeout(int timeoutMs) {
    _timeoutMs = timeoutMs;
    AppLogger.debug('设置JavaScript执行超时时间', {'timeout_ms': timeoutMs});
  }

  @override
  int getTimeout() {
    return _timeoutMs;
  }

  @override
  void dispose() {
    try {
      _jsRuntime.dispose();
      AppLogger.info('JavaScript运行时已释放');
    } catch (e) {
      AppLogger.warning('释放JavaScript运行时时出错', e);
    }
  }

  /// 包装代码，添加输入处理和错误捕获
  String _wrapCode(String code, String? input) {
    final inputVar = input != null ? _escapeString(input) : 'null';
    
    return '''
      (function() {
        try {
          // 设置输入变量
          var input = $inputVar;
          
          // 执行用户代码
          var result = (function() {
            $code
          })();
          
          // 返回结果
          if (result === undefined) {
            return '';
          } else if (typeof result === 'object') {
            return JSON.stringify(result, null, 2);
          } else {
            return String(result);
          }
        } catch (error) {
          throw new Error('执行错误: ' + error.message);
        }
      })();
    ''';
  }

  /// 处理执行结果
  String _processResult(dynamic result) {
    if (result == null) {
      return '';
    } else if (result is String) {
      return result;
    } else {
      return result.toString();
    }
  }

  /// 格式化错误信息
  String _formatError(dynamic error) {
    String errorMessage = error.toString();
    
    // 清理常见的错误前缀
    if (errorMessage.startsWith('Exception: ')) {
      errorMessage = errorMessage.substring(11);
    }
    
    return errorMessage;
  }

  /// 转义字符串用于JavaScript
  String _escapeString(String str) {
    return '"${str.replaceAll('\\', '\\\\').replaceAll('"', '\\"').replaceAll('\n', '\\n').replaceAll('\r', '\\r')}"';
  }
}
