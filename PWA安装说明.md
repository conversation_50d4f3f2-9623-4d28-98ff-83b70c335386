# 📱 AES加密工具 - PWA安装说明

## 🎯 什么是PWA？
PWA (Progressive Web App) 是一种可以像原生APP一样安装和使用的网页应用。

## 📲 如何安装到手机？

### Android手机 (Chrome浏览器)
1. 用Chrome浏览器打开 `index.html`
2. 点击地址栏右侧的"安装"图标 📥
3. 或者点击菜单 → "添加到主屏幕"
4. 确认安装，应用图标会出现在桌面

### iPhone (Safari浏览器)  
1. 用Safari浏览器打开 `index.html`
2. 点击底部分享按钮 📤
3. 选择"添加到主屏幕"
4. 确认添加，应用图标会出现在桌面

### 电脑 (Chrome/Edge浏览器)
1. 打开 `index.html`
2. 地址栏会显示安装图标 📥
3. 点击安装，应用会添加到开始菜单

## ✨ PWA特性
- 🔒 **完全离线**：无需网络连接即可使用
- ⚡ **快速启动**：像原生APP一样快速打开
- 📱 **全屏体验**：隐藏浏览器地址栏
- 🎨 **原生外观**：与系统界面完美融合
- 🔄 **自动更新**：后台自动更新到最新版本

## 🚀 部署到服务器
如果要部署到网站：
1. 将所有文件上传到服务器
2. 确保HTTPS访问（PWA要求）
3. 用户访问网址即可安装

## 📁 文件说明
- `index.html` - 主应用文件
- `manifest.json` - PWA配置文件
- `sw.js` - Service Worker (离线支持)
- `icon.svg` - 应用图标
- `crypto-js.min.js` - 加密库

## 🔧 测试方法
1. 在本地启动HTTP服务器：
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Node.js
   npx serve .
   ```
2. 浏览器访问 `http://localhost:8000`
3. 测试安装功能

## ⚠️ 注意事项
- PWA需要HTTPS或localhost环境
- 首次访问需要网络下载资源
- 安装后完全离线可用
- 数据处理完全在本地，保证安全性
