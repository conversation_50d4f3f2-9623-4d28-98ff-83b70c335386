import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/models/crypto_config.dart';
import '../providers/aes_crypto_providers.dart';

/// 可折叠的加密配置组件（移动端专用）
class CollapsibleConfigSection extends ConsumerStatefulWidget {
  final VoidCallback? onConfigChanged;

  const CollapsibleConfigSection({
    super.key,
    this.onConfigChanged,
  });

  @override
  ConsumerState<CollapsibleConfigSection> createState() =>
      _CollapsibleConfigSectionState();
}

class _CollapsibleConfigSectionState
    extends ConsumerState<CollapsibleConfigSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;

  final TextEditingController _keyController = TextEditingController();
  final TextEditingController _ivController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _keyController.dispose();
    _ivController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _triggerConfigChanged() {
    widget.onConfigChanged?.call();
  }

  String _getConfigSummary(CryptoConfig config) {
    return '${config.keySize.displayName}-${config.mode.displayName}-${config.padding.displayName}-${config.outputFormat.displayName}';
  }

  void _generateRandomIV() {
    final config = ref.read(cryptoConfigProvider);
    final configNotifier = ref.read(cryptoConfigProvider.notifier);

    if (config.mode != AesMode.ecb) {
      // 生成16字节随机IV
      final randomBytes = List.generate(16, (index) =>
        DateTime.now().millisecondsSinceEpoch % 256);
      final randomIV = randomBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
      configNotifier.updateIV(randomIV);
      _ivController.text = randomIV;
      _triggerConfigChanged();
    }
  }

  @override
  Widget build(BuildContext context) {
    final config = ref.watch(cryptoConfigProvider);
    final configNotifier = ref.read(cryptoConfigProvider.notifier);

    // 同步控制器文本
    if (_keyController.text != config.key) {
      _keyController.text = config.key;
    }
    if (_ivController.text != config.iv) {
      _ivController.text = config.iv;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // 配置摘要和展开按钮
          InkWell(
            onTap: _toggleExpanded,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.settings,
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '加密配置',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _getConfigSummary(config),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: const Icon(Icons.expand_more),
                  ),
                ],
              ),
            ),
          ),
          
          // 可展开的配置详情
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: [
                  const Divider(),
                  const SizedBox(height: 16),
                  
                  // 密钥输入
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '密钥',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      TextField(
                        controller: _keyController,
                        decoration: const InputDecoration(
                          hintText: '请输入加密密钥',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          isDense: true,
                        ),
                        onChanged: (value) {
                          configNotifier.updateKey(value);
                          _triggerConfigChanged();
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // IV输入
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '初始向量 (IV)',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      TextField(
                        controller: _ivController,
                        enabled: config.mode != AesMode.ecb,
                        decoration: InputDecoration(
                          hintText: config.mode == AesMode.ecb ? 'ECB模式不需要IV' : '请输入IV',
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          isDense: true,
                          suffixIcon: config.mode != AesMode.ecb
                              ? IconButton(
                                  onPressed: _generateRandomIV,
                                  icon: const Icon(Icons.shuffle, size: 20),
                                  tooltip: '生成随机IV',
                                )
                              : null,
                        ),
                        onChanged: (value) {
                          configNotifier.updateIV(value);
                          _triggerConfigChanged();
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // 配置选项 - 2x2网格布局
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '加密模式',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            const SizedBox(height: 4),
                            DropdownButtonFormField<AesMode>(
                              value: config.mode,
                              isExpanded: true,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                isDense: true,
                              ),
                              items: AesMode.values.map((mode) {
                                return DropdownMenuItem<AesMode>(
                                  value: mode,
                                  child: Text(
                                    mode.displayName,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                );
                              }).toList(),
                              onChanged: (mode) {
                                if (mode != null) {
                                  configNotifier.updateMode(mode);
                                  if (mode == AesMode.ecb) {
                                    _ivController.clear();
                                    configNotifier.updateIV('');
                                  }
                                  _triggerConfigChanged();
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '密钥长度',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            const SizedBox(height: 4),
                            DropdownButtonFormField<AesKeySize>(
                              value: config.keySize,
                              isExpanded: true,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                isDense: true,
                              ),
                              items: AesKeySize.values.map((keySize) {
                                return DropdownMenuItem<AesKeySize>(
                                  value: keySize,
                                  child: Text(
                                    keySize.displayName,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                );
                              }).toList(),
                              onChanged: (keySize) {
                                if (keySize != null) {
                                  configNotifier.updateKeySize(keySize);
                                  _triggerConfigChanged();
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '填充方式',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            const SizedBox(height: 4),
                            DropdownButtonFormField<AesPadding>(
                              value: config.padding,
                              isExpanded: true,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                isDense: true,
                              ),
                              items: AesPadding.values.map((padding) {
                                return DropdownMenuItem<AesPadding>(
                                  value: padding,
                                  child: Text(
                                    padding.displayName,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                );
                              }).toList(),
                              onChanged: (padding) {
                                if (padding != null) {
                                  configNotifier.updatePadding(padding);
                                  _triggerConfigChanged();
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '编码格式',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            const SizedBox(height: 4),
                            DropdownButtonFormField<EncodingFormat>(
                              value: config.outputFormat,
                              isExpanded: true,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                isDense: true,
                              ),
                              items: EncodingFormat.values.map((format) {
                                return DropdownMenuItem<EncodingFormat>(
                                  value: format,
                                  child: Text(
                                    format.displayName,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                );
                              }).toList(),
                              onChanged: (format) {
                                if (format != null) {
                                  configNotifier.updateOutputFormat(format);
                                  _triggerConfigChanged();
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 重置按钮
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () {
                        configNotifier.reset();
                        _keyController.clear();
                        _ivController.clear();
                        _triggerConfigChanged();
                      },
                      icon: const Icon(Icons.refresh, size: 18),
                      label: const Text('重置配置'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
