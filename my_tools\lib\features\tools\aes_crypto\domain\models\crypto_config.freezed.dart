// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'crypto_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CryptoConfig _$CryptoConfigFromJson(Map<String, dynamic> json) {
  return _CryptoConfig.fromJson(json);
}

/// @nodoc
mixin _$CryptoConfig {
  String get key => throw _privateConstructorUsedError;
  String get iv => throw _privateConstructorUsedError;
  AesMode get mode => throw _privateConstructorUsedError;
  AesKeySize get keySize => throw _privateConstructorUsedError;
  AesPadding get padding => throw _privateConstructorUsedError;
  EncodingFormat get inputFormat => throw _privateConstructorUsedError;
  EncodingFormat get outputFormat => throw _privateConstructorUsedError;

  /// Serializes this CryptoConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CryptoConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CryptoConfigCopyWith<CryptoConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CryptoConfigCopyWith<$Res> {
  factory $CryptoConfigCopyWith(
          CryptoConfig value, $Res Function(CryptoConfig) then) =
      _$CryptoConfigCopyWithImpl<$Res, CryptoConfig>;
  @useResult
  $Res call(
      {String key,
      String iv,
      AesMode mode,
      AesKeySize keySize,
      AesPadding padding,
      EncodingFormat inputFormat,
      EncodingFormat outputFormat});
}

/// @nodoc
class _$CryptoConfigCopyWithImpl<$Res, $Val extends CryptoConfig>
    implements $CryptoConfigCopyWith<$Res> {
  _$CryptoConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CryptoConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? iv = null,
    Object? mode = null,
    Object? keySize = null,
    Object? padding = null,
    Object? inputFormat = null,
    Object? outputFormat = null,
  }) {
    return _then(_value.copyWith(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      iv: null == iv
          ? _value.iv
          : iv // ignore: cast_nullable_to_non_nullable
              as String,
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as AesMode,
      keySize: null == keySize
          ? _value.keySize
          : keySize // ignore: cast_nullable_to_non_nullable
              as AesKeySize,
      padding: null == padding
          ? _value.padding
          : padding // ignore: cast_nullable_to_non_nullable
              as AesPadding,
      inputFormat: null == inputFormat
          ? _value.inputFormat
          : inputFormat // ignore: cast_nullable_to_non_nullable
              as EncodingFormat,
      outputFormat: null == outputFormat
          ? _value.outputFormat
          : outputFormat // ignore: cast_nullable_to_non_nullable
              as EncodingFormat,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CryptoConfigImplCopyWith<$Res>
    implements $CryptoConfigCopyWith<$Res> {
  factory _$$CryptoConfigImplCopyWith(
          _$CryptoConfigImpl value, $Res Function(_$CryptoConfigImpl) then) =
      __$$CryptoConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String key,
      String iv,
      AesMode mode,
      AesKeySize keySize,
      AesPadding padding,
      EncodingFormat inputFormat,
      EncodingFormat outputFormat});
}

/// @nodoc
class __$$CryptoConfigImplCopyWithImpl<$Res>
    extends _$CryptoConfigCopyWithImpl<$Res, _$CryptoConfigImpl>
    implements _$$CryptoConfigImplCopyWith<$Res> {
  __$$CryptoConfigImplCopyWithImpl(
      _$CryptoConfigImpl _value, $Res Function(_$CryptoConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of CryptoConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? iv = null,
    Object? mode = null,
    Object? keySize = null,
    Object? padding = null,
    Object? inputFormat = null,
    Object? outputFormat = null,
  }) {
    return _then(_$CryptoConfigImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      iv: null == iv
          ? _value.iv
          : iv // ignore: cast_nullable_to_non_nullable
              as String,
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as AesMode,
      keySize: null == keySize
          ? _value.keySize
          : keySize // ignore: cast_nullable_to_non_nullable
              as AesKeySize,
      padding: null == padding
          ? _value.padding
          : padding // ignore: cast_nullable_to_non_nullable
              as AesPadding,
      inputFormat: null == inputFormat
          ? _value.inputFormat
          : inputFormat // ignore: cast_nullable_to_non_nullable
              as EncodingFormat,
      outputFormat: null == outputFormat
          ? _value.outputFormat
          : outputFormat // ignore: cast_nullable_to_non_nullable
              as EncodingFormat,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CryptoConfigImpl implements _CryptoConfig {
  const _$CryptoConfigImpl(
      {this.key = '',
      this.iv = '',
      this.mode = AesMode.ecb,
      this.keySize = AesKeySize.aes128,
      this.padding = AesPadding.pkcs7,
      this.inputFormat = EncodingFormat.base64,
      this.outputFormat = EncodingFormat.base64});

  factory _$CryptoConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$CryptoConfigImplFromJson(json);

  @override
  @JsonKey()
  final String key;
  @override
  @JsonKey()
  final String iv;
  @override
  @JsonKey()
  final AesMode mode;
  @override
  @JsonKey()
  final AesKeySize keySize;
  @override
  @JsonKey()
  final AesPadding padding;
  @override
  @JsonKey()
  final EncodingFormat inputFormat;
  @override
  @JsonKey()
  final EncodingFormat outputFormat;

  @override
  String toString() {
    return 'CryptoConfig(key: $key, iv: $iv, mode: $mode, keySize: $keySize, padding: $padding, inputFormat: $inputFormat, outputFormat: $outputFormat)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CryptoConfigImpl &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.iv, iv) || other.iv == iv) &&
            (identical(other.mode, mode) || other.mode == mode) &&
            (identical(other.keySize, keySize) || other.keySize == keySize) &&
            (identical(other.padding, padding) || other.padding == padding) &&
            (identical(other.inputFormat, inputFormat) ||
                other.inputFormat == inputFormat) &&
            (identical(other.outputFormat, outputFormat) ||
                other.outputFormat == outputFormat));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, key, iv, mode, keySize, padding, inputFormat, outputFormat);

  /// Create a copy of CryptoConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CryptoConfigImplCopyWith<_$CryptoConfigImpl> get copyWith =>
      __$$CryptoConfigImplCopyWithImpl<_$CryptoConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CryptoConfigImplToJson(
      this,
    );
  }
}

abstract class _CryptoConfig implements CryptoConfig {
  const factory _CryptoConfig(
      {final String key,
      final String iv,
      final AesMode mode,
      final AesKeySize keySize,
      final AesPadding padding,
      final EncodingFormat inputFormat,
      final EncodingFormat outputFormat}) = _$CryptoConfigImpl;

  factory _CryptoConfig.fromJson(Map<String, dynamic> json) =
      _$CryptoConfigImpl.fromJson;

  @override
  String get key;
  @override
  String get iv;
  @override
  AesMode get mode;
  @override
  AesKeySize get keySize;
  @override
  AesPadding get padding;
  @override
  EncodingFormat get inputFormat;
  @override
  EncodingFormat get outputFormat;

  /// Create a copy of CryptoConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CryptoConfigImplCopyWith<_$CryptoConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
