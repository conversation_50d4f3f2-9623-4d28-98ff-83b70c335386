import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart';
import 'package:convert/convert.dart';

import '../../domain/models/crypto_config.dart';
import '../../domain/models/crypto_result.dart';
import '../../domain/services/aes_crypto_service.dart';
import '../../../../../shared/utils/app_logger.dart';

/// AES加密服务实现
class AesCryptoServiceImpl implements AesCryptoService {
  @override
  Future<CryptoResult> encrypt(String plaintext, CryptoConfig config) async {
    try {
      AppLogger.info('开始加密操作', {
        'plaintext_length': plaintext.length,
        'config': {
          'mode': config.mode.name,
          'keySize': config.keySize.bits,
          'padding': config.padding.name,
          'inputFormat': config.inputFormat.name,
          'outputFormat': config.outputFormat.name,
          'key_length': config.key.length,
          'iv_length': config.iv.length,
        }
      });

      if (!validateConfig(config)) {
        AppLogger.warning('加密失败：配置参数无效');
        return const CryptoResult(
          success: false,
          error: '配置参数无效',
          operation: CryptoOperation.encrypt,
        );
      }

      // 处理密钥
      final processedKey = processKey(config.key, config.keySize);
      final key = Key.fromBase64(base64.encode(utf8.encode(processedKey)));

      // 创建加密器
      final encrypter = _createEncrypter(config.mode, key);
      
      // 处理IV
      IV? iv;
      if (_needsIV(config.mode)) {
        if (config.iv.isEmpty) {
          return const CryptoResult(
            success: false,
            error: '该模式需要提供初始向量(IV)',
            operation: CryptoOperation.encrypt,
          );
        }
        iv = IV.fromBase64(base64.encode(utf8.encode(config.iv)));
      }

      // 执行加密
      final encrypted = encrypter.encrypt(plaintext, iv: iv);
      
      // 根据输出格式转换结果
      String result;
      switch (config.outputFormat) {
        case EncodingFormat.base64:
          result = encrypted.base64;
          break;
        case EncodingFormat.hex:
          result = hex.encode(encrypted.bytes);
          break;
        case EncodingFormat.utf8:
          result = utf8.decode(encrypted.bytes);
          break;
      }

      AppLogger.info('加密成功', {
        'result_length': result.length,
        'output_format': config.outputFormat.name,
      });

      return CryptoResult(
        success: true,
        data: result,
        operation: CryptoOperation.encrypt,
      );
    } catch (e, stackTrace) {
      AppLogger.error('加密失败', e, stackTrace);
      return CryptoResult(
        success: false,
        error: '加密失败: ${e.toString()}',
        operation: CryptoOperation.encrypt,
      );
    }
  }

  @override
  Future<CryptoResult> decrypt(String ciphertext, CryptoConfig config) async {
    try {
      if (!validateConfig(config)) {
        return const CryptoResult(
          success: false,
          error: '配置参数无效',
          operation: CryptoOperation.decrypt,
        );
      }

      // 处理密钥
      final processedKey = processKey(config.key, config.keySize);
      final key = Key.fromBase64(base64.encode(utf8.encode(processedKey)));

      // 创建加密器
      final encrypter = _createEncrypter(config.mode, key);
      
      // 处理IV
      IV? iv;
      if (_needsIV(config.mode)) {
        if (config.iv.isEmpty) {
          return const CryptoResult(
            success: false,
            error: '该模式需要提供初始向量(IV)',
            operation: CryptoOperation.decrypt,
          );
        }
        iv = IV.fromBase64(base64.encode(utf8.encode(config.iv)));
      }

      // 根据输入格式解析密文
      Encrypted encrypted;
      switch (config.inputFormat) {
        case EncodingFormat.base64:
          encrypted = Encrypted.fromBase64(ciphertext);
          break;
        case EncodingFormat.hex:
          encrypted = Encrypted(Uint8List.fromList(hex.decode(ciphertext)));
          break;
        case EncodingFormat.utf8:
          encrypted = Encrypted(utf8.encode(ciphertext));
          break;
      }

      // 执行解密
      final decrypted = encrypter.decrypt(encrypted, iv: iv);

      return CryptoResult(
        success: true,
        data: decrypted,
        operation: CryptoOperation.decrypt,
      );
    } catch (e) {
      return CryptoResult(
        success: false,
        error: '解密失败: ${e.toString()}',
        operation: CryptoOperation.decrypt,
      );
    }
  }

  @override
  Future<CryptoResult> autoProcess(String input, CryptoConfig config) async {
    if (input.isEmpty) {
      return const CryptoResult(
        success: false,
        error: '输入内容不能为空',
      );
    }

    // 简单的自动判断逻辑：
    // 如果输入看起来像Base64或Hex编码，尝试解密
    // 否则进行加密
    if (_looksLikeEncoded(input, config.inputFormat)) {
      return await decrypt(input, config);
    } else {
      return await encrypt(input, config);
    }
  }

  @override
  bool validateConfig(CryptoConfig config) {
    // 检查需要IV的模式是否提供了IV
    if (_needsIV(config.mode) && config.iv.isEmpty) {
      return false;
    }

    return true;
  }

  @override
  String generateRandomIV(AesMode mode) {
    if (!_needsIV(mode)) {
      return '';
    }

    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return String.fromCharCodes(bytes);
  }

  @override
  String processKey(String key, AesKeySize keySize) {
    final keyBytes = utf8.encode(key);
    final targetLength = keySize.bytes;

    if (keyBytes.length == targetLength) {
      return key;
    } else if (keyBytes.length < targetLength) {
      // 使用null字节填充
      final padding = List<int>.filled(targetLength - keyBytes.length, 0);
      return utf8.decode([...keyBytes, ...padding]);
    } else {
      // 截断到目标长度
      return utf8.decode(keyBytes.take(targetLength).toList());
    }
  }

  /// 创建加密器
  Encrypter _createEncrypter(AesMode mode, Key key) {
    switch (mode) {
      case AesMode.ecb:
        return Encrypter(AES(key, mode: AESMode.ecb));
      case AesMode.cbc:
        return Encrypter(AES(key, mode: AESMode.cbc));
      case AesMode.cfb:
        return Encrypter(AES(key, mode: AESMode.cfb64));
      case AesMode.ofb:
        return Encrypter(AES(key, mode: AESMode.ofb64));
      case AesMode.gcm:
        return Encrypter(AES(key, mode: AESMode.gcm));
    }
  }

  /// 检查模式是否需要IV
  bool _needsIV(AesMode mode) {
    return mode != AesMode.ecb;
  }

  /// 检查输入是否看起来像编码后的数据
  bool _looksLikeEncoded(String input, EncodingFormat format) {
    switch (format) {
      case EncodingFormat.base64:
        return _isValidBase64(input);
      case EncodingFormat.hex:
        return _isValidHex(input);
      case EncodingFormat.utf8:
        return false; // UTF-8格式无法简单判断
    }
  }

  /// 检查是否为有效的Base64
  bool _isValidBase64(String input) {
    try {
      base64.decode(input);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 检查是否为有效的Hex
  bool _isValidHex(String input) {
    try {
      hex.decode(input);
      return true;
    } catch (e) {
      return false;
    }
  }
}
