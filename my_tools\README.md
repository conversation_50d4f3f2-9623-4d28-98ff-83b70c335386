# 我的工具箱 (My Tools)

多工具集合应用 - 实用工具的集合

## 项目简介

这是一个基于Flutter开发的多工具集合应用，采用模块化设计，支持快速添加新工具。目前已实现AES加密工具，未来将添加更多实用工具。

## 技术架构

### 核心技术栈
- **Flutter**: 跨平台UI框架
- **Riverpod**: 状态管理
- **Go Router**: 声明式路由
- **RxDart**: 响应式编程
- **Freezed**: 数据类生成
- **Encrypt**: 高级加密库

### 架构设计
采用Clean Architecture原则，分为以下层次：
- **Presentation Layer**: UI组件和状态管理
- **Domain Layer**: 业务逻辑和模型
- **Data Layer**: 数据服务实现

## 项目结构

```
lib/
├── app/                    # 应用级配置
│   └── router/            # 路由配置
├── features/              # 功能模块
│   ├── home/             # 首页
│   └── tools/            # 工具集合
│       ├── tool_registry/ # 工具注册机制
│       └── aes_crypto/   # AES加密工具
│           ├── data/     # 数据层
│           ├── domain/   # 领域层
│           └── presentation/ # 表现层
└── shared/               # 共享组件
    ├── themes/          # 主题配置
    └── widgets/         # 通用组件
```

## 已实现功能

### AES加密工具
- ✅ 支持多种加密模式 (ECB, CBC, CFB, OFB, GCM)
- ✅ 支持多种密钥长度 (128, 192, 256位)
- ✅ 支持多种填充方式 (PKCS7, PKCS5, NoPadding)
- ✅ 支持多种编码格式 (Base64, Hex, UTF-8)
- ✅ 双向自动处理 (输入明文自动加密，输入密文自动解密)
- ✅ 便捷操作 (复制、粘贴、清空、随机IV生成)
- ✅ 极简UI设计 (灰白色调，无渐变，无动画)

## 快速开始

### 环境要求
- Flutter SDK >= 3.8.1
- Dart SDK >= 3.8.1

### 安装依赖
```bash
flutter pub get
```

### 代码生成
```bash
dart run build_runner build
```

### 运行应用
```bash
flutter run
```

### 运行测试
```bash
flutter test
```

## 添加新工具

1. 在 `lib/features/tools/` 下创建新工具目录
2. 按照Clean Architecture结构组织代码
3. 在 `ToolRegistry.initializeDefaultTools()` 中注册新工具
4. 添加路由配置

## 设计原则

- **极简风格**: 采用灰白色调，避免渐变和过多动画
- **模块化**: 每个工具独立开发，便于维护和扩展
- **响应式**: 使用RxDart实现响应式编程
- **类型安全**: 使用Freezed生成不可变数据类
- **测试友好**: 依赖注入和Provider模式便于单元测试
