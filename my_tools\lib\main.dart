import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'app/router/app_router.dart';
import 'features/tools/tool_registry/registry.dart';
import 'features/tools/js_executor/data/database/objectbox_manager.dart';
import 'shared/themes/app_theme.dart';
import 'shared/utils/app_logger.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // 初始化ObjectBox数据库
    await ObjectBoxManager.instance.initialize();
    AppLogger.info('应用初始化成功');

    // 初始化工具注册表
    ToolRegistry.initializeDefaultTools();

    runApp(
      const ProviderScope(
        child: MyToolsApp(),
      ),
    );
  } catch (e, stackTrace) {
    AppLogger.error('应用初始化失败', e, stackTrace);
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('应用初始化失败'),
                const SizedBox(height: 8),
                Text(e.toString()),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 应用主入口
class MyToolsApp extends ConsumerWidget {
  const MyToolsApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);

    return MaterialApp.router(
      title: '我的工具箱',
      theme: AppTheme.lightTheme,
      routerConfig: router,
      debugShowCheckedModeBanner: false,
    );
  }
}
