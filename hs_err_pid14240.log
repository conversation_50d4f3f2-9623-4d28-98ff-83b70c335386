#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1069856 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:389), pid=14240, tid=0x000000000000264c
#
# JRE version: Java(TM) SE Runtime Environment (8.0_202-b08) (build 1.8.0_202-b08)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.202-b08 mixed mode windows-amd64 compressed oops)
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#

---------------  T H R E A D  ---------------

Current thread (0x0000000039347000):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=9804, stack(0x000000003b030000,0x000000003b130000)]

Stack: [0x000000003b030000,0x000000003b130000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)


Current CompileTask:
C2:  28369 6309       4       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ArchiveHandler::processEntry (225 bytes)


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x000000003d03f800 JavaThread "jar transforms Thread 7" [_thread_blocked, id=59964, stack(0x0000000046310000,0x0000000046410000)]
  0x000000003d03e000 JavaThread "jar transforms Thread 6" [_thread_blocked, id=35428, stack(0x0000000047910000,0x0000000047a10000)]
  0x000000003d044800 JavaThread "Cache worker for file content cache (D:\Dev\Env\repo\caches\8.12\fileContent)" [_thread_blocked, id=66200, stack(0x0000000046210000,0x0000000046310000)]
  0x000000003d036800 JavaThread "Cache worker for checksums cache (D:\Dev\OtherProjects\aes\my_tools\android\.gradle\8.12\checksums)" [_thread_blocked, id=16152, stack(0x0000000046110000,0x0000000046210000)]
  0x000000003d03d000 JavaThread "jar transforms Thread 5" [_thread_blocked, id=56592, stack(0x0000000045e10000,0x0000000045f10000)]
  0x000000003d03b800 JavaThread "jar transforms Thread 4" [_thread_blocked, id=27052, stack(0x0000000045910000,0x0000000045a10000)]
  0x000000003d03b000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=25184, stack(0x0000000044fd0000,0x00000000450d0000)]
  0x000000003d039800 JavaThread "jar transforms Thread 2" [_thread_blocked, id=35632, stack(0x0000000044ed0000,0x0000000044fd0000)]
  0x000000003d037000 JavaThread "jar transforms" [_thread_blocked, id=30840, stack(0x0000000044dd0000,0x0000000044ed0000)]
  0x000000003d038000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=37300, stack(0x0000000043670000,0x0000000043770000)]
  0x000000003d03c800 JavaThread "File lock release action executor" [_thread_blocked, id=36128, stack(0x0000000043570000,0x0000000043670000)]
  0x000000003d038800 JavaThread "File watcher server" daemon [_thread_in_native, id=40564, stack(0x0000000043470000,0x0000000043570000)]
  0x00000000393a6000 JavaThread "Cache worker for Build Output Cleanup Cache (D:\Dev\OtherProjects\aes\my_tools\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=33600, stack(0x0000000043170000,0x0000000043270000)]
  0x00000000393a7000 JavaThread "Cache worker for file hash cache (D:\Dev\OtherProjects\aes\my_tools\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=37388, stack(0x0000000043070000,0x0000000043170000)]
  0x00000000393a4000 JavaThread "Problems report writer" [_thread_blocked, id=32184, stack(0x0000000041d70000,0x0000000041e70000)]
  0x00000000393a3000 JavaThread "Cache worker for file hash cache (D:\Dev\Env\repo\caches\8.12\fileHashes)" [_thread_blocked, id=42148, stack(0x0000000040c90000,0x0000000040d90000)]
  0x00000000393aa000 JavaThread "File lock request listener" [_thread_in_native, id=6888, stack(0x0000000040b90000,0x0000000040c90000)]
  0x00000000393a8800 JavaThread "Cache worker for journal cache (D:\Dev\Env\repo\caches\journal-1)" [_thread_blocked, id=38096, stack(0x0000000040a90000,0x0000000040b90000)]
  0x000000003e0fd000 JavaThread "Daemon client event forwarder" [_thread_blocked, id=53620, stack(0x000000003f7c0000,0x000000003f8c0000)]
  0x000000003e207800 JavaThread "Stdin handler" [_thread_blocked, id=54340, stack(0x000000003f4c0000,0x000000003f5c0000)]
  0x000000003e208800 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:40270 to /127.0.0.1:40271" [_thread_blocked, id=60808, stack(0x000000003f3c0000,0x000000003f4c0000)]
  0x000000003e142000 JavaThread "Daemon worker" [_thread_in_Java, id=42944, stack(0x000000003f2c0000,0x000000003f3c0000)]
  0x000000003e0a2000 JavaThread "Cancel handler" [_thread_blocked, id=7244, stack(0x000000003f1c0000,0x000000003f2c0000)]
  0x000000003e036000 JavaThread "Handler for socket connection from /127.0.0.1:40270 to /127.0.0.1:40271" [_thread_in_native, id=65892, stack(0x000000003f0c0000,0x000000003f1c0000)]
  0x000000003e034000 JavaThread "Daemon" [_thread_blocked, id=41100, stack(0x000000003efc0000,0x000000003f0c0000)]
  0x000000003e031800 JavaThread "Daemon periodic checks" [_thread_blocked, id=52928, stack(0x000000003eec0000,0x000000003efc0000)]
  0x000000003dff0800 JavaThread "Incoming local TCP Connector on port 40270" [_thread_in_native, id=60116, stack(0x000000003ed10000,0x000000003ee10000)]
  0x000000003c41e000 JavaThread "Daemon health stats" [_thread_blocked, id=53796, stack(0x000000003e6d0000,0x000000003e7d0000)]
  0x000000003942b000 JavaThread "Service Thread" daemon [_thread_blocked, id=62716, stack(0x000000003bb30000,0x000000003bc30000)]
  0x00000000393a4800 JavaThread "C1 CompilerThread11" daemon [_thread_blocked, id=56576, stack(0x000000003ba30000,0x000000003bb30000)]
  0x00000000393a7800 JavaThread "C1 CompilerThread10" daemon [_thread_blocked, id=62112, stack(0x000000003b930000,0x000000003ba30000)]
  0x0000000039396800 JavaThread "C1 CompilerThread9" daemon [_thread_blocked, id=65256, stack(0x000000003b730000,0x000000003b830000)]
  0x0000000039365800 JavaThread "C1 CompilerThread8" daemon [_thread_blocked, id=61072, stack(0x000000003b630000,0x000000003b730000)]
  0x000000003935b000 JavaThread "C2 CompilerThread7" daemon [_thread_blocked, id=49920, stack(0x000000003b530000,0x000000003b630000)]
  0x0000000039359000 JavaThread "C2 CompilerThread6" daemon [_thread_blocked, id=44544, stack(0x000000003b430000,0x000000003b530000)]
  0x0000000039358800 JavaThread "C2 CompilerThread5" daemon [_thread_blocked, id=30144, stack(0x000000003b330000,0x000000003b430000)]
  0x000000003934a800 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=60776, stack(0x000000003b230000,0x000000003b330000)]
  0x000000003934a000 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=46040, stack(0x000000003b130000,0x000000003b230000)]
=>0x0000000039347000 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=9804, stack(0x000000003b030000,0x000000003b130000)]
  0x0000000039345800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=5776, stack(0x000000003af30000,0x000000003b030000)]
  0x0000000039343800 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=33700, stack(0x000000003ae30000,0x000000003af30000)]
  0x00000000392d5800 JavaThread "Attach Listener" daemon [_thread_blocked, id=21360, stack(0x000000003ad30000,0x000000003ae30000)]
  0x00000000392d3000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=5372, stack(0x000000003ac30000,0x000000003ad30000)]
  0x00000000377f3000 JavaThread "Finalizer" daemon [_thread_blocked, id=40380, stack(0x000000003aac0000,0x000000003abc0000)]
  0x0000000039256000 JavaThread "Reference Handler" daemon [_thread_blocked, id=54620, stack(0x000000003a9c0000,0x000000003aac0000)]
  0x0000000002b4e800 JavaThread "main" [_thread_blocked, id=21976, stack(0x00000000029b0000,0x0000000002ab0000)]

Other Threads:
  0x0000000039235800 VMThread [stack: 0x000000003a8c0000,0x000000003a9c0000] [id=52744]
  0x0000000039404800 WatcherThread [stack: 0x000000003bc30000,0x000000003bd30000] [id=25340]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

OutOfMemory and StackOverflow Exception counts:
OutOfMemoryError java_heap_errors=1

heap address: 0x00000005c0000000, size: 8192 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 422912K, used 248194K [0x0000000715580000, 0x0000000735d00000, 0x00000007c0000000)
  eden space 406016K, 61% used [0x0000000715580000,0x00000007247e0848,0x000000072e200000)
  from space 16896K, 0% used [0x000000072f200000,0x000000072f200000,0x0000000730280000)
  to   space 16384K, 0% used [0x000000072e200000,0x000000072e200000,0x000000072f200000)
 ParOldGen       total 177664K, used 29474K [0x00000005c0000000, 0x00000005cad80000, 0x0000000715580000)
  object space 177664K, 16% used [0x00000005c0000000,0x00000005c1cc8ae8,0x00000005cad80000)
 Metaspace       used 67193K, capacity 70682K, committed 70936K, reserved 1110016K
  class space    used 9538K, capacity 10225K, committed 10280K, reserved 1048576K

Card table byte_map: [0x00000000235a0000,0x00000000245b0000] byte_map_base: 0x00000000207a0000

Marking Bits: (ParMarkBitMap*) 0x00000000601e5ce0
 Begin Bits: [0x0000000025d60000, 0x000000002dd60000)
 End Bits:   [0x000000002dd60000, 0x0000000035d60000)

Polling page: 0x00000000010a0000

CodeCache: size=524288Kb used=23112Kb max_used=23221Kb free=501175Kb
 bounds [0x0000000002da0000, 0x0000000004470000, 0x0000000022da0000]
 total_blobs=6620 nmethods=5768 adapters=761
 compilation: enabled

Compilation events (10 events):
Event: 28.353 Thread 0x00000000393a7800 nmethod 6373 0x00000000036eb9d0 code [0x00000000036ebb80, 0x00000000036ec1d8]
Event: 28.353 Thread 0x00000000393a7800 6379       3       org.gradle.internal.problems.DefaultProblemLocationAnalyzer::tryGetLocation (51 bytes)
Event: 28.353 Thread 0x00000000393a4800 nmethod 6378 0x00000000036eb710 code [0x00000000036eb860, 0x00000000036eb970]
Event: 28.353 Thread 0x0000000039365800 nmethod 6377 0x00000000036eb450 code [0x00000000036eb5a0, 0x00000000036eb6b0]
Event: 28.354 Thread 0x0000000039396800 nmethod 6375 0x00000000036e7d10 code [0x00000000036e7f40, 0x00000000036e9058]
Event: 28.354 Thread 0x00000000393a7800 nmethod 6379 0x00000000036eab50 code [0x00000000036ead00, 0x00000000036eb2b8]
Event: 28.356 Thread 0x00000000393a4800 6380       3       org.codehaus.groovy.runtime.StackTraceUtils::isApplicationClass (84 bytes)
Event: 28.357 Thread 0x00000000393a4800 nmethod 6380 0x00000000036e6b90 code [0x00000000036e6da0, 0x00000000036e78b8]
Event: 28.363 Thread 0x0000000039365800 6381       3       java.lang.invoke.DirectMethodHandle::<init> (8 bytes)
Event: 28.363 Thread 0x0000000039365800 nmethod 6381 0x00000000036ea7d0 code [0x00000000036ea940, 0x00000000036eaac8]

GC Heap History (10 events):
Event: 21.529 GC heap before
{Heap before GC invocations=13 (full 2):
 PSYoungGen      total 317440K, used 317419K [0x0000000715580000, 0x000000072b480000, 0x00000007c0000000)
  eden space 303616K, 100% used [0x0000000715580000,0x0000000727e00000,0x0000000727e00000)
  from space 13824K, 99% used [0x000000072a700000,0x000000072b47ade8,0x000000072b480000)
  to   space 16384K, 0% used [0x0000000729480000,0x0000000729480000,0x000000072a480000)
 ParOldGen       total 123392K, used 17869K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 14% used [0x00000005c0000000,0x00000005c1173710,0x00000005c7880000)
 Metaspace       used 47661K, capacity 50696K, committed 50984K, reserved 1093632K
  class space    used 6601K, capacity 7170K, committed 7208K, reserved 1048576K
Event: 21.551 GC heap after
Heap after GC invocations=13 (full 2):
 PSYoungGen      total 343040K, used 1816K [0x0000000715580000, 0x0000000730280000, 0x00000007c0000000)
  eden space 326656K, 0% used [0x0000000715580000,0x0000000715580000,0x0000000729480000)
  from space 16384K, 11% used [0x0000000729480000,0x00000007296461a8,0x000000072a480000)
  to   space 16896K, 0% used [0x000000072f200000,0x000000072f200000,0x0000000730280000)
 ParOldGen       total 123392K, used 26163K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 21% used [0x00000005c0000000,0x00000005c198cdc8,0x00000005c7880000)
 Metaspace       used 47661K, capacity 50696K, committed 50984K, reserved 1093632K
  class space    used 6601K, capacity 7170K, committed 7208K, reserved 1048576K
}
Event: 23.206 GC heap before
{Heap before GC invocations=14 (full 2):
 PSYoungGen      total 343040K, used 328472K [0x0000000715580000, 0x0000000730280000, 0x00000007c0000000)
  eden space 326656K, 100% used [0x0000000715580000,0x0000000729480000,0x0000000729480000)
  from space 16384K, 11% used [0x0000000729480000,0x00000007296461a8,0x000000072a480000)
  to   space 16896K, 0% used [0x000000072f200000,0x000000072f200000,0x0000000730280000)
 ParOldGen       total 123392K, used 26163K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 21% used [0x00000005c0000000,0x00000005c198cdc8,0x00000005c7880000)
 Metaspace       used 49615K, capacity 52678K, committed 53032K, reserved 1095680K
  class space    used 6827K, capacity 7399K, committed 7464K, reserved 1048576K
Event: 23.218 GC heap after
Heap after GC invocations=14 (full 2):
 PSYoungGen      total 422400K, used 9931K [0x0000000715580000, 0x0000000731600000, 0x00000007c0000000)
  eden space 405504K, 0% used [0x0000000715580000,0x0000000715580000,0x000000072e180000)
  from space 16896K, 58% used [0x000000072f200000,0x000000072fbb2fb8,0x0000000730280000)
  to   space 16896K, 0% used [0x000000072e180000,0x000000072e180000,0x000000072f200000)
 ParOldGen       total 123392K, used 26507K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 21% used [0x00000005c0000000,0x00000005c19e2dc8,0x00000005c7880000)
 Metaspace       used 49615K, capacity 52678K, committed 53032K, reserved 1095680K
  class space    used 6827K, capacity 7399K, committed 7464K, reserved 1048576K
}
Event: 24.768 GC heap before
{Heap before GC invocations=15 (full 2):
 PSYoungGen      total 422400K, used 415435K [0x0000000715580000, 0x0000000731600000, 0x00000007c0000000)
  eden space 405504K, 100% used [0x0000000715580000,0x000000072e180000,0x000000072e180000)
  from space 16896K, 58% used [0x000000072f200000,0x000000072fbb2fb8,0x0000000730280000)
  to   space 16896K, 0% used [0x000000072e180000,0x000000072e180000,0x000000072f200000)
 ParOldGen       total 123392K, used 26507K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 21% used [0x00000005c0000000,0x00000005c19e2dc8,0x00000005c7880000)
 Metaspace       used 49704K, capacity 52724K, committed 53032K, reserved 1095680K
  class space    used 6839K, capacity 7408K, committed 7464K, reserved 1048576K
Event: 24.774 GC heap after
Heap after GC invocations=15 (full 2):
 PSYoungGen      total 422400K, used 1763K [0x0000000715580000, 0x0000000731600000, 0x00000007c0000000)
  eden space 405504K, 0% used [0x0000000715580000,0x0000000715580000,0x000000072e180000)
  from space 16896K, 10% used [0x000000072e180000,0x000000072e338df0,0x000000072f200000)
  to   space 16896K, 0% used [0x000000072f200000,0x000000072f200000,0x0000000730280000)
 ParOldGen       total 123392K, used 26779K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 21% used [0x00000005c0000000,0x00000005c1a26dc8,0x00000005c7880000)
 Metaspace       used 49704K, capacity 52724K, committed 53032K, reserved 1095680K
  class space    used 6839K, capacity 7408K, committed 7464K, reserved 1048576K
}
Event: 26.814 GC heap before
{Heap before GC invocations=16 (full 2):
 PSYoungGen      total 422400K, used 195757K [0x0000000715580000, 0x0000000731600000, 0x00000007c0000000)
  eden space 405504K, 47% used [0x0000000715580000,0x00000007212f2918,0x000000072e180000)
  from space 16896K, 10% used [0x000000072e180000,0x000000072e338df0,0x000000072f200000)
  to   space 16896K, 0% used [0x000000072f200000,0x000000072f200000,0x0000000730280000)
 ParOldGen       total 123392K, used 26779K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 21% used [0x00000005c0000000,0x00000005c1a26dc8,0x00000005c7880000)
 Metaspace       used 55975K, capacity 58982K, committed 59160K, reserved 1099776K
  class space    used 8095K, capacity 8663K, committed 8744K, reserved 1048576K
Event: 26.820 GC heap after
Heap after GC invocations=16 (full 2):
 PSYoungGen      total 422912K, used 7287K [0x0000000715580000, 0x0000000735d00000, 0x00000007c0000000)
  eden space 406016K, 0% used [0x0000000715580000,0x0000000715580000,0x000000072e200000)
  from space 16896K, 43% used [0x000000072f200000,0x000000072f91dcf8,0x0000000730280000)
  to   space 16384K, 0% used [0x000000072e200000,0x000000072e200000,0x000000072f200000)
 ParOldGen       total 123392K, used 26913K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 21% used [0x00000005c0000000,0x00000005c1a485e8,0x00000005c7880000)
 Metaspace       used 55975K, capacity 58982K, committed 59160K, reserved 1099776K
  class space    used 8095K, capacity 8663K, committed 8744K, reserved 1048576K
}
Event: 26.820 GC heap before
{Heap before GC invocations=17 (full 3):
 PSYoungGen      total 422912K, used 7287K [0x0000000715580000, 0x0000000735d00000, 0x00000007c0000000)
  eden space 406016K, 0% used [0x0000000715580000,0x0000000715580000,0x000000072e200000)
  from space 16896K, 43% used [0x000000072f200000,0x000000072f91dcf8,0x0000000730280000)
  to   space 16384K, 0% used [0x000000072e200000,0x000000072e200000,0x000000072f200000)
 ParOldGen       total 123392K, used 26913K [0x00000005c0000000, 0x00000005c7880000, 0x0000000715580000)
  object space 123392K, 21% used [0x00000005c0000000,0x00000005c1a485e8,0x00000005c7880000)
 Metaspace       used 55975K, capacity 58982K, committed 59160K, reserved 1099776K
  class space    used 8095K, capacity 8663K, committed 8744K, reserved 1048576K
Event: 26.973 GC heap after
Heap after GC invocations=17 (full 3):
 PSYoungGen      total 422912K, used 0K [0x0000000715580000, 0x0000000735d00000, 0x00000007c0000000)
  eden space 406016K, 0% used [0x0000000715580000,0x0000000715580000,0x000000072e200000)
  from space 16896K, 0% used [0x000000072f200000,0x000000072f200000,0x0000000730280000)
  to   space 16384K, 0% used [0x000000072e200000,0x000000072e200000,0x000000072f200000)
 ParOldGen       total 177664K, used 29474K [0x00000005c0000000, 0x00000005cad80000, 0x0000000715580000)
  object space 177664K, 16% used [0x00000005c0000000,0x00000005c1cc8ae8,0x00000005cad80000)
 Metaspace       used 55943K, capacity 58944K, committed 59160K, reserved 1099776K
  class space    used 8083K, capacity 8649K, committed 8744K, reserved 1048576K
}

Deoptimization events (10 events):
Event: 28.340 Thread 0x000000003e142000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000000003213280 method=com.google.common.collect.ImmutableCollection.toArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 6
Event: 28.340 Thread 0x000000003e0fd000 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000000000443d5bc method=java.util.ComparableTimSort.reverseRange([Ljava/lang/Object;II)V @ 20
Event: 28.341 Thread 0x000000003e0fd000 Uncommon trap: reason=predicate action=maybe_recompile pc=0x00000000033a9034 method=java.util.TimSort.reverseRange([Ljava/lang/Object;II)V @ 8
Event: 28.341 Thread 0x000000003e0fd000 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000000003fa0510 method=java.util.TimSort.binarySort([Ljava/lang/Object;IIILjava/util/Comparator;)V @ 215
Event: 28.341 Thread 0x000000003e0fd000 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000000003fa0644 method=java.util.TimSort.binarySort([Ljava/lang/Object;IIILjava/util/Comparator;)V @ 193
Event: 28.341 Thread 0x000000003e142000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000000329ca00 method=java.util.stream.Sink$ChainedReference.cancellationRequested()Z @ 4
Event: 28.341 Thread 0x000000003e142000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000000329ca00 method=java.util.stream.Sink$ChainedReference.cancellationRequested()Z @ 4
Event: 28.341 Thread 0x000000003e142000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000000329ca00 method=java.util.stream.Sink$ChainedReference.cancellationRequested()Z @ 4
Event: 28.341 Thread 0x000000003e0fd000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000000003fa3c68 method=java.io.ObjectOutputStream$BlockDataOutputStream.writeBytes(Ljava/lang/String;)V @ 22
Event: 28.341 Thread 0x000000003e142000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000000000329ca00 method=java.util.stream.Sink$ChainedReference.cancellationRequested()Z @ 4

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 27.124 Thread 0x000000003e142000 Exception <a 'sun/nio/fs/WindowsException'> (0x000000071748df80) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 27.202 Thread 0x000000003e142000 Exception <a 'sun/nio/fs/WindowsException'> (0x00000007187dd4b8) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 27.354 Thread 0x000000003e142000 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$static$0()J> (0x000000071a987380) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\interpreter\linkResolver.cpp, line 620]
Event: 27.542 Thread 0x000000003e142000 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$static$0(I)[Lorg/jetbrains/kotlin/com/intellij/openapi/editor/event/DocumentListener;> (0x000000071cd2d588) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspo
Event: 27.585 Thread 0x000000003e142000 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$static$0(I)[Lorg/jetbrains/kotlin/com/intellij/psi/PsiAnnotation;> (0x000000071d53ac88) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\inter
Event: 27.770 Thread 0x000000003e142000 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$static$0(I)[Lorg/jetbrains/kotlin/com/intellij/psi/PsiElement;> (0x000000071f0fd0f0) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\interpre
Event: 28.087 Thread 0x000000003e142000 Exception <a 'sun/nio/fs/WindowsException'> (0x0000000721b82040) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 28.326 Thread 0x000000003e142000 Exception <a 'java/lang/OutOfMemoryError'> (0x0000000723a4d208) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\prims\jni.cpp, line 736]
Event: 28.340 Thread 0x000000003e0fd000 Exception <a 'java/lang/NoSuchMethodError': <clinit>> (0x0000000715d0e0d0) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\prims\jni.cpp, line 1614]
Event: 28.341 Thread 0x000000003e0fd000 Exception <a 'java/lang/NoSuchMethodError': <clinit>> (0x0000000715d1f3a0) thrown at [C:\re\workspace\8-2-build-windows-amd64-cygwin\jdk8u202\12323\hotspot\src\share\vm\prims\jni.cpp, line 1614]

Events (10 events):
Event: 28.361 loading class org/gradle/cache/internal/CompositeCleanupAction$ScopedCleanupAction done
Event: 28.362 loading class org/gradle/cache/CleanupFrequency
Event: 28.362 loading class org/gradle/cache/CleanupFrequency done
Event: 28.362 loading class org/gradle/api/provider/Provider
Event: 28.362 loading class org/gradle/api/provider/Provider done
Event: 28.367 loading class org/gradle/internal/build/ExecutionResult
Event: 28.367 loading class org/gradle/internal/build/ExecutionResult done
Event: 28.367 loading class org/gradle/internal/build/DefaultBuildLifecycleController
Event: 28.367 loading class org/gradle/internal/build/DefaultBuildLifecycleController done
Event: 28.368 loading class org/gradle/internal/model/StateTransitionController$State


Dynamic libraries:
0x00007ff7598e0000 - 0x00007ff759917000 	D:\Dev\Env\jdk8\bin\java.exe
0x00007ffe013b0000 - 0x00007ffe015c7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffdfffb0000 - 0x00007ffe00074000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffdfe6d0000 - 0x00007ffdfeaa2000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe010b0000 - 0x00007ffe01161000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffdff340000 - 0x00007ffdff3e7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffdff290000 - 0x00007ffdff338000 	C:\WINDOWS\System32\sechost.dll
0x00007ffdfedc0000 - 0x00007ffdfede8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe00e30000 - 0x00007ffe00f47000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe01180000 - 0x00007ffe01331000 	C:\WINDOWS\System32\USER32.dll
0x00007ffdfe560000 - 0x00007ffdfe586000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe003b0000 - 0x00007ffe003d9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffdfef10000 - 0x00007ffdff033000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffdfedf0000 - 0x00007ffdfee8a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffdfeab0000 - 0x00007ffdfebc1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffde3000000 - 0x00007ffde329b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5697_none_27154c497380d676\COMCTL32.dll
0x00007ffdfff60000 - 0x00007ffdfff91000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000060270000 - 0x0000000060342000 	D:\Dev\Env\jdk8\jre\bin\msvcr100.dll
0x000000005f9c0000 - 0x0000000060265000 	D:\Dev\Env\jdk8\jre\bin\server\jvm.dll
0x00007ffe01360000 - 0x00007ffe01368000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffdea080000 - 0x00007ffdea0b4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffdf6990000 - 0x00007ffdf699a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffdc2920000 - 0x00007ffdc2929000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffe002a0000 - 0x00007ffe00311000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffdec530000 - 0x00007ffdec53f000 	D:\Dev\Env\jdk8\jre\bin\verify.dll
0x00007ffddf4c0000 - 0x00007ffddf4e9000 	D:\Dev\Env\jdk8\jre\bin\java.dll
0x00007ffddf140000 - 0x00007ffddf163000 	D:\Dev\Env\jdk8\jre\bin\instrument.dll
0x00007ffddf520000 - 0x00007ffddf536000 	D:\Dev\Env\jdk8\jre\bin\zip.dll
0x00007ffdff3f0000 - 0x00007ffdffc91000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffdfe590000 - 0x00007ffdfe6cf000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffe00610000 - 0x00007ffe009a2000 	C:\WINDOWS\System32\combase.dll
0x00007ffdfc300000 - 0x00007ffdfcc1a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffdffe50000 - 0x00007ffdfff5b000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe00080000 - 0x00007ffe000e9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffdfe3d0000 - 0x00007ffdfe3fb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffddf4a0000 - 0x00007ffddf4ba000 	D:\Dev\Env\jdk8\jre\bin\net.dll
0x00007ffded500000 - 0x00007ffded6f5000 	C:\WINDOWS\SYSTEM32\urlmon.dll
0x00007ffdecee0000 - 0x00007ffded19e000 	C:\WINDOWS\SYSTEM32\iertutil.dll
0x00007ffdf4a40000 - 0x00007ffdf4a68000 	C:\WINDOWS\SYSTEM32\srvcli.dll
0x00007ffdfce70000 - 0x00007ffdfce7c000 	C:\WINDOWS\SYSTEM32\netutils.dll
0x00007ffdfd900000 - 0x00007ffdfd969000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffddf470000 - 0x00007ffddf482000 	D:\Dev\Env\jdk8\jre\bin\nio.dll
0x00007ffddb7b0000 - 0x00007ffddb7d7000 	D:\Dev\Env\repo\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x000000005f740000 - 0x000000005f7b3000 	D:\Dev\Env\repo\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffdea6f0000 - 0x00007ffdea6fd000 	D:\Dev\Env\jdk8\jre\bin\management.dll
0x00007ffdfdc50000 - 0x00007ffdfdc6b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffdfd3a0000 - 0x00007ffdfd3d7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffdfd9a0000 - 0x00007ffdfd9c8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffdfed40000 - 0x00007ffdfedbb000 	C:\WINDOWS\System32\bcryptprimitives.dll
0x00007ffdfdb00000 - 0x00007ffdfdb0c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffdfce80000 - 0x00007ffdfcead000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffdfffa0000 - 0x00007ffdfffa9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffdf69c0000 - 0x00007ffdf69d9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdf69a0000 - 0x00007ffdf69bf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffdfcef0000 - 0x00007ffdfcfe8000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x000000005f640000 - 0x000000005f6b3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform7559513452761050750dir\gradle-fileevents.dll
0x00007ffdfd550000 - 0x00007ffdfd584000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:D:\Dev\Env\repo\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): D:\Dev\Env\repo\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar;D:\Dev\Env\repo\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=D:\Dev\Env\jdk8
CLASSPATH=.;%JAVA_HOME%\lib\dt.jar;%JAVA_HOME%\lib\tools.jar
PATH=D:\Dev\Env\python\Scripts\;D:\Dev\Env\python\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Google\Chrome\Application\;C:\Program File;C:\Program Files\Git\bin;C:\Users\<USER>\fvm\default\bin;D:\Program Files\Xftp 8\;C:\Program Files\CursorModifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files\Microsoft SQL Server\110\Tools\Binn\;D:\Program Files\swigwin-4.3.0;C:\Users\<USER>\.local\bin;D:\Dev\Env\apache-maven-3.6.2\bin;D:\Dev\Env\uv;D:\Program Files\TortoiseGit\bin;C:\Program Files (x86)\Xshell 8\;C:\Program Files (x86)\Xftp\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\tools\neovim\nvim-win64\bin;C:\Users\<USER>\AppData\Roaming\nvm;node_global;node_global\node_modules\yarn\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Fiddler;C:\msys64\mingw64\bin;D:\Program Files\JetBrains\DataGrip 2023.1\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;D:\Program Files\Neovim\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\GoLand 2024.1.4\bin;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;D:\Java\apache-maven-3.9.6\bin;C:\ProgramData\mingw64\mingw64\bin;C:\Program Files\cursor-id-modifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;%IntelliJ IDEA%;C:\Program Files\ai;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10.0 , 64 bit Build 22621 (10.0.22621.5415)

CPU:total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx

Memory: 4k page, physical 16617752k(787028k free), swap 52866880k(6008k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (25.202-b08) for windows-amd64 JRE (1.8.0_202-b08), built on Dec 15 2018 19:54:30 by "java_re" with MS VC++ 10.0 (VS2010)

time: Thu Jul 31 10:47:49 2025
timezone: 中国标准时间
elapsed time: 28 seconds (0d 0h 0m 28s)

