// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'features/tools/js_executor/domain/models/js_code_snippet.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 553209797949817091),
      name: 'JsCodeSnippetEntity',
      lastPropertyId: const obx_int.IdUid(7, 929447249616817679),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 1306121217765314246),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 4618741906803723760),
            name: 'name',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 309824728779864425),
            name: 'code',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 2796910781636817368),
            name: 'description',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 4934880519341885939),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 8343496295349706753),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 929447249616817679),
            name: 'isEnabled',
            type: 1,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(1, 553209797949817091),
      lastIndexId: const obx_int.IdUid(0, 0),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    JsCodeSnippetEntity: obx_int.EntityDefinition<JsCodeSnippetEntity>(
        model: _entities[0],
        toOneRelations: (JsCodeSnippetEntity object) => [],
        toManyRelations: (JsCodeSnippetEntity object) => {},
        getId: (JsCodeSnippetEntity object) => object.id,
        setId: (JsCodeSnippetEntity object, int id) {
          object.id = id;
        },
        objectToFB: (JsCodeSnippetEntity object, fb.Builder fbb) {
          final nameOffset = fbb.writeString(object.name);
          final codeOffset = fbb.writeString(object.code);
          final descriptionOffset = fbb.writeString(object.description);
          fbb.startTable(8);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, nameOffset);
          fbb.addOffset(2, codeOffset);
          fbb.addOffset(3, descriptionOffset);
          fbb.addInt64(4, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(5, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addBool(6, object.isEnabled);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 12);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 14);
          final object = JsCodeSnippetEntity()
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0)
            ..name = const fb.StringReader(asciiOptimization: true)
                .vTableGet(buffer, rootOffset, 6, '')
            ..code = const fb.StringReader(asciiOptimization: true)
                .vTableGet(buffer, rootOffset, 8, '')
            ..description = const fb.StringReader(asciiOptimization: true)
                .vTableGet(buffer, rootOffset, 10, '')
            ..createdAt = createdAtValue == null
                ? null
                : DateTime.fromMillisecondsSinceEpoch(createdAtValue)
            ..updatedAt = updatedAtValue == null
                ? null
                : DateTime.fromMillisecondsSinceEpoch(updatedAtValue)
            ..isEnabled =
                const fb.BoolReader().vTableGet(buffer, rootOffset, 16, false);

          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [JsCodeSnippetEntity] entity fields to define ObjectBox queries.
class JsCodeSnippetEntity_ {
  /// See [JsCodeSnippetEntity.id].
  static final id =
      obx.QueryIntegerProperty<JsCodeSnippetEntity>(_entities[0].properties[0]);

  /// See [JsCodeSnippetEntity.name].
  static final name =
      obx.QueryStringProperty<JsCodeSnippetEntity>(_entities[0].properties[1]);

  /// See [JsCodeSnippetEntity.code].
  static final code =
      obx.QueryStringProperty<JsCodeSnippetEntity>(_entities[0].properties[2]);

  /// See [JsCodeSnippetEntity.description].
  static final description =
      obx.QueryStringProperty<JsCodeSnippetEntity>(_entities[0].properties[3]);

  /// See [JsCodeSnippetEntity.createdAt].
  static final createdAt =
      obx.QueryDateProperty<JsCodeSnippetEntity>(_entities[0].properties[4]);

  /// See [JsCodeSnippetEntity.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<JsCodeSnippetEntity>(_entities[0].properties[5]);

  /// See [JsCodeSnippetEntity.isEnabled].
  static final isEnabled =
      obx.QueryBooleanProperty<JsCodeSnippetEntity>(_entities[0].properties[6]);
}
