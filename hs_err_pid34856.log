#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 353056 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=34856, tid=53556
#
# JRE version: Java(TM) SE Runtime Environment (21.0.7+8) (build 21.0.7+8-LTS-245)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.7+8-LTS-245, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=true -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx10G -Xms2G -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\02f6bb0736726e084caf6170a8d996d1\redhat.java\ss_ws --pipe=\\.\pipe\lsp-01466d58123764f1f382a19fb568b4e3-sock

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Jul 31 10:46:25 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 65.354524 seconds (0d 0h 1m 5s)

---------------  T H R E A D  ---------------

Current thread (0x000002967ecaf080):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=53556, stack(0x000000ad30200000,0x000000ad30300000) (1024K)]


Current CompileTask:
C2:  65354 4607       4       lombok.patcher.PatchScript$MethodPatcher::visitMethod (158 bytes)

Stack: [0x000000ad30200000,0x000000ad30300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cae19]
V  [jvm.dll+0x855741]
V  [jvm.dll+0x857c6e]
V  [jvm.dll+0x858343]
V  [jvm.dll+0x27e0b6]
V  [jvm.dll+0xc4ecd]
V  [jvm.dll+0xc5403]
V  [jvm.dll+0x2f145d]
V  [jvm.dll+0x5f5a8a]
V  [jvm.dll+0x24fbc2]
V  [jvm.dll+0x24ff7f]
V  [jvm.dll+0x248a9c]
V  [jvm.dll+0x246004]
V  [jvm.dll+0x1c6efe]
V  [jvm.dll+0x255819]
V  [jvm.dll+0x253daa]
V  [jvm.dll+0x3eebb6]
V  [jvm.dll+0x7ffa1b]
V  [jvm.dll+0x6c94fd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000029676f33310, length=44, elements={
0x000002967ca05830, 0x000002962ddd67b0, 0x000002962ddda0c0, 0x000002962ddde350,
0x000002967eca2100, 0x000002967eca4610, 0x000002967eca6c80, 0x000002967ecaf080,
0x000002967ecb8c70, 0x00000296703cdc50, 0x000002967607e0a0, 0x0000029676510970,
0x00000296764edda0, 0x000002967649ca60, 0x00000296762d47b0, 0x0000029676870e50,
0x0000029676ee1480, 0x0000029676958c40, 0x000002967031cfa0, 0x000002967031f700,
0x000002967031dcc0, 0x000002967031d630, 0x000002967031e350, 0x000002967031e9e0,
0x000002967031f070, 0x000002967031c280, 0x000002967031c910, 0x000002967755be30,
0x000002967755c4c0, 0x0000029677561380, 0x000002967755e590, 0x0000029677561a10,
0x000002967755ec20, 0x000002967755f2b0, 0x0000029677560660, 0x000002967755df00,
0x000002967755cb50, 0x0000029677560cf0, 0x00000296775620a0, 0x000002967755b110,
0x0000029676657a70, 0x0000029676c44530, 0x0000029676a11330, 0x0000029677b65870
}

Java Threads: ( => current thread )
  0x000002967ca05830 JavaThread "main"                              [_thread_blocked, id=63268, stack(0x000000ad2f800000,0x000000ad2f900000) (1024K)]
  0x000002962ddd67b0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7552, stack(0x000000ad2fc00000,0x000000ad2fd00000) (1024K)]
  0x000002962ddda0c0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=57292, stack(0x000000ad2fd00000,0x000000ad2fe00000) (1024K)]
  0x000002962ddde350 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=10684, stack(0x000000ad2fe00000,0x000000ad2ff00000) (1024K)]
  0x000002967eca2100 JavaThread "Attach Listener"            daemon [_thread_blocked, id=27552, stack(0x000000ad2ff00000,0x000000ad30000000) (1024K)]
  0x000002967eca4610 JavaThread "Service Thread"             daemon [_thread_blocked, id=16152, stack(0x000000ad30000000,0x000000ad30100000) (1024K)]
  0x000002967eca6c80 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=56660, stack(0x000000ad30100000,0x000000ad30200000) (1024K)]
=>0x000002967ecaf080 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=53556, stack(0x000000ad30200000,0x000000ad30300000) (1024K)]
  0x000002967ecb8c70 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=56592, stack(0x000000ad30300000,0x000000ad30400000) (1024K)]
  0x00000296703cdc50 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=54056, stack(0x000000ad30400000,0x000000ad30500000) (1024K)]
  0x000002967607e0a0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=59964, stack(0x000000ad30500000,0x000000ad30600000) (1024K)]
  0x0000029676510970 JavaThread "Active Thread: Equinox Container: e7627b50-54b2-4c2d-b68b-ff79a6ef11eb"        [_thread_blocked, id=27904, stack(0x000000ad30600000,0x000000ad30700000) (1024K)]
  0x00000296764edda0 JavaThread "Framework Event Dispatcher: Equinox Container: e7627b50-54b2-4c2d-b68b-ff79a6ef11eb" daemon [_thread_blocked, id=42256, stack(0x000000ad30700000,0x000000ad30800000) (1024K)]
  0x000002967649ca60 JavaThread "Start Level: Equinox Container: e7627b50-54b2-4c2d-b68b-ff79a6ef11eb" daemon [_thread_blocked, id=27672, stack(0x000000ad30800000,0x000000ad30900000) (1024K)]
  0x00000296762d47b0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=35428, stack(0x000000ad30900000,0x000000ad30a00000) (1024K)]
  0x0000029676870e50 JavaThread "Worker-JM"                         [_thread_blocked, id=54996, stack(0x000000ad30b00000,0x000000ad30c00000) (1024K)]
  0x0000029676ee1480 JavaThread "Worker-0"                          [_thread_blocked, id=44384, stack(0x000000ad30c00000,0x000000ad30d00000) (1024K)]
  0x0000029676958c40 JavaThread "Worker-1"                          [_thread_blocked, id=64324, stack(0x000000ad30d00000,0x000000ad30e00000) (1024K)]
  0x000002967031cfa0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=44776, stack(0x000000ad31a00000,0x000000ad31b00000) (1024K)]
  0x000002967031f700 JavaThread "Thread-2"                   daemon [_thread_in_native, id=33448, stack(0x000000ad31d00000,0x000000ad31e00000) (1024K)]
  0x000002967031dcc0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=35848, stack(0x000000ad31e00000,0x000000ad31f00000) (1024K)]
  0x000002967031d630 JavaThread "Thread-4"                   daemon [_thread_in_native, id=9976, stack(0x000000ad31f00000,0x000000ad32000000) (1024K)]
  0x000002967031e350 JavaThread "Thread-5"                   daemon [_thread_in_native, id=62904, stack(0x000000ad32000000,0x000000ad32100000) (1024K)]
  0x000002967031e9e0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=28664, stack(0x000000ad32100000,0x000000ad32200000) (1024K)]
  0x000002967031f070 JavaThread "Thread-7"                   daemon [_thread_in_native, id=63780, stack(0x000000ad32200000,0x000000ad32300000) (1024K)]
  0x000002967031c280 JavaThread "Thread-8"                   daemon [_thread_in_native, id=49208, stack(0x000000ad32300000,0x000000ad32400000) (1024K)]
  0x000002967031c910 JavaThread "Thread-9"                   daemon [_thread_in_native, id=25280, stack(0x000000ad32400000,0x000000ad32500000) (1024K)]
  0x000002967755be30 JavaThread "Thread-10"                  daemon [_thread_in_native, id=19540, stack(0x000000ad32500000,0x000000ad32600000) (1024K)]
  0x000002967755c4c0 JavaThread "Thread-11"                  daemon [_thread_in_native, id=63936, stack(0x000000ad32600000,0x000000ad32700000) (1024K)]
  0x0000029677561380 JavaThread "Thread-12"                  daemon [_thread_in_native, id=47264, stack(0x000000ad32700000,0x000000ad32800000) (1024K)]
  0x000002967755e590 JavaThread "Thread-13"                  daemon [_thread_in_native, id=62548, stack(0x000000ad32800000,0x000000ad32900000) (1024K)]
  0x0000029677561a10 JavaThread "Thread-14"                  daemon [_thread_in_native, id=63888, stack(0x000000ad32900000,0x000000ad32a00000) (1024K)]
  0x000002967755ec20 JavaThread "Thread-15"                  daemon [_thread_in_native, id=15176, stack(0x000000ad32a00000,0x000000ad32b00000) (1024K)]
  0x000002967755f2b0 JavaThread "Thread-16"                  daemon [_thread_in_native, id=40028, stack(0x000000ad32b00000,0x000000ad32c00000) (1024K)]
  0x0000029677560660 JavaThread "Thread-17"                  daemon [_thread_in_native, id=35608, stack(0x000000ad32c00000,0x000000ad32d00000) (1024K)]
  0x000002967755df00 JavaThread "Thread-18"                  daemon [_thread_in_native, id=24012, stack(0x000000ad32d00000,0x000000ad32e00000) (1024K)]
  0x000002967755cb50 JavaThread "Worker-2"                          [_thread_blocked, id=51448, stack(0x000000ad32e00000,0x000000ad32f00000) (1024K)]
  0x0000029677560cf0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=16964, stack(0x000000ad32f00000,0x000000ad33000000) (1024K)]
  0x00000296775620a0 JavaThread "WorkspaceEventsHandler"            [_thread_in_vm, id=49548, stack(0x000000ad33000000,0x000000ad33100000) (1024K)]
  0x000002967755b110 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=18564, stack(0x000000ad33100000,0x000000ad33200000) (1024K)]
  0x0000029676657a70 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=37968, stack(0x000000ad2f700000,0x000000ad2f800000) (1024K)]
  0x0000029676c44530 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=49772, stack(0x000000ad30a00000,0x000000ad30b00000) (1024K)]
  0x0000029676a11330 JavaThread "C1 CompilerThread3"         daemon [_thread_blocked, id=34620, stack(0x000000ad33200000,0x000000ad33300000) (1024K)]
  0x0000029677b65870 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=51796, stack(0x000000ad2f500000,0x000000ad2f600000) (1024K)]
Total: 44

Other Threads:
  0x000002967eca1c00 VMThread "VM Thread"                           [id=56760, stack(0x000000ad2fb00000,0x000000ad2fc00000) (1024K)]
  0x000002962dcbca00 WatcherThread "VM Periodic Task Thread"        [id=6340, stack(0x000000ad2fa00000,0x000000ad2fb00000) (1024K)]
  0x000002967ec1faa0 WorkerThread "GC Thread#0"                     [id=14688, stack(0x000000ad2f900000,0x000000ad2fa00000) (1024K)]
  0x0000029676088720 WorkerThread "GC Thread#1"                     [id=35420, stack(0x000000ad30e00000,0x000000ad30f00000) (1024K)]
  0x0000029677496950 WorkerThread "GC Thread#2"                     [id=43216, stack(0x000000ad30f00000,0x000000ad31000000) (1024K)]
  0x0000029677496cf0 WorkerThread "GC Thread#3"                     [id=30508, stack(0x000000ad31000000,0x000000ad31100000) (1024K)]
  0x00000296769b5e40 WorkerThread "GC Thread#4"                     [id=4916, stack(0x000000ad31100000,0x000000ad31200000) (1024K)]
  0x00000296769b61e0 WorkerThread "GC Thread#5"                     [id=44796, stack(0x000000ad31200000,0x000000ad31300000) (1024K)]
  0x00000296772252b0 WorkerThread "GC Thread#6"                     [id=16160, stack(0x000000ad31300000,0x000000ad31400000) (1024K)]
  0x0000029677224b70 WorkerThread "GC Thread#7"                     [id=45904, stack(0x000000ad31400000,0x000000ad31500000) (1024K)]
  0x00000296772247d0 WorkerThread "GC Thread#8"                     [id=46972, stack(0x000000ad31500000,0x000000ad31600000) (1024K)]
  0x0000029677224f10 WorkerThread "GC Thread#9"                     [id=60696, stack(0x000000ad31600000,0x000000ad31700000) (1024K)]
  0x0000029677225650 WorkerThread "GC Thread#10"                    [id=41324, stack(0x000000ad31700000,0x000000ad31800000) (1024K)]
  0x0000029677223cf0 WorkerThread "GC Thread#11"                    [id=35076, stack(0x000000ad31800000,0x000000ad31900000) (1024K)]
  0x0000029677224090 WorkerThread "GC Thread#12"                    [id=56720, stack(0x000000ad31900000,0x000000ad31a00000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0    65407 4607       4       lombok.patcher.PatchScript$MethodPatcher::visitMethod (158 bytes)
C2 CompilerThread1    65407 4595       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000580000000, size: 10240 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000002962e000000-0x000002962ec90000-0x000002962ec90000), size 13172736, SharedBaseAddress: 0x000002962e000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002962f000000-0x000002966f000000, reserved size: 1073741824
Narrow klass base: 0x000002962e000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 16 total, 16 available
 Memory: 16228M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 2G
 Heap Initial Capacity: 2G
 Heap Max Capacity: 10G
 Pre-touch: Disabled
 Parallel Workers: 13

Heap:
 PSYoungGen      total 611840K, used 58659K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 11% used [0x000000072ab00000,0x000000072e448ef0,0x000000074ab80000)
  from space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 21823K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 1% used [0x0000000580000000,0x000000058154fca0,0x00000005d5580000)
 Metaspace       used 36490K, committed 37376K, reserved 1114112K
  class space    used 3548K, committed 3904K, reserved 1048576K

Card table byte_map: [0x00000296173d0000,0x00000296187e0000] _byte_map_base: 0x00000296147d0000

Marking Bits: (ParMarkBitMap*) 0x00007ffd4a3b2da0
 Begin Bits: [0x00000296187e0000, 0x00000296227e0000)
 End Bits:   [0x00000296227e0000, 0x000002962c7e0000)

Polling page: 0x000002967cba0000

Metaspace:

Usage:
  Non-class:     32.17 MB used.
      Class:      3.47 MB used.
       Both:     35.64 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      32.69 MB ( 51%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.81 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      36.50 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  14.67 MB
       Class:  12.12 MB
        Both:  26.80 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 8.
num_arena_births: 596.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 583.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 22.
num_chunks_taken_from_freelist: 1991.
num_chunk_merges: 11.
num_chunk_splits: 1307.
num_chunks_enlarged: 870.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=2234Kb max_used=2348Kb free=116933Kb
 bounds [0x000002960ff70000, 0x00000296101e0000, 0x00000296173d0000]
CodeHeap 'profiled nmethods': size=119104Kb used=8798Kb max_used=9331Kb free=110305Kb
 bounds [0x00000296083d0000, 0x0000029608cf0000, 0x000002960f820000]
CodeHeap 'non-nmethods': size=7488Kb used=3064Kb max_used=3103Kb free=4423Kb
 bounds [0x000002960f820000, 0x000002960fb30000, 0x000002960ff70000]
 total_blobs=4942 nmethods=4264 adapters=581
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 65.227 Thread 0x0000029676a11330 nmethod 4632 0x00000296088ac890 code [0x00000296088aca60, 0x00000296088acd80]
Event: 65.227 Thread 0x0000029676657a70 nmethod 4631 0x0000029608423810 code [0x0000029608423a20, 0x0000029608424150]
Event: 65.258 Thread 0x0000029676a11330 4633       3       org.eclipse.osgi.internal.loader.BundleLoader::findLocalResource (9 bytes)
Event: 65.258 Thread 0x0000029676c44530 4634       3       org.eclipse.osgi.internal.loader.ModuleClassLoader::findLocalResource (9 bytes)
Event: 65.258 Thread 0x000002967ecb8c70 4635   !   3       org.eclipse.osgi.internal.loader.classpath.ClasspathManager::findLocalResource (183 bytes)
Event: 65.258 Thread 0x0000029676657a70 4636       3       org.eclipse.osgi.internal.loader.classpath.ClasspathManager::findLocalResourceImpl (174 bytes)
Event: 65.258 Thread 0x0000029676a11330 nmethod 4633 0x0000029608635910 code [0x0000029608635ae0, 0x0000029608635e00]
Event: 65.258 Thread 0x0000029676c44530 nmethod 4634 0x00000296086a4690 code [0x00000296086a4840, 0x00000296086a49b8]
Event: 65.258 Thread 0x0000029676a11330 4637       3       org.eclipse.osgi.internal.loader.classpath.ClasspathManager::findLocalResourceImpl (81 bytes)
Event: 65.259 Thread 0x0000029676a11330 nmethod 4637 0x0000029608493590 code [0x0000029608493760, 0x0000029608493b70]
Event: 65.259 Thread 0x000002967ecb8c70 nmethod 4635 0x0000029608489010 code [0x00000296084892e0, 0x000002960848a4c0]
Event: 65.259 Thread 0x0000029676657a70 nmethod 4636 0x000002960850ae90 code [0x000002960850b100, 0x000002960850bcb8]
Event: 65.299 Thread 0x000002967ecaf080 nmethod 4623 0x00000296101c1c90 code [0x00000296101c2260, 0x00000296101c6050]
Event: 65.300 Thread 0x000002967ecaf080 4638 %     4       lombok.launch.PackageShader::apply @ 76 (252 bytes)
Event: 65.315 Thread 0x0000029676c44530 4639   !   3       java.net.URL::of (255 bytes)
Event: 65.318 Thread 0x0000029676c44530 nmethod 4639 0x00000296084cb690 code [0x00000296084cbae0, 0x00000296084cd628]
Event: 65.320 Thread 0x000002967ecaf080 nmethod 4638% 0x000002960ff9f590 code [0x000002960ff9f780, 0x000002960ffa0348]
Event: 65.320 Thread 0x000002967ecaf080 4607       4       lombok.patcher.PatchScript$MethodPatcher::visitMethod (158 bytes)
Event: 65.323 Thread 0x0000029676a11330 4640   !   3       java.io.File::toURI (69 bytes)
Event: 65.324 Thread 0x0000029676a11330 nmethod 4640 0x0000029608498290 code [0x0000029608498560, 0x0000029608499120]

GC Heap History (8 events):
Event: 6.296 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 611840K, used 252048K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 48% used [0x000000072ab00000,0x000000073a1240d8,0x000000074ab80000)
  from space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 0K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x0000000580000000,0x00000005d5580000)
 Metaspace       used 20864K, committed 21504K, reserved 1114112K
  class space    used 1929K, committed 2240K, reserved 1048576K
}
Event: 6.344 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 611840K, used 10126K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 11% used [0x000000074ab80000,0x000000074b563be8,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 104K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x000000058001a010,0x00000005d5580000)
 Metaspace       used 20864K, committed 21504K, reserved 1114112K
  class space    used 1929K, committed 2240K, reserved 1048576K
}
Event: 6.344 GC heap before
{Heap before GC invocations=2 (full 1):
 PSYoungGen      total 611840K, used 10126K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 11% used [0x000000074ab80000,0x000000074b563be8,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 104K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x000000058001a010,0x00000005d5580000)
 Metaspace       used 20864K, committed 21504K, reserved 1114112K
  class space    used 1929K, committed 2240K, reserved 1048576K
}
Event: 6.422 GC heap after
{Heap after GC invocations=2 (full 1):
 PSYoungGen      total 611840K, used 0K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 9916K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x00000005809af278,0x00000005d5580000)
 Metaspace       used 20852K, committed 21504K, reserved 1114112K
  class space    used 1926K, committed 2240K, reserved 1048576K
}
Event: 64.751 GC heap before
{Heap before GC invocations=3 (full 1):
 PSYoungGen      total 611840K, used 381991K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 72% used [0x000000072ab00000,0x0000000742009f88,0x000000074ab80000)
  from space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 9916K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x00000005809af278,0x00000005d5580000)
 Metaspace       used 35049K, committed 35840K, reserved 1114112K
  class space    used 3405K, committed 3776K, reserved 1048576K
}
Event: 64.819 GC heap after
{Heap after GC invocations=3 (full 1):
 PSYoungGen      total 611840K, used 13288K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 15% used [0x0000000750080000,0x0000000750d7a370,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 9924K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x00000005809b1278,0x00000005d5580000)
 Metaspace       used 35049K, committed 35840K, reserved 1114112K
  class space    used 3405K, committed 3776K, reserved 1048576K
}
Event: 64.819 GC heap before
{Heap before GC invocations=4 (full 2):
 PSYoungGen      total 611840K, used 13288K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 15% used [0x0000000750080000,0x0000000750d7a370,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 9924K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x00000005809b1278,0x00000005d5580000)
 Metaspace       used 35049K, committed 35840K, reserved 1114112K
  class space    used 3405K, committed 3776K, reserved 1048576K
}
Event: 64.963 GC heap after
{Heap after GC invocations=4 (full 2):
 PSYoungGen      total 611840K, used 0K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 21823K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 1% used [0x0000000580000000,0x000000058154fca0,0x00000005d5580000)
 Metaspace       used 35049K, committed 35840K, reserved 1114112K
  class space    used 3405K, committed 3776K, reserved 1048576K
}

Dll operation events (13 events):
Event: 0.064 Loaded shared library D:\Dev\Env\jdk21\bin\java.dll
Event: 0.218 Loaded shared library D:\Dev\Env\jdk21\bin\jsvml.dll
Event: 0.293 Loaded shared library D:\Dev\Env\jdk21\bin\zip.dll
Event: 0.301 Loaded shared library D:\Dev\Env\jdk21\bin\instrument.dll
Event: 0.312 Loaded shared library D:\Dev\Env\jdk21\bin\net.dll
Event: 0.326 Loaded shared library D:\Dev\Env\jdk21\bin\nio.dll
Event: 0.333 Loaded shared library D:\Dev\Env\jdk21\bin\zip.dll
Event: 0.382 Loaded shared library D:\Dev\Env\jdk21\bin\jimage.dll
Event: 0.544 Loaded shared library D:\Dev\Env\jdk21\bin\verify.dll
Event: 2.807 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.470 Loaded shared library D:\Dev\Env\jdk21\bin\management.dll
Event: 4.478 Loaded shared library D:\Dev\Env\jdk21\bin\management_ext.dll
Event: 7.859 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna9430288266965912188.dll

Deoptimization events (20 events):
Event: 65.097 Thread 0x00000296775620a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029610085f88 relative=0x0000000000006a08
Event: 65.097 Thread 0x00000296775620a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029610085f88 method=lombok.patcher.PatchScript$MethodPatcher.visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; @ 101 c
Event: 65.097 Thread 0x00000296775620a0 DEOPT PACKING pc=0x0000029610085f88 sp=0x000000ad330fa4d0
Event: 65.098 Thread 0x00000296775620a0 DEOPT UNPACKING pc=0x000002960f873aa2 sp=0x000000ad330fa438 mode 2
Event: 65.098 Thread 0x00000296775620a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000029610085f88 relative=0x0000000000006a08
Event: 65.098 Thread 0x00000296775620a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000029610085f88 method=lombok.patcher.PatchScript$MethodPatcher.visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; @ 101 c
Event: 65.098 Thread 0x00000296775620a0 DEOPT PACKING pc=0x0000029610085f88 sp=0x000000ad330fa4d0
Event: 65.098 Thread 0x00000296775620a0 DEOPT UNPACKING pc=0x000002960f873aa2 sp=0x000000ad330fa438 mode 2
Event: 65.115 Thread 0x00000296775620a0 Uncommon trap: trap_request=0xffffff6e fr.pc=0x0000029610008208 relative=0x0000000000000a28
Event: 65.115 Thread 0x00000296775620a0 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x0000029610008208 method=java.util.regex.Pattern$StartS.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 34 c2
Event: 65.115 Thread 0x00000296775620a0 DEOPT PACKING pc=0x0000029610008208 sp=0x000000ad330fa170
Event: 65.115 Thread 0x00000296775620a0 DEOPT UNPACKING pc=0x000002960f873aa2 sp=0x000000ad330fa0d0 mode 2
Event: 65.148 Thread 0x00000296775620a0 DEOPT PACKING pc=0x00000296088f89f1 sp=0x000000ad330fa560
Event: 65.148 Thread 0x00000296775620a0 DEOPT UNPACKING pc=0x000002960f874242 sp=0x000000ad330f9b08 mode 0
Event: 65.152 Thread 0x00000296775620a0 DEOPT PACKING pc=0x00000296088f89f1 sp=0x000000ad330fa560
Event: 65.152 Thread 0x00000296775620a0 DEOPT UNPACKING pc=0x000002960f874242 sp=0x000000ad330f9b08 mode 0
Event: 65.157 Thread 0x00000296775620a0 DEOPT PACKING pc=0x00000296088f89f1 sp=0x000000ad330fa560
Event: 65.157 Thread 0x00000296775620a0 DEOPT UNPACKING pc=0x000002960f874242 sp=0x000000ad330f9b08 mode 0
Event: 65.165 Thread 0x00000296775620a0 DEOPT PACKING pc=0x00000296088f89f1 sp=0x000000ad330fa560
Event: 65.165 Thread 0x00000296775620a0 DEOPT UNPACKING pc=0x000002960f874242 sp=0x000000ad330f9b08 mode 0

Classes loaded (20 events):
Event: 63.238 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_CN
Event: 63.238 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_CN done
Event: 63.238 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_CN
Event: 63.238 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_CN done
Event: 63.241 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_Hans
Event: 63.241 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_Hans done
Event: 63.241 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_Hans
Event: 63.241 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_Hans done
Event: 63.241 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_Hans_CN
Event: 63.241 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_Hans_CN done
Event: 63.241 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_Hans_CN
Event: 63.241 Loading class com/sun/org/apache/xerces/internal/impl/msg/XMLMessages_zh_Hans_CN done
Event: 63.276 Loading class java/io/UTFDataFormatException
Event: 63.277 Loading class java/io/UTFDataFormatException done
Event: 64.142 Loading class java/lang/UnsupportedClassVersionError
Event: 64.143 Loading class java/lang/UnsupportedClassVersionError done
Event: 64.365 Loading class java/lang/ExceptionInInitializerError
Event: 64.365 Loading class java/lang/ExceptionInInitializerError done
Event: 64.576 Loading class sun/misc/Unsafe
Event: 64.577 Loading class sun/misc/Unsafe done

Classes unloaded (7 events):
Event: 6.350 Thread 0x000002967eca1c00 Unloading class 0x000002962f17bc00 'java/lang/invoke/LambdaForm$MH+0x000002962f17bc00'
Event: 6.350 Thread 0x000002967eca1c00 Unloading class 0x000002962f17b800 'java/lang/invoke/LambdaForm$MH+0x000002962f17b800'
Event: 6.350 Thread 0x000002967eca1c00 Unloading class 0x000002962f17b400 'java/lang/invoke/LambdaForm$MH+0x000002962f17b400'
Event: 6.350 Thread 0x000002967eca1c00 Unloading class 0x000002962f17b000 'java/lang/invoke/LambdaForm$MH+0x000002962f17b000'
Event: 6.350 Thread 0x000002967eca1c00 Unloading class 0x000002962f17ac00 'java/lang/invoke/LambdaForm$BMH+0x000002962f17ac00'
Event: 6.350 Thread 0x000002967eca1c00 Unloading class 0x000002962f17a800 'java/lang/invoke/LambdaForm$DMH+0x000002962f17a800'
Event: 6.350 Thread 0x000002967eca1c00 Unloading class 0x000002962f179800 'java/lang/invoke/LambdaForm$DMH+0x000002962f179800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 62.986 Thread 0x00000296775620a0 Exception <a 'java/io/FileNotFoundException'{0x0000000737ec8938}> (0x0000000737ec8938) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.041 Thread 0x0000029676958c40 Exception <a 'java/lang/NoSuchMethodError'{0x000000072df47800}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000072df47800) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 63.078 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000738c110f0}> (0x0000000738c110f0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.084 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000738c34280}> (0x0000000738c34280) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.097 Thread 0x00000296775620a0 Exception <a 'java/io/FileNotFoundException'{0x0000000738ca3060}> (0x0000000738ca3060) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.102 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000738caa2d8}> (0x0000000738caa2d8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.104 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000738caffb0}> (0x0000000738caffb0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.109 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000738cbf630}> (0x0000000738cbf630) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.110 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000738cbfb70}> (0x0000000738cbfb70) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.110 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000738cc0410}> (0x0000000738cc0410) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.111 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000738cc0950}> (0x0000000738cc0950) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.214 Thread 0x00000296775620a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000007393e39d8}> (0x00000007393e39d8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.218 Thread 0x00000296775620a0 Exception <a 'java/io/FileNotFoundException'{0x00000007393f1898}> (0x00000007393f1898) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.218 Thread 0x00000296775620a0 Exception <a 'java/io/FileNotFoundException'{0x00000007393f2b20}> (0x00000007393f2b20) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 63.230 Thread 0x00000296775620a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000073941c3a0}: com/sun/org/apache/xerces/internal/impl/msg/spi/DOMMessagesProvider> (0x000000073941c3a0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 63.233 Thread 0x00000296775620a0 Exception <a 'java/lang/ClassNotFoundException'{0x00000007394354e8}: com/sun/org/apache/xerces/internal/impl/msg/spi/XMLSerializerMessagesProvider> (0x00000007394354e8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 63.235 Thread 0x00000296775620a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000073944a410}: com/sun/org/apache/xerces/internal/impl/msg/spi/XMLMessagesProvider> (0x000000073944a410) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 63.250 Thread 0x00000296775620a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007394b91d0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007394b91d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 64.308 Thread 0x00000296775620a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000074146b828}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x000000074146b828) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 64.621 Thread 0x00000296775620a0 Implicit null exception at 0x00000296100ffe31 to 0x0000029610100138

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 12.575 Executing VM operation: Cleanup
Event: 12.579 Executing VM operation: Cleanup done
Event: 13.579 Executing VM operation: Cleanup
Event: 13.579 Executing VM operation: Cleanup done
Event: 14.580 Executing VM operation: Cleanup
Event: 14.580 Executing VM operation: Cleanup done
Event: 60.086 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 60.090 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 60.090 Executing VM operation: RendezvousGCThreads
Event: 60.090 Executing VM operation: RendezvousGCThreads done
Event: 62.810 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 62.811 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 63.813 Executing VM operation: Cleanup
Event: 63.813 Executing VM operation: Cleanup done
Event: 64.078 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 64.078 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 64.750 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 64.963 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 64.963 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 64.963 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done

Events (20 events):
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x00000296086dce90
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x00000296086e2310
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x00000296086e6e90
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x00000296086efa10
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x00000296086f0290
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x00000296086f9490
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x0000029608712190
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x0000029608716b10
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x0000029608719a10
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x000002960874b890
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x000002960874bb90
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x000002960875fc90
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x0000029608830690
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x0000029608830b10
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x0000029608870910
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x0000029608871390
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x0000029608876a90
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x00000296088ac890
Event: 64.896 Thread 0x000002967eca1c00 flushing nmethod 0x00000296088c4b10
Event: 65.028 Thread 0x0000029677b65870 Thread added: 0x0000029677b65870


Dynamic libraries:
0x00007ff6c73d0000 - 0x00007ff6c73e0000 	D:\Dev\Env\jdk21\bin\java.exe
0x00007ffe013b0000 - 0x00007ffe015c7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffdfffb0000 - 0x00007ffe00074000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffdfe6d0000 - 0x00007ffdfeaa2000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffdfeab0000 - 0x00007ffdfebc1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffddf1d0000 - 0x00007ffddf1eb000 	D:\Dev\Env\jdk21\bin\VCRUNTIME140.dll
0x00007ffddcc50000 - 0x00007ffddcc69000 	D:\Dev\Env\jdk21\bin\jli.dll
0x00007ffe010b0000 - 0x00007ffe01161000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffdff340000 - 0x00007ffdff3e7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffdff290000 - 0x00007ffdff338000 	C:\WINDOWS\System32\sechost.dll
0x00007ffdfedc0000 - 0x00007ffdfede8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe00e30000 - 0x00007ffe00f47000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe01180000 - 0x00007ffe01331000 	C:\WINDOWS\System32\USER32.dll
0x00007ffde3000000 - 0x00007ffde329b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5697_none_27154c497380d676\COMCTL32.dll
0x00007ffdfe560000 - 0x00007ffdfe586000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe003b0000 - 0x00007ffe003d9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffdfef10000 - 0x00007ffdff033000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffdfedf0000 - 0x00007ffdfee8a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffdf6990000 - 0x00007ffdf699a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffdfff60000 - 0x00007ffdfff91000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffddf490000 - 0x00007ffddf49c000 	D:\Dev\Env\jdk21\bin\vcruntime140_1.dll
0x00007ffd9b040000 - 0x00007ffd9b0ce000 	D:\Dev\Env\jdk21\bin\msvcp140.dll
0x00007ffd49770000 - 0x00007ffd4a48a000 	D:\Dev\Env\jdk21\bin\server\jvm.dll
0x00007ffe002a0000 - 0x00007ffe00311000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffdfe1c0000 - 0x00007ffdfe20d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffdea080000 - 0x00007ffdea0b4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffdfe1a0000 - 0x00007ffdfe1b3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffdfd440000 - 0x00007ffdfd458000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffddf440000 - 0x00007ffddf44a000 	D:\Dev\Env\jdk21\bin\jimage.dll
0x00007ffdf2290000 - 0x00007ffdf24c3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe00610000 - 0x00007ffe009a2000 	C:\WINDOWS\System32\combase.dll
0x00007ffdff0f0000 - 0x00007ffdff1c8000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffded700000 - 0x00007ffded732000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffdfed40000 - 0x00007ffdfedbb000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffddefc0000 - 0x00007ffddefcf000 	D:\Dev\Env\jdk21\bin\instrument.dll
0x00007ffddb490000 - 0x00007ffddb4af000 	D:\Dev\Env\jdk21\bin\java.dll
0x00007ffdff3f0000 - 0x00007ffdffc91000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffdfe590000 - 0x00007ffdfe6cf000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffdfc300000 - 0x00007ffdfcc1a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffdffe50000 - 0x00007ffdfff5b000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe00080000 - 0x00007ffe000e9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffdfe3d0000 - 0x00007ffdfe3fb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd74ee0000 - 0x00007ffd74fb7000 	D:\Dev\Env\jdk21\bin\jsvml.dll
0x00007ffdd1330000 - 0x00007ffdd1348000 	D:\Dev\Env\jdk21\bin\zip.dll
0x00007ffddd430000 - 0x00007ffddd440000 	D:\Dev\Env\jdk21\bin\net.dll
0x00007ffdf6ab0000 - 0x00007ffdf6bdc000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffdf3aa0000 - 0x00007ffdf3c1a000 	C:\Program Files (x86)\Sangfor\VDI\ClientComponent2\SangforTcpX64.dll
0x00007ffe00100000 - 0x00007ffe002a0000 	C:\WINDOWS\System32\ole32.dll
0x00007ffdfee90000 - 0x00007ffdfef0c000 	C:\WINDOWS\System32\WINTRUST.dll
0x00007ffdfebd0000 - 0x00007ffdfed38000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffdfe0c0000 - 0x00007ffdfe0d2000 	C:\WINDOWS\SYSTEM32\MSASN1.dll
0x00007ffdfd900000 - 0x00007ffdfd969000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffdd1310000 - 0x00007ffdd1326000 	D:\Dev\Env\jdk21\bin\nio.dll
0x00007ffddd320000 - 0x00007ffddd330000 	D:\Dev\Env\jdk21\bin\verify.dll
0x00007ffdad220000 - 0x00007ffdad265000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffddd200000 - 0x00007ffddd20a000 	D:\Dev\Env\jdk21\bin\management.dll
0x00007ffddd1b0000 - 0x00007ffddd1bb000 	D:\Dev\Env\jdk21\bin\management_ext.dll
0x00007ffe01360000 - 0x00007ffe01368000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffdfdc50000 - 0x00007ffdfdc6b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffdfd3a0000 - 0x00007ffdfd3d7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffdfd9a0000 - 0x00007ffdfd9c8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffdfdb00000 - 0x00007ffdfdb0c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffdfce80000 - 0x00007ffdfcead000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffdfffa0000 - 0x00007ffdfffa9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffddb7a0000 - 0x00007ffddb7e9000 	C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna9430288266965912188.dll
0x00007ffdf69c0000 - 0x00007ffdf69d9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdf69a0000 - 0x00007ffdf69bf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Dev\Env\jdk21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5697_none_27154c497380d676;D:\Dev\Env\jdk21\bin\server;C:\Program Files (x86)\Sangfor\VDI\ClientComponent2;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-146731693

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=true -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx10G -Xms2G -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\02f6bb0736726e084caf6170a8d996d1\redhat.java\ss_ws --pipe=\\.\pipe\lsp-01466d58123764f1f382a19fb568b4e3-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 2147483648                                {product} {command line}
   size_t MaxHeapSize                              = 10737418240                               {product} {command line}
   size_t MaxNewSize                               = 3578789888                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 2147483648                                {product} {command line}
   size_t NewSize                                  = 715653120                                 {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 1431830528                                {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 10737418240                            {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Dev\Env\jdk8
CLASSPATH=.;%JAVA_HOME%\lib\dt.jar;%JAVA_HOME%\lib\tools.jar
PATH=D:\Dev\Env\python\Scripts\;D:\Dev\Env\python\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Google\Chrome\Application\;C:\Program File;C:\Program Files\Git\bin;C:\Users\<USER>\fvm\default\bin;D:\Program Files\Xftp 8\;C:\Program Files\CursorModifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files\Microsoft SQL Server\110\Tools\Binn\;D:\Program Files\swigwin-4.3.0;C:\Users\<USER>\.local\bin;D:\Dev\Env\apache-maven-3.6.2\bin;D:\Dev\Env\uv;D:\Program Files\TortoiseGit\bin;C:\Program Files (x86)\Xshell 8\;C:\Program Files (x86)\Xftp\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\tools\neovim\nvim-win64\bin;C:\Users\<USER>\AppData\Roaming\nvm;node_global;node_global\node_modules\yarn\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Fiddler;C:\msys64\mingw64\bin;D:\Program Files\JetBrains\DataGrip 2023.1\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;D:\Program Files\Neovim\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\GoLand 2024.1.4\bin;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;D:\Java\apache-maven-3.9.6\bin;C:\ProgramData\mingw64\mingw64\bin;C:\Program Files\cursor-id-modifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;%IntelliJ IDEA%;C:\Program Files\ai;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 1 days 21:36 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xe2, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 16 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 16228M (582M free)
TotalPageFile size 51627M (AvailPageFile size 1M)
current process WorkingSet (physical memory assigned to process): 356M, peak: 357M
current process commit charge ("private bytes"): 2554M, peak: 2559M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.7+8-LTS-245) for windows-amd64 JRE (21.0.7+8-LTS-245), built on 2025-02-21T05:11:27Z by "mach5one" with MS VC++ 17.6 (VS2022)

END.
