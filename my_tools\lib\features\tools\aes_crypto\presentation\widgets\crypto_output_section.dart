import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../shared/widgets/custom_text_field.dart';
import '../../domain/models/crypto_result.dart';
import '../providers/aes_crypto_providers.dart';

/// 加密输出区域组件
class CryptoOutputSection extends ConsumerStatefulWidget {
  const CryptoOutputSection({super.key});

  @override
  ConsumerState<CryptoOutputSection> createState() => _CryptoOutputSectionState();
}

class _CryptoOutputSectionState extends ConsumerState<CryptoOutputSection> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();

    // 监听焦点变化
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        ref.read(lastActiveFieldProvider.notifier).state = ActiveField.output;
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final result = ref.watch(processResultProvider);

    // 监听输出文本变化
    ref.listen<String>(outputTextProvider, (previous, next) {
      if (_controller.text != next) {
        _controller.text = next;
      }
    });

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Text(
                  '输出区域',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                // 状态指示器
                if (result != null) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: result.isSuccess ? Colors.green.shade50 : Colors.red.shade50,
                      border: Border.all(
                        color: result.isSuccess ? Colors.green.shade200 : Colors.red.shade200,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          result.isSuccess ? Icons.check_circle : Icons.error,
                          size: 16,
                          color: result.isSuccess ? Colors.green.shade600 : Colors.red.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          result.isSuccess ? result.operation.displayName : '失败',
                          style: TextStyle(
                            fontSize: 12,
                            color: result.isSuccess ? Colors.green.shade700 : Colors.red.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                // 清空按钮
                IconButton(
                  onPressed: () {
                    _controller.clear();
                    ref.read(outputTextProvider.notifier).state = '';
                  },
                  icon: const Icon(Icons.clear),
                  tooltip: '清空',
                ),
                // 粘贴按钮
                IconButton(
                  onPressed: _pasteFromClipboard,
                  icon: const Icon(Icons.paste),
                  tooltip: '粘贴',
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 输出框
            CustomTextField(
              controller: _controller,
              focusNode: _focusNode,
              hintText: '加密/解密结果将显示在这里...',
              maxLines: 6,
              minLines: 6,
              onChanged: (value) {
                ref.read(outputTextProvider.notifier).state = value;
                ref.read(autoProcessProvider).onOutputChanged(value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 操作按钮和错误信息
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 操作按钮
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        ref.read(autoProcessProvider).processCurrentField();
                      },
                      icon: const Icon(Icons.lock_open),
                      label: const Text('解密'),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton.icon(
                      onPressed: _copyToClipboard,
                      icon: const Icon(Icons.copy),
                      label: const Text('复制'),
                    ),
                  ],
                ),
                
                // 错误信息
                if (result != null && result.isFailure) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      border: Border.all(color: Colors.red.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error, color: Colors.red.shade600, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            result.errorMessage,
                            style: TextStyle(
                              color: Colors.red.shade700,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 从剪贴板粘贴
  Future<void> _pasteFromClipboard() async {
    try {
      final data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data?.text != null) {
        _controller.text = data!.text!;
        ref.read(outputTextProvider.notifier).state = data.text!;
        ref.read(autoProcessProvider).onOutputChanged(data.text!);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('粘贴失败: $e')),
        );
      }
    }
  }

  /// 复制到剪贴板
  Future<void> _copyToClipboard() async {
    try {
      await Clipboard.setData(ClipboardData(text: _controller.text));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('已复制到剪贴板')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('复制失败: $e')),
        );
      }
    }
  }
}
