import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/aes_crypto_providers.dart';
import '../../domain/models/crypto_result.dart';
import '../../../../../shared/utils/app_logger.dart';

/// 紧凑的输入输出组件（移动端专用）
class CompactInputOutputSection extends ConsumerStatefulWidget {
  const CompactInputOutputSection({super.key});

  @override
  ConsumerState<CompactInputOutputSection> createState() =>
      _CompactInputOutputSectionState();
}

class _CompactInputOutputSectionState
    extends ConsumerState<CompactInputOutputSection> {
  final TextEditingController _inputController = TextEditingController();
  final TextEditingController _outputController = TextEditingController();
  final FocusNode _inputFocusNode = FocusNode();

  @override
  void dispose() {
    _inputController.dispose();
    _outputController.dispose();
    _inputFocusNode.dispose();
    super.dispose();
  }

  void _copyToClipboard(String text) {
    if (text.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: text));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('已复制到剪贴板'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  Future<void> _pasteFromClipboard() async {
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData?.text != null) {
      _inputController.text = clipboardData!.text!;
      ref.read(inputTextProvider.notifier).state = clipboardData.text!;
    }
  }

  void _clearInput() {
    _inputController.clear();
    ref.read(inputTextProvider.notifier).state = '';
    _inputFocusNode.requestFocus();
  }

  void _clearOutput() {
    ref.read(outputTextProvider.notifier).state = '';
  }

  @override
  Widget build(BuildContext context) {
    final inputText = ref.watch(inputTextProvider);
    final outputText = ref.watch(outputTextProvider);
    final processResult = ref.watch(processResultProvider);

    // 同步输入控制器
    if (_inputController.text != inputText) {
      _inputController.text = inputText;
    }

    // 同步输出控制器
    if (_outputController.text != outputText) {
      _outputController.text = outputText;
    }

    return Column(
      children: [
        // 模式切换按钮
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final mode = ref.watch(operationModeProvider);
                    return SegmentedButton<OperationMode>(
                      segments: OperationMode.values.map((mode) {
                        return ButtonSegment<OperationMode>(
                          value: mode,
                          label: Text(mode.displayName),
                          icon: Icon(mode == OperationMode.encrypt
                              ? Icons.lock
                              : Icons.lock_open),
                        );
                      }).toList(),
                      selected: {mode},
                      onSelectionChanged: (Set<OperationMode> selection) {
                        ref.read(operationModeProvider.notifier).state = selection.first;
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // 输入区域
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 输入标题和操作按钮
                Row(
                  children: [
                    Icon(
                      Icons.edit,
                      size: 18,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '输入文本',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    const Spacer(),
                    // 操作按钮
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: _pasteFromClipboard,
                          icon: const Icon(Icons.paste, size: 18),
                          tooltip: '粘贴',
                          padding: const EdgeInsets.all(4),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                        IconButton(
                          onPressed: inputText.isNotEmpty ? _clearInput : null,
                          icon: const Icon(Icons.clear, size: 18),
                          tooltip: '清空',
                          padding: const EdgeInsets.all(4),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                
                // 输入文本框
                TextField(
                  controller: _inputController,
                  focusNode: _inputFocusNode,
                  maxLines: 4,
                  minLines: 3,
                  decoration: InputDecoration(
                    hintText: '请输入要加密或解密的文本...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.all(12),
                    isDense: true,
                  ),
                  onChanged: (value) {
                    AppLogger.debug('CompactInputOutputSection: 输入文本变化', {
                      'value_length': value.length,
                      'value_preview': value.length > 20 ? '${value.substring(0, 20)}...' : value,
                    });
                    ref.read(inputTextProvider.notifier).state = value;
                    ref.read(lastActiveFieldProvider.notifier).state = ActiveField.input;
                  },
                ),
              ],
            ),
          ),
        ),
        
        // 处理状态指示器（简化版）
        if (inputText.isNotEmpty && outputText.isEmpty)
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '处理中...',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        
        // 输出区域
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 输出标题和操作按钮
                Row(
                  children: [
                    Icon(
                      processResult?.isFailure == true
                          ? Icons.error_outline
                          : Icons.output,
                      size: 18,
                      color: processResult?.isFailure == true
                          ? Theme.of(context).colorScheme.error
                          : Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      processResult?.isFailure == true ? '错误信息' : '输出结果',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: processResult?.isFailure == true
                            ? Theme.of(context).colorScheme.error
                            : null,
                      ),
                    ),
                    const Spacer(),
                    // 操作按钮
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: outputText.isNotEmpty
                              ? () => _copyToClipboard(outputText)
                              : null,
                          icon: const Icon(Icons.copy, size: 18),
                          tooltip: '复制',
                          padding: const EdgeInsets.all(4),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                        IconButton(
                          onPressed: outputText.isNotEmpty ? _clearOutput : null,
                          icon: const Icon(Icons.clear, size: 18),
                          tooltip: '清空',
                          padding: const EdgeInsets.all(4),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                
                // 输出文本框
                TextField(
                  controller: _outputController,
                  maxLines: 4,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'monospace',
                  ),
                  decoration: InputDecoration(
                    hintText: '结果将在这里显示，也可以在此输入密文进行解密...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    errorBorder: processResult?.isFailure == true
                        ? OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.error,
                            ),
                          )
                        : null,
                    contentPadding: const EdgeInsets.all(12),
                    isDense: true,
                  ),
                  onChanged: (value) {
                    AppLogger.debug('CompactInputOutputSection: 输出文本变化', {
                      'value_length': value.length,
                      'value_preview': value.length > 20 ? '${value.substring(0, 20)}...' : value,
                    });
                    ref.read(outputTextProvider.notifier).state = value;
                    ref.read(lastActiveFieldProvider.notifier).state = ActiveField.output;
                  },
                ),
              ],
            ),
          ),
        ),
        
        // 使用说明（紧凑版）
        if (inputText.isEmpty && outputText.isEmpty)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '使用说明',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Text(
                  '• 输入明文自动加密，输入密文自动解密\n• 点击配置可调整加密参数\n• 支持复制粘贴操作',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
      ],
    );
  }
}
