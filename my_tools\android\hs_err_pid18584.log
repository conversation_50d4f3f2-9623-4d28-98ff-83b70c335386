#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=18584, tid=27372
#
# JRE version: OpenJDK Runtime Environment (21.0.6) (build 21.0.6+-13391695-b895.109)
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13391695-b895.109, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:D:\Dev\Env\repo\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Jul 31 14:21:18 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 28.572111 seconds (0d 0h 0m 28s)

---------------  T H R E A D  ---------------

Current thread (0x000001deaf714ea0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=27372, stack(0x000000c57af00000,0x000000c57b000000) (1024K)]


Current CompileTask:
C2:28572 14361       4       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput$1::execute (9 bytes)

Stack: [0x000000c57af00000,0x000000c57b000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0xbff6d]
V  [jvm.dll+0xc04a3]
V  [jvm.dll+0x1cc7f0]
V  [jvm.dll+0x3925b8]
V  [jvm.dll+0x3984ec]
V  [jvm.dll+0x39df2a]
V  [jvm.dll+0x393412]
V  [jvm.dll+0x39a946]
V  [jvm.dll+0x1c9af1]
V  [jvm.dll+0x2f1984]
V  [jvm.dll+0x6eab46]
V  [jvm.dll+0x6e2d9b]
V  [jvm.dll+0x6e2242]
V  [jvm.dll+0x6e040a]
V  [jvm.dll+0x1c90dc]
V  [jvm.dll+0x1c9c8a]
V  [jvm.dll+0x2f1984]
V  [jvm.dll+0x6eab46]
V  [jvm.dll+0x6e2d9b]
V  [jvm.dll+0x6e2242]
V  [jvm.dll+0x6e040a]
V  [jvm.dll+0x1c90dc]
V  [jvm.dll+0x1c9c8a]
V  [jvm.dll+0x2f1984]
V  [jvm.dll+0x6eab46]
V  [jvm.dll+0x6e2d9b]
V  [jvm.dll+0x6e2242]
V  [jvm.dll+0x6e040a]
V  [jvm.dll+0x1c90dc]
V  [jvm.dll+0x1c9c8a]
V  [jvm.dll+0x2f1984]
V  [jvm.dll+0x6eab46]
V  [jvm.dll+0x6e2d9b]
V  [jvm.dll+0x6e2242]
V  [jvm.dll+0x6e040a]
V  [jvm.dll+0x1c90dc]
V  [jvm.dll+0x1c9c8a]
V  [jvm.dll+0x2f1984]
V  [jvm.dll+0x6eab46]
V  [jvm.dll+0x6e2d9b]
V  [jvm.dll+0x6e2242]
V  [jvm.dll+0x6e040a]
V  [jvm.dll+0x1c90dc]
V  [jvm.dll+0x246ded]
V  [jvm.dll+0x1c5f04]
V  [jvm.dll+0x2569ac]
V  [jvm.dll+0x254ef6]
V  [jvm.dll+0x3f0e96]
V  [jvm.dll+0x806ed8]
V  [jvm.dll+0x6cef0d]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001debfc9e6e0, length=195, elements={
0x000001defc6595d0, 0x000001deaf707430, 0x000001deaf6d4360, 0x000001deaf713440,
0x000001deaf712db0, 0x000001deaf712090, 0x000001deaf70ffc0, 0x000001deaf714ea0,
0x000001deaf7160d0, 0x000001deaf711370, 0x000001deaf710650, 0x000001deaf710ce0,
0x000001deaf712720, 0x000001deaf711a00, 0x000001deb59aca40, 0x000001deb59a9c50,
0x000001deb59ab000, 0x000001deb59ac3b0, 0x000001deb59ad760, 0x000001deb59ad0d0,
0x000001deb59aa2e0, 0x000001deb59ab690, 0x000001deb59abd20, 0x000001deb59addf0,
0x000001deb59a88a0, 0x000001deb59a7b80, 0x000001deb59aa970, 0x000001deb59ae480,
0x000001deb59af1a0, 0x000001deb59a8210, 0x000001deb59a8f30, 0x000001deb59a95c0,
0x000001deb86815c0, 0x000001deb8681c50, 0x000001deb8682970, 0x000001deb867fb80,
0x000001deb8685760, 0x000001deb86822e0, 0x000001deb8683d20, 0x000001deb8685df0,
0x000001deb8680f30, 0x000001deb8683690, 0x000001deb86843b0, 0x000001deb8683000,
0x000001deb86850d0, 0x000001deb8686480, 0x000001deb867f4f0, 0x000001deb8686b10,
0x000001deb8680210, 0x000001deb86808a0, 0x000001deb93782d0, 0x000001deb9373410,
0x000001deb9376f20, 0x000001deb9376890, 0x000001deb9373aa0, 0x000001deb93754e0,
0x000001deb9372d80, 0x000001deb9375b70, 0x000001deb9374130, 0x000001deb93747c0,
0x000001deb93775b0, 0x000001deb9374e50, 0x000001deb9378ff0, 0x000001deb9376200,
0x000001deb9378960, 0x000001deb9377c40, 0x000001deb9372060, 0x000001deb9379680,
0x000001deb93726f0, 0x000001deb82c3620, 0x000001deb82c3cb0, 0x000001deb82c5060,
0x000001deb82c4340, 0x000001deb82c1be0, 0x000001deb82c2270, 0x000001deb82c49d0,
0x000001deb82c6aa0, 0x000001deb82c6410, 0x000001deb82c7e50, 0x000001deb82c56f0,
0x000001deb8b4f260, 0x000001deb8b50610, 0x000001deb8b52050, 0x000001deb8b4deb0,
0x000001deb8b50ca0, 0x000001deb8b51330, 0x000001deb8b526e0, 0x000001deb8b519c0,
0x000001deb8b52d70, 0x000001deb8b4bde0, 0x000001deb8b4e540, 0x000001deb8b53400,
0x000001deb8b4c470, 0x000001deb8b4d190, 0x000001deb8b4cb00, 0x000001deb8b4d820,
0x000001deb8b4ebd0, 0x000001deaf92b160, 0x000001deaf929720, 0x000001deaf92f300,
0x000001deaf92c510, 0x000001deaf92aad0, 0x000001deaf92be80, 0x000001deaf92f990,
0x000001deaf92cba0, 0x000001deaf930020, 0x000001deaf92d230, 0x000001deaf92d8c0,
0x000001deaf9306b0, 0x000001deaf92df50, 0x000001deaf929db0, 0x000001deaf930d40,
0x000001deaf92e5e0, 0x000001deaf92a440, 0x000001deaf92b7f0, 0x000001deaf92ec70,
0x000001debf251340, 0x000001debf2526f0, 0x000001debf24f900, 0x000001debf252d80,
0x000001debf2519d0, 0x000001debf250cb0, 0x000001debf24ebe0, 0x000001debf253410,
0x000001debf252060, 0x000001debf253aa0, 0x000001debf254130, 0x000001debf2547c0,
0x000001debf24f270, 0x000001debf254e50, 0x000001debf24ff90, 0x000001debf24dec0,
0x000001debf2554e0, 0x000001debf24e550, 0x000001debf250620, 0x000001deba1be500,
0x000001deba1c05d0, 0x000001deba1bb080, 0x000001deba1c0c60, 0x000001deba1bd150,
0x000001deba1ba9f0, 0x000001deba1bff40, 0x000001deba1bbda0, 0x000001deba1bd7e0,
0x000001deba1bde70, 0x000001deba1c12f0, 0x000001deba1bf220, 0x000001deba1bc430,
0x000001deba1beb90, 0x000001deba1bf8b0, 0x000001deba1bcac0, 0x000001deba1bb710,
0x000001deba1ba360, 0x000001deba1c1980, 0x000001deb8fa6ed0, 0x000001deb8fad7d0,
0x000001deb8fae4f0, 0x000001deb8fa9cc0, 0x000001deb8fac420, 0x000001deb8faa350,
0x000001deb8faa9e0, 0x000001deb8fab070, 0x000001deb8fade60, 0x000001deb8fa7560,
0x000001deb8fab700, 0x000001deb8fa8280, 0x000001deb8fabd90, 0x000001deb8facab0,
0x000001deb8fad140, 0x000001deb8fa7bf0, 0x000001deb8fa9630, 0x000001deb8faeb80,
0x000001deb8faf210, 0x000001deb8faf8a0, 0x000001deb8fa8910, 0x000001deb8faff30,
0x000001deb8fa8fa0, 0x000001deb8fb05c0, 0x000001deb8fb2000, 0x000001deb8fb4760,
0x000001deb8fb0c50, 0x000001deb8fb2690, 0x000001deb8fb12e0, 0x000001deb8fb2d20,
0x000001deb8fb1970, 0x000001deb8fb61a0, 0x000001deb8fb33b0, 0x000001deb8fb3a40,
0x000001deb8fb40d0, 0x000001deb98c7900, 0x000001deb98cdb70, 0x000001deb98cad80,
0x000001deb98cb410, 0x000001deb98cf5b0, 0x000001deb98cbaa0
}

Java Threads: ( => current thread )
  0x000001defc6595d0 JavaThread "main"                              [_thread_blocked, id=24904, stack(0x000000c57a100000,0x000000c57a200000) (1024K)]
  0x000001deaf707430 JavaThread "Reference Handler"          daemon [_thread_blocked, id=18400, stack(0x000000c57a900000,0x000000c57aa00000) (1024K)]
  0x000001deaf6d4360 JavaThread "Finalizer"                  daemon [_thread_blocked, id=66952, stack(0x000000c57aa00000,0x000000c57ab00000) (1024K)]
  0x000001deaf713440 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=41372, stack(0x000000c57ab00000,0x000000c57ac00000) (1024K)]
  0x000001deaf712db0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=38048, stack(0x000000c57ac00000,0x000000c57ad00000) (1024K)]
  0x000001deaf712090 JavaThread "Service Thread"             daemon [_thread_blocked, id=30516, stack(0x000000c57ad00000,0x000000c57ae00000) (1024K)]
  0x000001deaf70ffc0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=55968, stack(0x000000c57ae00000,0x000000c57af00000) (1024K)]
=>0x000001deaf714ea0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=27372, stack(0x000000c57af00000,0x000000c57b000000) (1024K)]
  0x000001deaf7160d0 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=40668, stack(0x000000c57b000000,0x000000c57b100000) (1024K)]
  0x000001deaf711370 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=5176, stack(0x000000c57b100000,0x000000c57b200000) (1024K)]
  0x000001deaf710650 JavaThread "Notification Thread"        daemon [_thread_blocked, id=49148, stack(0x000000c57b300000,0x000000c57b400000) (1024K)]
  0x000001deaf710ce0 JavaThread "Daemon health stats"               [_thread_blocked, id=55568, stack(0x000000c57ba00000,0x000000c57bb00000) (1024K)]
  0x000001deaf712720 JavaThread "Incoming local TCP Connector on port 51713"        [_thread_in_native, id=66452, stack(0x000000c57bf00000,0x000000c57c000000) (1024K)]
  0x000001deaf711a00 JavaThread "Daemon periodic checks"            [_thread_blocked, id=62028, stack(0x000000c57c000000,0x000000c57c100000) (1024K)]
  0x000001deb59aca40 JavaThread "Daemon"                            [_thread_blocked, id=14868, stack(0x000000c57c100000,0x000000c57c200000) (1024K)]
  0x000001deb59a9c50 JavaThread "Handler for socket connection from /127.0.0.1:51713 to /127.0.0.1:51714"        [_thread_in_native, id=66716, stack(0x000000c57c200000,0x000000c57c300000) (1024K)]
  0x000001deb59ab000 JavaThread "Cancel handler"                    [_thread_blocked, id=20296, stack(0x000000c57c300000,0x000000c57c400000) (1024K)]
  0x000001deb59ac3b0 JavaThread "Daemon worker"                     [_thread_blocked, id=67420, stack(0x000000c57c400000,0x000000c57c500000) (1024K)]
  0x000001deb59ad760 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51713 to /127.0.0.1:51714"        [_thread_in_vm, id=16032, stack(0x000000c57c500000,0x000000c57c600000) (1024K)]
  0x000001deb59ad0d0 JavaThread "Stdin handler"                     [_thread_blocked, id=40764, stack(0x000000c57c600000,0x000000c57c700000) (1024K)]
  0x000001deb59aa2e0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=57748, stack(0x000000c57c700000,0x000000c57c800000) (1024K)]
  0x000001deb59ab690 JavaThread "Cache worker for journal cache (D:\Dev\Env\repo\caches\journal-1)"        [_thread_blocked, id=4452, stack(0x000000c57cd00000,0x000000c57ce00000) (1024K)]
  0x000001deb59abd20 JavaThread "File lock request listener"        [_thread_in_native, id=61704, stack(0x000000c57ce00000,0x000000c57cf00000) (1024K)]
  0x000001deb59addf0 JavaThread "Cache worker for file hash cache (D:\Dev\Env\repo\caches\8.12\fileHashes)"        [_thread_blocked, id=49240, stack(0x000000c57cf00000,0x000000c57d000000) (1024K)]
  0x000001deb59a88a0 JavaThread "Cache worker for file hash cache (D:\Dev\OtherProjects\aes\my_tools\android\.gradle\8.12\fileHashes)"        [_thread_blocked, id=8360, stack(0x000000c57d000000,0x000000c57d100000) (1024K)]
  0x000001deb59a7b80 JavaThread "Cache worker for Build Output Cleanup Cache (D:\Dev\OtherProjects\aes\my_tools\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=4692, stack(0x000000c57d100000,0x000000c57d200000) (1024K)]
  0x000001deb59aa970 JavaThread "File watcher server"        daemon [_thread_in_native, id=1832, stack(0x000000c57d200000,0x000000c57d300000) (1024K)]
  0x000001deb59ae480 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=27196, stack(0x000000c57d300000,0x000000c57d400000) (1024K)]
  0x000001deb59af1a0 JavaThread "Cache worker for checksums cache (D:\Dev\OtherProjects\aes\my_tools\android\.gradle\8.12\checksums)"        [_thread_blocked, id=59140, stack(0x000000c57d500000,0x000000c57d600000) (1024K)]
  0x000001deb59a8210 JavaThread "Cache worker for file content cache (D:\Dev\Env\repo\caches\8.12\fileContent)"        [_thread_blocked, id=47848, stack(0x000000c57d600000,0x000000c57d700000) (1024K)]
  0x000001deb59a8f30 JavaThread "Cache worker for cache directory md-rule (D:\Dev\Env\repo\caches\8.12\md-rule)"        [_thread_blocked, id=1828, stack(0x000000c57d700000,0x000000c57d800000) (1024K)]
  0x000001deb59a95c0 JavaThread "Cache worker for cache directory md-supplier (D:\Dev\Env\repo\caches\8.12\md-supplier)"        [_thread_blocked, id=50520, stack(0x000000c57d800000,0x000000c57d900000) (1024K)]
  0x000001deb86815c0 JavaThread "Cache worker for Build Output Cleanup Cache (D:\Dev\Env\flutter\packages\flutter_tools\gradle\.gradle\buildOutputCleanup)"        [_thread_blocked, id=38420, stack(0x000000c57d900000,0x000000c57da00000) (1024K)]
  0x000001deb8681c50 JavaThread "Unconstrained build operations"        [_thread_blocked, id=53156, stack(0x000000c57da00000,0x000000c57db00000) (1024K)]
  0x000001deb8682970 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=66312, stack(0x000000c57db00000,0x000000c57dc00000) (1024K)]
  0x000001deb867fb80 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=53504, stack(0x000000c57dc00000,0x000000c57dd00000) (1024K)]
  0x000001deb8685760 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=42488, stack(0x000000c57dd00000,0x000000c57de00000) (1024K)]
  0x000001deb86822e0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=12000, stack(0x000000c57de00000,0x000000c57df00000) (1024K)]
  0x000001deb8683d20 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=54936, stack(0x000000c57df00000,0x000000c57e000000) (1024K)]
  0x000001deb8685df0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=36664, stack(0x000000c57e000000,0x000000c57e100000) (1024K)]
  0x000001deb8680f30 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=48068, stack(0x000000c57e100000,0x000000c57e200000) (1024K)]
  0x000001deb8683690 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=67040, stack(0x000000c57e200000,0x000000c57e300000) (1024K)]
  0x000001deb86843b0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=19888, stack(0x000000c57e300000,0x000000c57e400000) (1024K)]
  0x000001deb8683000 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=16308, stack(0x000000c57e400000,0x000000c57e500000) (1024K)]
  0x000001deb86850d0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=30396, stack(0x000000c57e500000,0x000000c57e600000) (1024K)]
  0x000001deb8686480 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=61168, stack(0x000000c57e600000,0x000000c57e700000) (1024K)]
  0x000001deb867f4f0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=57528, stack(0x000000c57e700000,0x000000c57e800000) (1024K)]
  0x000001deb8686b10 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=14476, stack(0x000000c57e800000,0x000000c57e900000) (1024K)]
  0x000001deb8680210 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=37820, stack(0x000000c57e900000,0x000000c57ea00000) (1024K)]
  0x000001deb86808a0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=52684, stack(0x000000c57ea00000,0x000000c57eb00000) (1024K)]
  0x000001deb93782d0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=42068, stack(0x000000c57eb00000,0x000000c57ec00000) (1024K)]
  0x000001deb9373410 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=65564, stack(0x000000c57ec00000,0x000000c57ed00000) (1024K)]
  0x000001deb9376f20 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=29728, stack(0x000000c57ed00000,0x000000c57ee00000) (1024K)]
  0x000001deb9376890 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=60216, stack(0x000000c57ee00000,0x000000c57ef00000) (1024K)]
  0x000001deb9373aa0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=50692, stack(0x000000c57ef00000,0x000000c57f000000) (1024K)]
  0x000001deb93754e0 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=28064, stack(0x000000c57f000000,0x000000c57f100000) (1024K)]
  0x000001deb9372d80 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=27000, stack(0x000000c57f100000,0x000000c57f200000) (1024K)]
  0x000001deb9375b70 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=5440, stack(0x000000c57f200000,0x000000c57f300000) (1024K)]
  0x000001deb9374130 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=6032, stack(0x000000c57f300000,0x000000c57f400000) (1024K)]
  0x000001deb93747c0 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=54044, stack(0x000000c57f400000,0x000000c57f500000) (1024K)]
  0x000001deb93775b0 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=65856, stack(0x000000c57f500000,0x000000c57f600000) (1024K)]
  0x000001deb9374e50 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=33284, stack(0x000000c57f600000,0x000000c57f700000) (1024K)]
  0x000001deb9378ff0 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=17568, stack(0x000000c57f700000,0x000000c57f800000) (1024K)]
  0x000001deb9376200 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=65948, stack(0x000000c57f800000,0x000000c57f900000) (1024K)]
  0x000001deb9378960 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=52208, stack(0x000000c57f900000,0x000000c57fa00000) (1024K)]
  0x000001deb9377c40 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=66228, stack(0x000000c57fa00000,0x000000c57fb00000) (1024K)]
  0x000001deb9372060 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=35612, stack(0x000000c57fb00000,0x000000c57fc00000) (1024K)]
  0x000001deb9379680 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=36908, stack(0x000000c57fc00000,0x000000c57fd00000) (1024K)]
  0x000001deb93726f0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=56576, stack(0x000000c57fd00000,0x000000c57fe00000) (1024K)]
  0x000001deb82c3620 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=66976, stack(0x000000c57fe00000,0x000000c57ff00000) (1024K)]
  0x000001deb82c3cb0 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=47948, stack(0x000000c57ff00000,0x000000c580000000) (1024K)]
  0x000001deb82c5060 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=2348, stack(0x000000c500000000,0x000000c500100000) (1024K)]
  0x000001deb82c4340 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=59344, stack(0x000000c500100000,0x000000c500200000) (1024K)]
  0x000001deb82c1be0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=55604, stack(0x000000c500200000,0x000000c500300000) (1024K)]
  0x000001deb82c2270 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=10452, stack(0x000000c500300000,0x000000c500400000) (1024K)]
  0x000001deb82c49d0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=10700, stack(0x000000c500400000,0x000000c500500000) (1024K)]
  0x000001deb82c6aa0 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=2876, stack(0x000000c500500000,0x000000c500600000) (1024K)]
  0x000001deb82c6410 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=50720, stack(0x000000c500600000,0x000000c500700000) (1024K)]
  0x000001deb82c7e50 JavaThread "Memory manager"                    [_thread_blocked, id=65452, stack(0x000000c500700000,0x000000c500800000) (1024K)]
  0x000001deb82c56f0 JavaThread "build event listener"              [_thread_blocked, id=24268, stack(0x000000c500b00000,0x000000c500c00000) (1024K)]
  0x000001deb8b4f260 JavaThread "included builds"                   [_thread_blocked, id=51248, stack(0x000000c500e00000,0x000000c500f00000) (1024K)]
  0x000001deb8b50610 JavaThread "Execution worker"                  [_thread_blocked, id=41052, stack(0x000000c500f00000,0x000000c501000000) (1024K)]
  0x000001deb8b52050 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=1908, stack(0x000000c501000000,0x000000c501100000) (1024K)]
  0x000001deb8b4deb0 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=55644, stack(0x000000c501100000,0x000000c501200000) (1024K)]
  0x000001deb8b50ca0 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=21024, stack(0x000000c501200000,0x000000c501300000) (1024K)]
  0x000001deb8b51330 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=29188, stack(0x000000c501300000,0x000000c501400000) (1024K)]
  0x000001deb8b526e0 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=22248, stack(0x000000c501400000,0x000000c501500000) (1024K)]
  0x000001deb8b519c0 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=30584, stack(0x000000c501500000,0x000000c501600000) (1024K)]
  0x000001deb8b52d70 JavaThread "Execution worker Thread 8"         [_thread_blocked, id=51840, stack(0x000000c501600000,0x000000c501700000) (1024K)]
  0x000001deb8b4bde0 JavaThread "Execution worker Thread 9"         [_thread_blocked, id=42700, stack(0x000000c501700000,0x000000c501800000) (1024K)]
  0x000001deb8b4e540 JavaThread "Execution worker Thread 10"        [_thread_blocked, id=35236, stack(0x000000c501800000,0x000000c501900000) (1024K)]
  0x000001deb8b53400 JavaThread "Execution worker Thread 11"        [_thread_blocked, id=52096, stack(0x000000c501900000,0x000000c501a00000) (1024K)]
  0x000001deb8b4c470 JavaThread "Execution worker Thread 12"        [_thread_blocked, id=59004, stack(0x000000c501a00000,0x000000c501b00000) (1024K)]
  0x000001deb8b4d190 JavaThread "Execution worker Thread 13"        [_thread_blocked, id=55840, stack(0x000000c501b00000,0x000000c501c00000) (1024K)]
  0x000001deb8b4cb00 JavaThread "Execution worker Thread 14"        [_thread_blocked, id=57244, stack(0x000000c501c00000,0x000000c501d00000) (1024K)]
  0x000001deb8b4d820 JavaThread "Execution worker Thread 15"        [_thread_blocked, id=5024, stack(0x000000c501d00000,0x000000c501e00000) (1024K)]
  0x000001deb8b4ebd0 JavaThread "Cache worker for execution history cache (D:\Dev\Env\flutter\packages\flutter_tools\gradle\.gradle\8.12\executionHistory)"        [_thread_blocked, id=9136, stack(0x000000c501e00000,0x000000c501f00000) (1024K)]
  0x000001deaf92b160 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=51868, stack(0x000000c501f00000,0x000000c502000000) (1024K)]
  0x000001deaf929720 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=30916, stack(0x000000c502000000,0x000000c502100000) (1024K)]
  0x000001deaf92f300 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=26152, stack(0x000000c502100000,0x000000c502200000) (1024K)]
  0x000001deaf92c510 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=63224, stack(0x000000c502200000,0x000000c502300000) (1024K)]
  0x000001deaf92aad0 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=21056, stack(0x000000c502300000,0x000000c502400000) (1024K)]
  0x000001deaf92be80 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=57684, stack(0x000000c502400000,0x000000c502500000) (1024K)]
  0x000001deaf92f990 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=56760, stack(0x000000c502500000,0x000000c502600000) (1024K)]
  0x000001deaf92cba0 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=54208, stack(0x000000c502600000,0x000000c502700000) (1024K)]
  0x000001deaf930020 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=50108, stack(0x000000c502700000,0x000000c502800000) (1024K)]
  0x000001deaf92d230 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=1400, stack(0x000000c502800000,0x000000c502900000) (1024K)]
  0x000001deaf92d8c0 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=63028, stack(0x000000c502900000,0x000000c502a00000) (1024K)]
  0x000001deaf9306b0 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=37300, stack(0x000000c502a00000,0x000000c502b00000) (1024K)]
  0x000001deaf92df50 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=30144, stack(0x000000c502b00000,0x000000c502c00000) (1024K)]
  0x000001deaf929db0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=6640, stack(0x000000c502c00000,0x000000c502d00000) (1024K)]
  0x000001deaf930d40 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=66676, stack(0x000000c502d00000,0x000000c502e00000) (1024K)]
  0x000001deaf92e5e0 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=60260, stack(0x000000c502e00000,0x000000c502f00000) (1024K)]
  0x000001deaf92a440 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=57788, stack(0x000000c502f00000,0x000000c503000000) (1024K)]
  0x000001deaf92b7f0 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=65160, stack(0x000000c503000000,0x000000c503100000) (1024K)]
  0x000001deaf92ec70 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=20536, stack(0x000000c503100000,0x000000c503200000) (1024K)]
  0x000001debf251340 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=57924, stack(0x000000c503200000,0x000000c503300000) (1024K)]
  0x000001debf2526f0 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=57552, stack(0x000000c503300000,0x000000c503400000) (1024K)]
  0x000001debf24f900 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=53632, stack(0x000000c503400000,0x000000c503500000) (1024K)]
  0x000001debf252d80 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=49304, stack(0x000000c503500000,0x000000c503600000) (1024K)]
  0x000001debf2519d0 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=54920, stack(0x000000c503600000,0x000000c503700000) (1024K)]
  0x000001debf250cb0 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=25412, stack(0x000000c503700000,0x000000c503800000) (1024K)]
  0x000001debf24ebe0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=27812, stack(0x000000c503800000,0x000000c503900000) (1024K)]
  0x000001debf253410 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=48644, stack(0x000000c503900000,0x000000c503a00000) (1024K)]
  0x000001debf252060 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=56108, stack(0x000000c503a00000,0x000000c503b00000) (1024K)]
  0x000001debf253aa0 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=29888, stack(0x000000c503b00000,0x000000c503c00000) (1024K)]
  0x000001debf254130 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=52912, stack(0x000000c503c00000,0x000000c503d00000) (1024K)]
  0x000001debf2547c0 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=18936, stack(0x000000c503d00000,0x000000c503e00000) (1024K)]
  0x000001debf24f270 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=58064, stack(0x000000c503e00000,0x000000c503f00000) (1024K)]
  0x000001debf254e50 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=37892, stack(0x000000c503f00000,0x000000c504000000) (1024K)]
  0x000001debf24ff90 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=66656, stack(0x000000c504000000,0x000000c504100000) (1024K)]
  0x000001debf24dec0 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=53836, stack(0x000000c504100000,0x000000c504200000) (1024K)]
  0x000001debf2554e0 JavaThread "Unconstrained build operations Thread 81"        [_thread_blocked, id=4312, stack(0x000000c504200000,0x000000c504300000) (1024K)]
  0x000001debf24e550 JavaThread "Unconstrained build operations Thread 82"        [_thread_blocked, id=29096, stack(0x000000c504300000,0x000000c504400000) (1024K)]
  0x000001debf250620 JavaThread "Unconstrained build operations Thread 83"        [_thread_blocked, id=39400, stack(0x000000c504400000,0x000000c504500000) (1024K)]
  0x000001deba1be500 JavaThread "Unconstrained build operations Thread 84"        [_thread_blocked, id=64748, stack(0x000000c504500000,0x000000c504600000) (1024K)]
  0x000001deba1c05d0 JavaThread "Unconstrained build operations Thread 85"        [_thread_blocked, id=32200, stack(0x000000c504600000,0x000000c504700000) (1024K)]
  0x000001deba1bb080 JavaThread "Unconstrained build operations Thread 86"        [_thread_blocked, id=57992, stack(0x000000c504700000,0x000000c504800000) (1024K)]
  0x000001deba1c0c60 JavaThread "Unconstrained build operations Thread 87"        [_thread_blocked, id=65064, stack(0x000000c504800000,0x000000c504900000) (1024K)]
  0x000001deba1bd150 JavaThread "Unconstrained build operations Thread 88"        [_thread_blocked, id=46512, stack(0x000000c504900000,0x000000c504a00000) (1024K)]
  0x000001deba1ba9f0 JavaThread "Unconstrained build operations Thread 89"        [_thread_blocked, id=16732, stack(0x000000c504a00000,0x000000c504b00000) (1024K)]
  0x000001deba1bff40 JavaThread "Unconstrained build operations Thread 90"        [_thread_blocked, id=62816, stack(0x000000c504b00000,0x000000c504c00000) (1024K)]
  0x000001deba1bbda0 JavaThread "Unconstrained build operations Thread 91"        [_thread_blocked, id=22288, stack(0x000000c504c00000,0x000000c504d00000) (1024K)]
  0x000001deba1bd7e0 JavaThread "Unconstrained build operations Thread 92"        [_thread_blocked, id=44940, stack(0x000000c504d00000,0x000000c504e00000) (1024K)]
  0x000001deba1bde70 JavaThread "Unconstrained build operations Thread 93"        [_thread_blocked, id=55488, stack(0x000000c504e00000,0x000000c504f00000) (1024K)]
  0x000001deba1c12f0 JavaThread "Unconstrained build operations Thread 94"        [_thread_blocked, id=14232, stack(0x000000c504f00000,0x000000c505000000) (1024K)]
  0x000001deba1bf220 JavaThread "Unconstrained build operations Thread 95"        [_thread_blocked, id=62680, stack(0x000000c505000000,0x000000c505100000) (1024K)]
  0x000001deba1bc430 JavaThread "Unconstrained build operations Thread 96"        [_thread_blocked, id=60776, stack(0x000000c505100000,0x000000c505200000) (1024K)]
  0x000001deba1beb90 JavaThread "Unconstrained build operations Thread 97"        [_thread_blocked, id=38764, stack(0x000000c505200000,0x000000c505300000) (1024K)]
  0x000001deba1bf8b0 JavaThread "Unconstrained build operations Thread 98"        [_thread_blocked, id=28780, stack(0x000000c505300000,0x000000c505400000) (1024K)]
  0x000001deba1bcac0 JavaThread "Unconstrained build operations Thread 99"        [_thread_blocked, id=25716, stack(0x000000c505400000,0x000000c505500000) (1024K)]
  0x000001deba1bb710 JavaThread "Unconstrained build operations Thread 100"        [_thread_blocked, id=64292, stack(0x000000c505500000,0x000000c505600000) (1024K)]
  0x000001deba1ba360 JavaThread "Unconstrained build operations Thread 101"        [_thread_blocked, id=61488, stack(0x000000c505600000,0x000000c505700000) (1024K)]
  0x000001deba1c1980 JavaThread "Unconstrained build operations Thread 102"        [_thread_blocked, id=47588, stack(0x000000c505700000,0x000000c505800000) (1024K)]
  0x000001deb8fa6ed0 JavaThread "Unconstrained build operations Thread 103"        [_thread_blocked, id=43800, stack(0x000000c505800000,0x000000c505900000) (1024K)]
  0x000001deb8fad7d0 JavaThread "Unconstrained build operations Thread 104"        [_thread_blocked, id=58872, stack(0x000000c505900000,0x000000c505a00000) (1024K)]
  0x000001deb8fae4f0 JavaThread "Unconstrained build operations Thread 105"        [_thread_blocked, id=3916, stack(0x000000c505a00000,0x000000c505b00000) (1024K)]
  0x000001deb8fa9cc0 JavaThread "Unconstrained build operations Thread 106"        [_thread_blocked, id=63552, stack(0x000000c505b00000,0x000000c505c00000) (1024K)]
  0x000001deb8fac420 JavaThread "Unconstrained build operations Thread 107"        [_thread_blocked, id=52880, stack(0x000000c505c00000,0x000000c505d00000) (1024K)]
  0x000001deb8faa350 JavaThread "Unconstrained build operations Thread 108"        [_thread_blocked, id=62836, stack(0x000000c505d00000,0x000000c505e00000) (1024K)]
  0x000001deb8faa9e0 JavaThread "Unconstrained build operations Thread 109"        [_thread_blocked, id=39572, stack(0x000000c505e00000,0x000000c505f00000) (1024K)]
  0x000001deb8fab070 JavaThread "Unconstrained build operations Thread 110"        [_thread_blocked, id=29156, stack(0x000000c505f00000,0x000000c506000000) (1024K)]
  0x000001deb8fade60 JavaThread "Unconstrained build operations Thread 111"        [_thread_blocked, id=2448, stack(0x000000c506000000,0x000000c506100000) (1024K)]
  0x000001deb8fa7560 JavaThread "Unconstrained build operations Thread 112"        [_thread_blocked, id=64124, stack(0x000000c506100000,0x000000c506200000) (1024K)]
  0x000001deb8fab700 JavaThread "Unconstrained build operations Thread 113"        [_thread_blocked, id=45372, stack(0x000000c506200000,0x000000c506300000) (1024K)]
  0x000001deb8fa8280 JavaThread "Unconstrained build operations Thread 114"        [_thread_blocked, id=15872, stack(0x000000c506300000,0x000000c506400000) (1024K)]
  0x000001deb8fabd90 JavaThread "Unconstrained build operations Thread 115"        [_thread_blocked, id=21996, stack(0x000000c506400000,0x000000c506500000) (1024K)]
  0x000001deb8facab0 JavaThread "Unconstrained build operations Thread 116"        [_thread_blocked, id=44704, stack(0x000000c506500000,0x000000c506600000) (1024K)]
  0x000001deb8fad140 JavaThread "Unconstrained build operations Thread 117"        [_thread_blocked, id=58948, stack(0x000000c506600000,0x000000c506700000) (1024K)]
  0x000001deb8fa7bf0 JavaThread "Unconstrained build operations Thread 118"        [_thread_blocked, id=66132, stack(0x000000c506700000,0x000000c506800000) (1024K)]
  0x000001deb8fa9630 JavaThread "Unconstrained build operations Thread 119"        [_thread_blocked, id=42840, stack(0x000000c506800000,0x000000c506900000) (1024K)]
  0x000001deb8faeb80 JavaThread "Unconstrained build operations Thread 120"        [_thread_blocked, id=12048, stack(0x000000c506900000,0x000000c506a00000) (1024K)]
  0x000001deb8faf210 JavaThread "Unconstrained build operations Thread 121"        [_thread_blocked, id=18864, stack(0x000000c506a00000,0x000000c506b00000) (1024K)]
  0x000001deb8faf8a0 JavaThread "Unconstrained build operations Thread 122"        [_thread_blocked, id=47896, stack(0x000000c506b00000,0x000000c506c00000) (1024K)]
  0x000001deb8fa8910 JavaThread "Unconstrained build operations Thread 123"        [_thread_blocked, id=50264, stack(0x000000c506c00000,0x000000c506d00000) (1024K)]
  0x000001deb8faff30 JavaThread "Unconstrained build operations Thread 124"        [_thread_blocked, id=34692, stack(0x000000c506d00000,0x000000c506e00000) (1024K)]
  0x000001deb8fa8fa0 JavaThread "Unconstrained build operations Thread 125"        [_thread_blocked, id=67568, stack(0x000000c506e00000,0x000000c506f00000) (1024K)]
  0x000001deb8fb05c0 JavaThread "Unconstrained build operations Thread 126"        [_thread_blocked, id=67560, stack(0x000000c506f00000,0x000000c507000000) (1024K)]
  0x000001deb8fb2000 JavaThread "Unconstrained build operations Thread 127"        [_thread_blocked, id=58116, stack(0x000000c507000000,0x000000c507100000) (1024K)]
  0x000001deb8fb4760 JavaThread "Unconstrained build operations Thread 128"        [_thread_blocked, id=3856, stack(0x000000c507100000,0x000000c507200000) (1024K)]
  0x000001deb8fb0c50 JavaThread "Unconstrained build operations Thread 129"        [_thread_blocked, id=48120, stack(0x000000c507200000,0x000000c507300000) (1024K)]
  0x000001deb8fb2690 JavaThread "Unconstrained build operations Thread 130"        [_thread_blocked, id=41716, stack(0x000000c507300000,0x000000c507400000) (1024K)]
  0x000001deb8fb12e0 JavaThread "Unconstrained build operations Thread 131"        [_thread_blocked, id=58600, stack(0x000000c507400000,0x000000c507500000) (1024K)]
  0x000001deb8fb2d20 JavaThread "Unconstrained build operations Thread 132"        [_thread_blocked, id=34244, stack(0x000000c507500000,0x000000c507600000) (1024K)]
  0x000001deb8fb1970 JavaThread "Unconstrained build operations Thread 133"        [_thread_blocked, id=10084, stack(0x000000c507600000,0x000000c507700000) (1024K)]
  0x000001deb8fb61a0 JavaThread "Unconstrained build operations Thread 134"        [_thread_blocked, id=55236, stack(0x000000c507700000,0x000000c507800000) (1024K)]
  0x000001deb8fb33b0 JavaThread "Unconstrained build operations Thread 135"        [_thread_blocked, id=40820, stack(0x000000c507800000,0x000000c507900000) (1024K)]
  0x000001deb8fb3a40 JavaThread "Unconstrained build operations Thread 136"        [_thread_blocked, id=4716, stack(0x000000c507900000,0x000000c507a00000) (1024K)]
  0x000001deb8fb40d0 JavaThread "Unconstrained build operations Thread 137"        [_thread_blocked, id=46256, stack(0x000000c507a00000,0x000000c507b00000) (1024K)]
  0x000001deb98c7900 JavaThread "build event listener"              [_thread_blocked, id=36964, stack(0x000000c500a00000,0x000000c500b00000) (1024K)]
  0x000001deb98cdb70 JavaThread "Problems report writer"            [_thread_blocked, id=28956, stack(0x000000c507b00000,0x000000c507c00000) (1024K)]
  0x000001deb98cad80 JavaThread "Cache worker for execution history cache (D:\Dev\OtherProjects\aes\my_tools\android\.gradle\8.12\executionHistory)"        [_thread_blocked, id=1804, stack(0x000000c500900000,0x000000c500a00000) (1024K)]
  0x000001deb98cb410 JavaThread "Exec process"                      [_thread_in_native, id=50908, stack(0x000000c507c00000,0x000000c507d00000) (1024K)]
  0x000001deb98cf5b0 JavaThread "Exec process Thread 2"             [_thread_in_vm, id=55388, stack(0x000000c507d00000,0x000000c507e00000) (1024K)]
  0x000001deb98cbaa0 JavaThread "Exec process Thread 3"             [_thread_in_native, id=45856, stack(0x000000c507e00000,0x000000c507f00000) (1024K)]
Total: 195

Other Threads:
  0x000001deaf6a7820 VMThread "VM Thread"                           [id=24980, stack(0x000000c57a800000,0x000000c57a900000) (1024K)]
  0x000001deff61c0f0 WatcherThread "VM Periodic Task Thread"        [id=30428, stack(0x000000c57a700000,0x000000c57a800000) (1024K)]
  0x000001defe8e0420 WorkerThread "GC Thread#0"                     [id=51512, stack(0x000000c57a200000,0x000000c57a300000) (1024K)]
  0x000001deb4102ef0 WorkerThread "GC Thread#1"                     [id=46404, stack(0x000000c57b400000,0x000000c57b500000) (1024K)]
  0x000001deb4103290 WorkerThread "GC Thread#2"                     [id=61768, stack(0x000000c57b500000,0x000000c57b600000) (1024K)]
  0x000001deb419b220 WorkerThread "GC Thread#3"                     [id=20912, stack(0x000000c57b600000,0x000000c57b700000) (1024K)]
  0x000001deb419b5c0 WorkerThread "GC Thread#4"                     [id=28840, stack(0x000000c57b700000,0x000000c57b800000) (1024K)]
  0x000001deb419b960 WorkerThread "GC Thread#5"                     [id=16952, stack(0x000000c57b800000,0x000000c57b900000) (1024K)]
  0x000001deb582fe30 WorkerThread "GC Thread#6"                     [id=57024, stack(0x000000c57bb00000,0x000000c57bc00000) (1024K)]
  0x000001deb58d4760 WorkerThread "GC Thread#7"                     [id=65708, stack(0x000000c57bc00000,0x000000c57bd00000) (1024K)]
  0x000001deb59426b0 WorkerThread "GC Thread#8"                     [id=61580, stack(0x000000c57bd00000,0x000000c57be00000) (1024K)]
  0x000001deb5941bd0 WorkerThread "GC Thread#9"                     [id=10504, stack(0x000000c57be00000,0x000000c57bf00000) (1024K)]
  0x000001deb5942a50 WorkerThread "GC Thread#10"                    [id=31896, stack(0x000000c57c800000,0x000000c57c900000) (1024K)]
  0x000001deb5942df0 WorkerThread "GC Thread#11"                    [id=56264, stack(0x000000c57c900000,0x000000c57ca00000) (1024K)]
  0x000001deb5941830 WorkerThread "GC Thread#12"                    [id=51456, stack(0x000000c57ca00000,0x000000c57cb00000) (1024K)]
  0x000001deff468c50 ConcurrentGCThread "G1 Main Marker"            [id=13244, stack(0x000000c57a300000,0x000000c57a400000) (1024K)]
  0x000001deff469750 WorkerThread "G1 Conc#0"                       [id=5988, stack(0x000000c57a400000,0x000000c57a500000) (1024K)]
  0x000001deb5943190 WorkerThread "G1 Conc#1"                       [id=43436, stack(0x000000c57cb00000,0x000000c57cc00000) (1024K)]
  0x000001deb5942310 WorkerThread "G1 Conc#2"                       [id=18964, stack(0x000000c57cc00000,0x000000c57cd00000) (1024K)]
  0x000001deff4e6d40 ConcurrentGCThread "G1 Refine#0"               [id=41712, stack(0x000000c57a500000,0x000000c57a600000) (1024K)]
  0x000001deff4e76c0 ConcurrentGCThread "G1 Service"                [id=23048, stack(0x000000c57a600000,0x000000c57a700000) (1024K)]
Total: 21

Threads with active compile tasks:
C2 CompilerThread0  28988 14361       4       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput$1::execute (9 bytes)
C1 CompilerThread0  28989 14373       3       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput$StateContext::flushLineText (113 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd61cfc1a0] Metaspace_lock - owner thread: 0x000001deb59ad760

OutOfMemory and StackOverflow Exception counts:
StackOverflowErrors=1
LinkageErrors=217

Heap address: 0x0000000600000000, size: 8192 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 16 total, 16 available
 Memory: 16228M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 8G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 217088K, used 118820K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 3 survivors (12288K)
 Metaspace       used 123319K, committed 125568K, reserved 1179648K
  class space    used 16974K, committed 18112K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000600000000, 0x0000000600400000, 0x0000000600400000|100%|HS|  |TAMS 0x0000000600000000| PB 0x0000000600000000| Complete 
|   1|0x0000000600400000, 0x0000000600800000, 0x0000000600800000|100%| O|  |TAMS 0x0000000600400000| PB 0x0000000600400000| Untracked 
|   2|0x0000000600800000, 0x0000000600c00000, 0x0000000600c00000|100%| O|  |TAMS 0x0000000600800000| PB 0x0000000600800000| Untracked 
|   3|0x0000000600c00000, 0x0000000601000000, 0x0000000601000000|100%| O|  |TAMS 0x0000000600c00000| PB 0x0000000600c00000| Untracked 
|   4|0x0000000601000000, 0x0000000601400000, 0x0000000601400000|100%| O|  |TAMS 0x0000000601000000| PB 0x0000000601000000| Untracked 
|   5|0x0000000601400000, 0x0000000601800000, 0x0000000601800000|100%| O|  |TAMS 0x0000000601400000| PB 0x0000000601400000| Untracked 
|   6|0x0000000601800000, 0x0000000601c00000, 0x0000000601c00000|100%| O|  |TAMS 0x0000000601800000| PB 0x0000000601800000| Untracked 
|   7|0x0000000601c00000, 0x0000000602000000, 0x0000000602000000|100%| O|  |TAMS 0x0000000601c00000| PB 0x0000000601c00000| Untracked 
|   8|0x0000000602000000, 0x0000000602400000, 0x0000000602400000|100%| O|  |TAMS 0x0000000602000000| PB 0x0000000602000000| Untracked 
|   9|0x0000000602400000, 0x0000000602800000, 0x0000000602800000|100%|HS|  |TAMS 0x0000000602400000| PB 0x0000000602400000| Complete 
|  10|0x0000000602800000, 0x0000000602c00000, 0x0000000602c00000|100%| O|  |TAMS 0x0000000602800000| PB 0x0000000602800000| Untracked 
|  11|0x0000000602c00000, 0x0000000603000000, 0x0000000603000000|100%|HS|  |TAMS 0x0000000602c00000| PB 0x0000000602c00000| Complete 
|  12|0x0000000603000000, 0x0000000603400000, 0x0000000603400000|100%| O|  |TAMS 0x0000000603000000| PB 0x0000000603000000| Untracked 
|  13|0x0000000603400000, 0x0000000603800000, 0x0000000603800000|100%| O|  |TAMS 0x0000000603400000| PB 0x0000000603400000| Untracked 
|  14|0x0000000603800000, 0x0000000603c00000, 0x0000000603c00000|100%| O|  |TAMS 0x0000000603800000| PB 0x0000000603800000| Untracked 
|  15|0x0000000603c00000, 0x0000000604000000, 0x0000000604000000|100%| O|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|  16|0x0000000604000000, 0x0000000604400000, 0x0000000604400000|100%| O|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|  17|0x0000000604400000, 0x0000000604800000, 0x0000000604800000|100%| O|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|  18|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%| O|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|  19|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| O|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|  20|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|  21|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|  22|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|  23|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|  24|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  25|0x0000000606400000, 0x0000000606800000, 0x0000000606800000|100%| O|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  26|0x0000000606800000, 0x0000000606c00000, 0x0000000606c00000|100%| O|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  27|0x0000000606c00000, 0x0000000606c21ab0, 0x0000000607000000|  3%| O|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  28|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  29|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  30|0x0000000607800000, 0x0000000607be7920, 0x0000000607c00000| 97%| S|CS|TAMS 0x0000000607800000| PB 0x0000000607800000| Complete 
|  31|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  32|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  33|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  34|0x0000000608800000, 0x0000000608c00000, 0x0000000608c00000|100%| S|CS|TAMS 0x0000000608800000| PB 0x0000000608800000| Complete 
|  35|0x0000000608c00000, 0x0000000609000000, 0x0000000609000000|100%| S|CS|TAMS 0x0000000608c00000| PB 0x0000000608c00000| Complete 
|  36|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  37|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  38|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  39|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  40|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  41|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  42|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  43|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  44|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  45|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  46|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  47|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  56|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  57|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  58|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  63|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|2047|0x00000007ffc00000, 0x00000007fff8d998, 0x0000000800000000| 88%| E|  |TAMS 0x00000007ffc00000| PB 0x00000007ffc00000| Complete 

Card table byte_map: [0x000001dea4090000,0x000001dea5090000] _byte_map_base: 0x000001dea1090000

Marking Bits: (CMBitMap*) 0x000001defe8e0b20
 Bits: [0x000001dea5090000, 0x000001dead090000)

Polling page: 0x000001defc7c0000

Metaspace:

Usage:
  Non-class:    103.85 MB used.
      Class:     16.58 MB used.
       Both:    120.43 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     104.94 MB ( 82%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      17.69 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     122.62 MB ( 11%) committed. 

Chunk freelists:
   Non-Class:  6.68 MB
       Class:  14.25 MB
        Both:  20.93 MB

MaxMetaspaceSize: 4.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 189.81 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 4294.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1962.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 9520.
num_chunk_merges: 9.
num_chunk_splits: 6284.
num_chunks_enlarged: 4063.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=258432Kb used=7328Kb max_used=7328Kb free=251103Kb
 bounds [0x000001de93430000, 0x000001de93b60000, 0x000001dea3090000]
CodeHeap 'profiled nmethods': size=258368Kb used=24932Kb max_used=24932Kb free=233435Kb
 bounds [0x000001de83090000, 0x000001de848f0000, 0x000001de92ce0000]
CodeHeap 'non-nmethods': size=7488Kb used=2160Kb max_used=3934Kb free=5327Kb
 bounds [0x000001de92ce0000, 0x000001de930d0000, 0x000001de93430000]
 total_blobs=13289 nmethods=12401 adapters=792
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 23.642 Thread 0x000001deaf714ea0 13999       4       org.gradle.internal.graph.CachingDirectedGraphWalker$GraphWithEmptyEdges::getEdgeValues (1 bytes)
Event: 23.642 Thread 0x000001deaf714ea0 nmethod 13999 0x000001de93b57090 code [0x000001de93b57220, 0x000001de93b572a8]
Event: 23.642 Thread 0x000001deaf714ea0 14264       4       org.gradle.internal.snapshot.AbstractFileSystemLocationSnapshot::asFileSystemNode (2 bytes)
Event: 23.642 Thread 0x000001deaf714ea0 nmethod 14264 0x000001de93b57390 code [0x000001de93b57520, 0x000001de93b575a8]
Event: 23.642 Thread 0x000001deaf714ea0 14076       4       org.gradle.internal.properties.PropertyVisitor::visitInputProperty (1 bytes)
Event: 23.642 Thread 0x000001deaf714ea0 nmethod 14076 0x000001de93b57690 code [0x000001de93b57820, 0x000001de93b578a8]
Event: 23.773 Thread 0x000001deaf7160d0 14355       3       javax.management.ObjectName::setCanonicalName (251 bytes)
Event: 23.774 Thread 0x000001deaf7160d0 nmethod 14355 0x000001de848e6b90 code [0x000001de848e6e20, 0x000001de848e7e00]
Event: 24.766 Thread 0x000001deb44e1850 14356       3       java.util.HashMap$ValueSpliterator::characteristics (28 bytes)
Event: 24.766 Thread 0x000001deaf714ea0 14357       4       java.util.stream.Collectors$$Lambda/0x000000080013add0::accept (14 bytes)
Event: 24.767 Thread 0x000001deb44e1850 nmethod 14356 0x000001de848e8410 code [0x000001de848e85c0, 0x000001de848e8780]
Event: 24.768 Thread 0x000001deaf714ea0 nmethod 14357 0x000001de93b57990 code [0x000001de93b57b40, 0x000001de93b57df0]
Event: 25.762 Thread 0x000001deb44e1850 14358       3       javax.management.ObjectName::<init> (15 bytes)
Event: 25.763 Thread 0x000001deb44e1850 nmethod 14358 0x000001de848e8810 code [0x000001de848e89c0, 0x000001de848e8b60]
Event: 26.770 Thread 0x000001deb44e1850 14359       1       java.util.stream.FindOps$FindOp::getOpFlags (5 bytes)
Event: 26.770 Thread 0x000001deb44e1850 nmethod 14359 0x000001de93b57f90 code [0x000001de93b58120, 0x000001de93b581e8]
Event: 26.770 Thread 0x000001deb44e1850 14360       3       javax.management.ObjectName::getInstance (9 bytes)
Event: 26.771 Thread 0x000001deb44e1850 nmethod 14360 0x000001de848e8c10 code [0x000001de848e8dc0, 0x000001de848e9008]
Event: 28.567 Thread 0x000001deaf714ea0 14361       4       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput$1::execute (9 bytes)
Event: 28.570 Thread 0x000001deaf7160d0 14373       3       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput$StateContext::flushLineText (113 bytes)

GC Heap History (20 events):
Event: 19.416 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 143360K, used 119894K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 9 young (36864K), 2 survivors (8192K)
 Metaspace       used 90809K, committed 92736K, reserved 1179648K
  class space    used 12458K, committed 13440K, reserved 1048576K
}
Event: 19.421 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 143360K, used 91456K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 90809K, committed 92736K, reserved 1179648K
  class space    used 12458K, committed 13440K, reserved 1048576K
}
Event: 19.624 GC heap before
{Heap before GC invocations=32 (full 0):
 garbage-first heap   total 143360K, used 116032K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 92739K, committed 94784K, reserved 1179648K
  class space    used 12688K, committed 13696K, reserved 1048576K
}
Event: 19.628 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 172032K, used 96186K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 92739K, committed 94784K, reserved 1179648K
  class space    used 12688K, committed 13696K, reserved 1048576K
}
Event: 20.170 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 172032K, used 145338K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 1 survivors (4096K)
 Metaspace       used 96792K, committed 98816K, reserved 1179648K
  class space    used 13215K, committed 14208K, reserved 1048576K
}
Event: 20.174 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 172032K, used 99385K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 96792K, committed 98816K, reserved 1179648K
  class space    used 13215K, committed 14208K, reserved 1048576K
}
Event: 20.632 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 172032K, used 148537K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 100873K, committed 102912K, reserved 1179648K
  class space    used 13770K, committed 14784K, reserved 1048576K
}
Event: 20.640 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 172032K, used 99952K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 100873K, committed 102912K, reserved 1179648K
  class space    used 13770K, committed 14784K, reserved 1048576K
}
Event: 21.027 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 172032K, used 145008K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 2 survivors (8192K)
 Metaspace       used 102853K, committed 104960K, reserved 1179648K
  class space    used 14032K, committed 15104K, reserved 1048576K
}
Event: 21.034 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 172032K, used 104373K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 102853K, committed 104960K, reserved 1179648K
  class space    used 14032K, committed 15104K, reserved 1048576K
}
Event: 21.391 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 180224K, used 145333K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 2 survivors (8192K)
 Metaspace       used 106293K, committed 108480K, reserved 1179648K
  class space    used 14499K, committed 15616K, reserved 1048576K
}
Event: 21.397 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 180224K, used 107182K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 106293K, committed 108480K, reserved 1179648K
  class space    used 14499K, committed 15616K, reserved 1048576K
}
Event: 21.828 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 180224K, used 156334K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 110353K, committed 112448K, reserved 1179648K
  class space    used 15233K, committed 16320K, reserved 1048576K
}
Event: 21.834 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 180224K, used 107982K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 110353K, committed 112448K, reserved 1179648K
  class space    used 15233K, committed 16320K, reserved 1048576K
}
Event: 22.264 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 180224K, used 161230K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 1 survivors (4096K)
 Metaspace       used 114109K, committed 116416K, reserved 1179648K
  class space    used 15705K, committed 16896K, reserved 1048576K
}
Event: 22.270 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 217088K, used 109615K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 114109K, committed 116416K, reserved 1179648K
  class space    used 15705K, committed 16896K, reserved 1048576K
}
Event: 22.973 GC heap before
{Heap before GC invocations=42 (full 0):
 garbage-first heap   total 217088K, used 187439K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 21 young (86016K), 2 survivors (8192K)
 Metaspace       used 121067K, committed 123392K, reserved 1179648K
  class space    used 16654K, committed 17856K, reserved 1048576K
}
Event: 22.979 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 217088K, used 114054K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 121067K, committed 123392K, reserved 1179648K
  class space    used 16654K, committed 17856K, reserved 1048576K
}
Event: 23.529 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 217088K, used 179590K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 20 young (81920K), 3 survivors (12288K)
 Metaspace       used 122927K, committed 125184K, reserved 1179648K
  class space    used 16913K, committed 18112K, reserved 1048576K
}
Event: 23.540 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 217088K, used 118820K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 122927K, committed 125184K, reserved 1179648K
  class space    used 16913K, committed 18112K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.018 Loaded shared library D:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.024 Loaded shared library D:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.759 Loaded shared library D:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 23.554 Thread 0x000001deb8b53400 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001de93b402d0 relative=0x0000000000002030
Event: 23.554 Thread 0x000001deb8b53400 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001de93b402d0 method=org.gradle.internal.snapshot.AbstractListChildMap.store(Lorg/gradle/internal/snapshot/VfsRelativePath;Lorg/gradle/internal/snapshot/CaseSensitivity
Event: 23.554 Thread 0x000001deb8b53400 DEOPT PACKING pc=0x000001de93b402d0 sp=0x000000c5019fb290
Event: 23.554 Thread 0x000001deb8b53400 DEOPT UNPACKING pc=0x000001de92d346a2 sp=0x000000c5019fb270 mode 2
Event: 23.554 Thread 0x000001deb8b53400 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001de93b402d0 relative=0x0000000000002030
Event: 23.554 Thread 0x000001deb8b53400 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001de93b402d0 method=org.gradle.internal.snapshot.AbstractListChildMap.store(Lorg/gradle/internal/snapshot/VfsRelativePath;Lorg/gradle/internal/snapshot/CaseSensitivity
Event: 23.554 Thread 0x000001deb8b53400 DEOPT PACKING pc=0x000001de93b402d0 sp=0x000000c5019fb290
Event: 23.554 Thread 0x000001deb8b53400 DEOPT UNPACKING pc=0x000001de92d346a2 sp=0x000000c5019fb270 mode 2
Event: 23.554 Thread 0x000001deb8b53400 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001de93b402d0 relative=0x0000000000002030
Event: 23.554 Thread 0x000001deb8b53400 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001de93b402d0 method=org.gradle.internal.snapshot.AbstractListChildMap.store(Lorg/gradle/internal/snapshot/VfsRelativePath;Lorg/gradle/internal/snapshot/CaseSensitivity
Event: 23.554 Thread 0x000001deb8b53400 DEOPT PACKING pc=0x000001de93b402d0 sp=0x000000c5019fb290
Event: 23.554 Thread 0x000001deb8b53400 DEOPT UNPACKING pc=0x000001de92d346a2 sp=0x000000c5019fb270 mode 2
Event: 28.567 Thread 0x000001deb98cf5b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001de93a20d68 relative=0x00000000000000a8
Event: 28.567 Thread 0x000001deb98cf5b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001de93a20d68 method=org.gradle.internal.logging.sink.OutputEventRenderer.onOutput(Lorg/gradle/internal/logging/events/OutputEvent;)V @ 1 c2
Event: 28.567 Thread 0x000001deb98cf5b0 DEOPT PACKING pc=0x000001de93a20d68 sp=0x000000c507dff130
Event: 28.567 Thread 0x000001deb98cf5b0 DEOPT UNPACKING pc=0x000001de92d346a2 sp=0x000000c507dff070 mode 2
Event: 28.567 Thread 0x000001deb98cf5b0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001de93a2228c relative=0x00000000000006cc
Event: 28.567 Thread 0x000001deb98cf5b0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001de93a2228c method=org.gradle.internal.logging.sink.OutputEventTransformer.onOutput(Lorg/gradle/internal/logging/events/OutputEvent;)V @ 215 c2
Event: 28.567 Thread 0x000001deb98cf5b0 DEOPT PACKING pc=0x000001de93a2228c sp=0x000000c507dff040
Event: 28.567 Thread 0x000001deb98cf5b0 DEOPT UNPACKING pc=0x000001de92d346a2 sp=0x000000c507dfefe8 mode 2

Classes loaded (20 events):
Event: 22.940 Loading class java/io/CharConversionException done
Event: 22.940 Loading class com/sun/org/apache/xerces/internal/impl/io/MalformedByteSequenceException done
Event: 23.567 Loading class java/lang/ProcessImpl
Event: 23.567 Loading class java/lang/ProcessImpl done
Event: 23.571 Loading class java/lang/ProcessHandleImpl
Event: 23.572 Loading class java/lang/ProcessHandleImpl done
Event: 23.572 Loading class java/lang/ProcessImpl$2
Event: 23.572 Loading class java/lang/ProcessImpl$2 done
Event: 23.572 Loading class java/lang/Process$PipeInputStream
Event: 23.572 Loading class java/lang/Process$PipeInputStream done
Event: 23.574 Loading class jdk/internal/event/ProcessStartEvent
Event: 23.574 Loading class jdk/internal/event/ProcessStartEvent done
Event: 28.568 Loading class java/io/PrintWriter$1
Event: 28.568 Loading class jdk/internal/access/JavaIOPrintWriterAccess
Event: 28.568 Loading class jdk/internal/access/JavaIOPrintWriterAccess done
Event: 28.568 Loading class java/io/PrintWriter$1 done
Event: 28.568 Loading class java/lang/Throwable$WrappedPrintWriter
Event: 28.568 Loading class java/lang/Throwable$PrintStreamOrWriter
Event: 28.568 Loading class java/lang/Throwable$PrintStreamOrWriter done
Event: 28.568 Loading class java/lang/Throwable$WrappedPrintWriter done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 22.217 Thread 0x000001deb59ac3b0 Exception <a 'java/lang/ClassNotFoundException'{0x000000060831d4f8}: com/android/build/gradle/internal/api/ReadOnlyBaseConfigCustomizer> (0x000000060831d4f8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 22.218 Thread 0x000001deb59ac3b0 Exception <a 'java/lang/ClassNotFoundException'{0x000000060833daa8}: com/android/build/gradle/internal/api/ReadOnlyBuildTypeCustomizer> (0x000000060833daa8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 22.513 Thread 0x000001deb59ac3b0 Implicit null exception at 0x000001de9399c5c4 to 0x000001de9399ce84
Event: 22.585 Thread 0x000001deb59ac3b0 Implicit null exception at 0x000001de938dad44 to 0x000001de938dae20
Event: 22.597 Thread 0x000001deb59ac3b0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000060b336148}: Found class java.lang.Object, but interface was expected> (0x000000060b336148) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 851]
Event: 22.598 Thread 0x000001deb59ac3b0 Implicit null exception at 0x000001de938d85cd to 0x000001de938d871c
Event: 22.644 Thread 0x000001deb59ac3b0 Exception <a 'sun/nio/fs/WindowsException'{0x000000060a926e40}> (0x000000060a926e40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 22.644 Thread 0x000001deb59ac3b0 Exception <a 'sun/nio/fs/WindowsException'{0x000000060a9284d8}> (0x000000060a9284d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 22.644 Thread 0x000001deb59ac3b0 Exception <a 'sun/nio/fs/WindowsException'{0x000000060a929bc0}> (0x000000060a929bc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 22.644 Thread 0x000001deb59ac3b0 Exception <a 'sun/nio/fs/WindowsException'{0x000000060a92b2a8}> (0x000000060a92b2a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 22.721 Thread 0x000001deb59ac3b0 Exception <a 'java/lang/NullPointerException'{0x000000060a3861e0}> (0x000000060a3861e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1471]
Event: 22.958 Thread 0x000001deb59ac3b0 Implicit null exception at 0x000001de93ad39ef to 0x000001de93ada150
Event: 23.248 Thread 0x000001deb98c7900 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b2943b0}: static Ljava/lang/Object;.<clinit>()V> (0x000000060b2943b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 23.249 Thread 0x000001deb98c7900 Exception <a 'java/lang/NoSuchMethodError'{0x000000060b2b0ba0}: static Lorg/jetbrains/kotlin/gradle/plugin/statistics/MetricContainer;.<clinit>()V> (0x000000060b2b0ba0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 23.255 Thread 0x000001deb8b53400 Exception <a 'sun/nio/fs/WindowsException'{0x000000060b301d48}> (0x000000060b301d48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 23.571 Thread 0x000001deb98cb410 Exception <a 'java/lang/NoSuchMethodError'{0x00000007ffed2e18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000007ffed2e18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 23.572 Thread 0x000001deb98cb410 Exception <a 'java/lang/NoSuchMethodError'{0x00000007ffedcad0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, java.lang.Object)'> (0x00000007ffedcad0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 28.567 Thread 0x000001deb98cf5b0 StackOverflowError at 0x000001de9347e3c0
Event: 28.567 Thread 0x000001deb98cf5b0 Exception <a 'java/lang/StackOverflowError'{0x00000007fff08270}> (0x00000007fff08270) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 28.567 Thread 0x000001deb98cf5b0 Exception <a 'java/lang/StackOverflowError'{0x00000007fff08270}> (0x00000007fff08270) 
thrown [s\src\hotspot\share\oops\instanceKlass.cpp, line 1234]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 23.130 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.130 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 23.237 Executing VM operation: ICBufferFull
Event: 23.237 Executing VM operation: ICBufferFull done
Event: 23.246 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.246 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 23.516 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.517 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 23.529 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 23.540 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 23.546 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.546 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 23.547 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.548 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 23.574 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.574 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 24.580 Executing VM operation: Cleanup
Event: 24.580 Executing VM operation: Cleanup done
Event: 28.566 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 28.567 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83857590
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de839c1590
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83b79290
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83b7c410
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83c1a490
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de93596b10
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de9376a410
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de938a6c10
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de8310db10
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de832ac710
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83968190
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83968910
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83a2b110
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83a31b90
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83b1ee10
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83b1f210
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83b22690
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83ba4790
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83c47510
Event: 22.314 Thread 0x000001deaf6a7820 flushing  nmethod 0x000001de83d28e10

Events (20 events):
Event: 17.938 Thread 0x000001deb59ac3b0 Thread added: 0x000001deb8fb1970
Event: 17.938 Thread 0x000001deb59ac3b0 Thread added: 0x000001deb8fb61a0
Event: 17.938 Thread 0x000001deb59ac3b0 Thread added: 0x000001deb8fb33b0
Event: 17.939 Thread 0x000001deb59ac3b0 Thread added: 0x000001deb8fb3a40
Event: 17.939 Thread 0x000001deb59ac3b0 Thread added: 0x000001deb8fb40d0
Event: 18.632 Thread 0x000001deb96951c0 Thread exited: 0x000001deb96951c0
Event: 19.077 Thread 0x000001deb78e9080 Thread exited: 0x000001deb78e9080
Event: 19.535 Thread 0x000001deaf7160d0 Thread added: 0x000001debf55fcb0
Event: 20.264 Thread 0x000001deb59ac3b0 Thread added: 0x000001deb98c7900
Event: 21.947 Thread 0x000001debf55fcb0 Thread exited: 0x000001debf55fcb0
Event: 21.963 Thread 0x000001deaf7160d0 Thread added: 0x000001deb5e9a7a0
Event: 22.367 Thread 0x000001deb59ac3b0 Thread added: 0x000001deb98cdb70
Event: 22.799 Thread 0x000001deb5e9a7a0 Thread exited: 0x000001deb5e9a7a0
Event: 23.235 Thread 0x000001deb8b53400 Thread added: 0x000001deb98cad80
Event: 23.566 Thread 0x000001deb8b53400 Thread added: 0x000001deb98cb410
Event: 23.575 Thread 0x000001deb98cb410 Thread added: 0x000001deb98cf5b0
Event: 23.575 Thread 0x000001deb98cb410 Thread added: 0x000001deb98cbaa0
Event: 28.568 Thread 0x000001deb44e1850 Thread exited: 0x000001deb44e1850
Event: 28.568 Thread 0x000001deb8a01300 Thread exited: 0x000001deb8a01300
Event: 28.568 Thread 0x000001deb71f7f10 Thread exited: 0x000001deb71f7f10


Dynamic libraries:
0x00007ff69a7e0000 - 0x00007ff69a7ea000 	D:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffe013b0000 - 0x00007ffe015c7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffdfffb0000 - 0x00007ffe00074000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffdfe6d0000 - 0x00007ffdfeaa2000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffdfeab0000 - 0x00007ffdfebc1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffde6190000 - 0x00007ffde61a8000 	D:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffde6170000 - 0x00007ffde618b000 	D:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffe01180000 - 0x00007ffe01331000 	C:\WINDOWS\System32\USER32.dll
0x00007ffdfe560000 - 0x00007ffdfe586000 	C:\WINDOWS\System32\win32u.dll
0x00007ffde3000000 - 0x00007ffde329b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5697_none_27154c497380d676\COMCTL32.dll
0x00007ffdff340000 - 0x00007ffdff3e7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe003b0000 - 0x00007ffe003d9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffdfef10000 - 0x00007ffdff033000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffdfedf0000 - 0x00007ffdfee8a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffdfff60000 - 0x00007ffdfff91000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffdf7700000 - 0x00007ffdf770c000 	D:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffdae1c0000 - 0x00007ffdae24d000 	D:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffd61150000 - 0x00007ffd61ddb000 	D:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffe010b0000 - 0x00007ffe01161000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffdff290000 - 0x00007ffdff338000 	C:\WINDOWS\System32\sechost.dll
0x00007ffdfedc0000 - 0x00007ffdfede8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe00e30000 - 0x00007ffe00f47000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe002a0000 - 0x00007ffe00311000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffdfe1c0000 - 0x00007ffdfe20d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffdf6990000 - 0x00007ffdf699a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffdea080000 - 0x00007ffdea0b4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffdfe1a0000 - 0x00007ffdfe1b3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffdfd440000 - 0x00007ffdfd458000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffdedb70000 - 0x00007ffdedb7a000 	D:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffdf2290000 - 0x00007ffdf24c3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe00610000 - 0x00007ffe009a2000 	C:\WINDOWS\System32\combase.dll
0x00007ffdff0f0000 - 0x00007ffdff1c8000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffded700000 - 0x00007ffded732000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffdfed40000 - 0x00007ffdfedbb000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffddefc0000 - 0x00007ffddefce000 	D:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffde14d0000 - 0x00007ffde14f0000 	D:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffddf450000 - 0x00007ffddf468000 	D:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffdff3f0000 - 0x00007ffdffc91000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffdfe590000 - 0x00007ffdfe6cf000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffdfc300000 - 0x00007ffdfcc1a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffdffe50000 - 0x00007ffdfff5b000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe00080000 - 0x00007ffe000e9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffdfe3d0000 - 0x00007ffdfe3fb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffdea920000 - 0x00007ffdea930000 	D:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffdf6ab0000 - 0x00007ffdf6bdc000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffdf3aa0000 - 0x00007ffdf3c1a000 	C:\Program Files (x86)\Sangfor\VDI\ClientComponent2\SangforTcpX64.dll
0x00007ffe00100000 - 0x00007ffe002a0000 	C:\WINDOWS\System32\ole32.dll
0x00007ffdfee90000 - 0x00007ffdfef0c000 	C:\WINDOWS\System32\WINTRUST.dll
0x00007ffdfebd0000 - 0x00007ffdfed38000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffdfe0c0000 - 0x00007ffdfe0d2000 	C:\WINDOWS\SYSTEM32\MSASN1.dll
0x00007ffdfd900000 - 0x00007ffdfd969000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffdde500000 - 0x00007ffdde516000 	D:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffde6740000 - 0x00007ffde6750000 	D:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffdda450000 - 0x00007ffdda477000 	D:\Dev\Env\repo\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x000000005f740000 - 0x000000005f7b3000 	D:\Dev\Env\repo\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffde6140000 - 0x00007ffde6149000 	D:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffde60f0000 - 0x00007ffde60fb000 	D:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffe01360000 - 0x00007ffe01368000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffdfdc50000 - 0x00007ffdfdc6b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffdfd3a0000 - 0x00007ffdfd3d7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffdfd9a0000 - 0x00007ffdfd9c8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffdfdb00000 - 0x00007ffdfdb0c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffdfce80000 - 0x00007ffdfcead000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffdfffa0000 - 0x00007ffdfffa9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffde5f90000 - 0x00007ffde5f99000 	D:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffde63e0000 - 0x00007ffde63e8000 	C:\WINDOWS\system32\wshunix.dll
0x000000005f640000 - 0x000000005f6b3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform6556192471222534101dir\gradle-fileevents.dll
0x00007ffddbe10000 - 0x00007ffddbe27000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffddbdf0000 - 0x00007ffddbe0b000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffdfcef0000 - 0x00007ffdfcfe8000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffddaa40000 - 0x00007ffddaa51000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffdf6490000 - 0x00007ffdf64af000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffdda790000 - 0x00007ffdda7b1000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffdf5e40000 - 0x00007ffdf5e4a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffdf6100000 - 0x00007ffdf6184000 	C:\WINDOWS\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5697_none_27154c497380d676;D:\Program Files\Android\Android Studio\jbr\bin\server;C:\Program Files (x86)\Sangfor\VDI\ClientComponent2;D:\Dev\Env\repo\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;D:\Dev\Env\repo\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform6556192471222534101dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:D:\Dev\Env\repo\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): D:\Dev\Env\repo\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8589934592                                {product} {command line}
   size_t MaxMetaspaceSize                         = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 5150605312                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 264634216                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 264634216                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 536870912                              {pd product} {command line}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8589934592                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Android\Android Studio\jbr
CLASSPATH=D:\Dev\OtherProjects\aes\my_tools\android\\gradle\wrapper\gradle-wrapper.jar
PATH=D:\Program Files\Android\Android Studio\jbr\bin;D:\Dev\Env\python\Scripts\;D:\Dev\Env\python\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Google\Chrome\Application\;C:\Program File;C:\Program Files\Git\bin;C:\Users\<USER>\fvm\default\bin;D:\Program Files\Xftp 8\;C:\Program Files\CursorModifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files\Microsoft SQL Server\110\Tools\Binn\;D:\Program Files\swigwin-4.3.0;C:\Users\<USER>\.local\bin;D:\Dev\Env\apache-maven-3.6.2\bin;D:\Dev\Env\uv;D:\Program Files\TortoiseGit\bin;C:\Program Files (x86)\Xshell 8\;C:\Program Files (x86)\Xftp\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\tools\neovim\nvim-win64\bin;C:\Users\<USER>\AppData\Roaming\nvm;node_global;node_global\node_modules\yarn\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Fiddler;C:\msys64\mingw64\bin;D:\Program Files\JetBrains\DataGrip 2023.1\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;D:\Program Files\Neovim\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\GoLand 2024.1.4\bin;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;D:\Java\apache-maven-3.9.6\bin;C:\ProgramData\mingw64\mingw64\bin;C:\Program Files\cursor-id-modifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;%IntelliJ IDEA%;C:\Program Files\ai;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\Dev\Env\flutter\bin\mingit\cmd
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 41, weak refs: 11

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 522176K (3% of 16617752K total physical memory with 247956K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
