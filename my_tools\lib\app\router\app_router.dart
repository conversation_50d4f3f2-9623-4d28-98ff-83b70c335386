import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/tools/aes_crypto/presentation/pages/aes_crypto_page.dart';
import '../../features/tools/js_executor/presentation/pages/js_executor_page.dart';
import 'routes.dart';

/// 全局路由配置Provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return AppRouter.router;
});

/// 应用路由配置
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutes.home,
    routes: [
      // 首页路由
      GoRoute(
        path: AppRoutes.home,
        name: AppRoutes.homeName,
        builder: (context, state) => const HomePage(),
      ),
      
      // AES加密工具路由
      GoRoute(
        path: AppRoutes.aesCrypto,
        name: AppRoutes.aesCryptoName,
        builder: (context, state) => const AesCryptoPage(),
      ),

      // JavaScript执行器路由
      GoRoute(
        path: AppRoutes.jsExecutor,
        name: AppRoutes.jsExecutorName,
        builder: (context, state) => const JsExecutorPage(),
      ),
    ],
    
    // 错误页面
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('页面未找到'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              '页面未找到: ${state.uri}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    ),
  );
}
