import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/js_executor_providers.dart';
import '../../domain/models/js_code_snippet.dart';
import '../../../../../shared/utils/app_logger.dart';

/// 代码编辑器区域
class CodeEditorSection extends ConsumerStatefulWidget {
  const CodeEditorSection({super.key});

  @override
  ConsumerState<CodeEditorSection> createState() => _CodeEditorSectionState();
}

class _CodeEditorSectionState extends ConsumerState<CodeEditorSection> {
  late final TextEditingController _codeController;

  @override
  void initState() {
    super.initState();
    _codeController = TextEditingController();
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 监听代码编辑器状态变化
    ref.listen<String>(codeEditorProvider, (previous, next) {
      if (next != _codeController.text) {
        _codeController.text = next;
        _codeController.selection = TextSelection.fromPosition(
          TextPosition(offset: next.length),
        );
      }
    });

    // 监听选中的代码片段变化
    ref.listen<JsCodeSnippet?>(selectedSnippetProvider, (previous, next) {
      if (next != null && next != previous) {
        AppLogger.info('加载代码片段到编辑器', {
          'snippet_name': next.name,
          'code_length': next.code.length,
        });
      }
    });

    final selectedSnippet = ref.watch(selectedSnippetProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 当前代码片段信息
          if (selectedSnippet != null) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    const Icon(Icons.info_outline, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            selectedSnippet.name,
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                          if (selectedSnippet.description.isNotEmpty)
                            Text(
                              selectedSnippet.description,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        ref.read(selectedSnippetProvider.notifier).state = null;
                        ref.read(codeEditorProvider.notifier).state = '';
                      },
                      tooltip: '清除选择',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 代码编辑器标题
          Row(
            children: [
              const Icon(Icons.code),
              const SizedBox(width: 8),
              Text(
                'JavaScript代码',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const Spacer(),
              // 语法验证按钮
              TextButton.icon(
                onPressed: () => _validateSyntax(),
                icon: const Icon(Icons.check_circle_outline, size: 18),
                label: const Text('验证语法'),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 代码编辑器
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _codeController,
                maxLines: null,
                expands: true,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 14,
                ),
                decoration: const InputDecoration(
                  hintText: '''// 在这里编写JavaScript代码
// 可以使用 input 变量获取输入数据
// 例如：
if (!input) {
  return '请提供输入数据';
}

// 处理输入数据
const result = input.toUpperCase();
return result;''',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                ),
                onChanged: (value) {
                  ref.read(codeEditorProvider.notifier).state = value;
                },
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 使用说明
          Card(
            color: Colors.blue.shade50,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb_outline, 
                           color: Colors.blue.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        '使用说明',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• 使用 input 变量获取输入数据\n'
                    '• 使用 return 语句返回结果\n'
                    '• 支持所有标准JavaScript语法\n'
                    '• 代码会在输入变化时自动执行',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 验证JavaScript语法
  Future<void> _validateSyntax() async {
    final code = _codeController.text.trim();
    if (code.isEmpty) {
      _showMessage('请输入代码', isError: true);
      return;
    }

    try {
      final executorService = ref.read(jsExecutorServiceProvider);
      final error = await executorService.validateSyntax(code);
      
      if (error == null) {
        _showMessage('语法正确', isError: false);
      } else {
        _showMessage('语法错误: $error', isError: true);
      }
    } catch (e) {
      _showMessage('验证失败: ${e.toString()}', isError: true);
    }
  }

  /// 显示消息
  void _showMessage(String message, {required bool isError}) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
