import 'package:freezed_annotation/freezed_annotation.dart';

part 'crypto_config.freezed.dart';
part 'crypto_config.g.dart';

/// AES加密配置模型
@freezed
class CryptoConfig with _$CryptoConfig {
  const factory CryptoConfig({
    @Default('') String key,
    @Default('') String iv,
    @Default(AesMode.ecb) AesMode mode,
    @Default(AesKeySize.aes128) AesKeySize keySize,
    @Default(AesPadding.pkcs7) AesPadding padding,
    @Default(EncodingFormat.base64) EncodingFormat inputFormat,
    @Default(EncodingFormat.base64) EncodingFormat outputFormat,
  }) = _CryptoConfig;

  factory CryptoConfig.fromJson(Map<String, dynamic> json) =>
      _$CryptoConfigFromJson(json);
}

/// AES加密模式
enum AesMode {
  @JsonValue('ecb')
  ecb('ECB'),
  @JsonValue('cbc')
  cbc('CBC'),
  @JsonValue('cfb')
  cfb('CFB'),
  @JsonValue('ofb')
  ofb('OFB'),
  @JsonValue('gcm')
  gcm('GCM');

  const AesMode(this.displayName);
  final String displayName;
}

/// AES密钥长度
enum AesKeySize {
  @JsonValue('128')
  aes128('128位'),
  @JsonValue('192')
  aes192('192位'),
  @JsonValue('256')
  aes256('256位');

  const AesKeySize(this.displayName);
  final String displayName;
  
  int get bits {
    switch (this) {
      case AesKeySize.aes128:
        return 128;
      case AesKeySize.aes192:
        return 192;
      case AesKeySize.aes256:
        return 256;
    }
  }
  
  int get bytes => bits ~/ 8;
}

/// AES填充方式
enum AesPadding {
  @JsonValue('pkcs7')
  pkcs7('PKCS7'),
  @JsonValue('pkcs5')
  pkcs5('PKCS5'),
  @JsonValue('none')
  none('NoPadding');

  const AesPadding(this.displayName);
  final String displayName;
}

/// 编码格式
enum EncodingFormat {
  @JsonValue('base64')
  base64('Base64'),
  @JsonValue('hex')
  hex('Hex'),
  @JsonValue('utf8')
  utf8('UTF-8');

  const EncodingFormat(this.displayName);
  final String displayName;
}
