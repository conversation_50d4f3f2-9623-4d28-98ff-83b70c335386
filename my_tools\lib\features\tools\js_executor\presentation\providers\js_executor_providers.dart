import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rxdart/rxdart.dart';

import '../../data/services/js_executor_service_impl.dart';
import '../../data/services/code_storage_service_impl.dart';
import '../../domain/models/js_code_snippet.dart';
import '../../domain/models/js_execution_result.dart';
import '../../domain/services/js_executor_service.dart';
import '../../domain/services/code_storage_service.dart';
import '../../../../../shared/utils/app_logger.dart';

/// JavaScript执行服务Provider
final jsExecutorServiceProvider = Provider<JsExecutorService>((ref) {
  return JsExecutorServiceImpl();
});

/// 代码存储服务Provider
final codeStorageServiceProvider = Provider<CodeStorageService>((ref) {
  return CodeStorageServiceImpl();
});

/// 当前选中的代码片段Provider
final selectedSnippetProvider = StateProvider<JsCodeSnippet?>((ref) => null);

/// 代码编辑器内容Provider
final codeEditorProvider = StateProvider<String>((ref) => '');

/// 输入文本Provider
final inputTextProvider = StateProvider<String>((ref) => '');

/// 输出文本Provider
final outputTextProvider = StateProvider<String>((ref) => '');

/// 执行结果Provider
final executionResultProvider = StateProvider<JsExecutionResult?>((ref) => null);

/// 是否正在执行Provider
final isExecutingProvider = StateProvider<bool>((ref) => false);

/// 代码片段列表Provider
final codeSnippetsProvider = FutureProvider<List<JsCodeSnippet>>((ref) async {
  final storageService = ref.watch(codeStorageServiceProvider);
  return await storageService.getAllSnippets();
});

/// 搜索查询Provider
final searchQueryProvider = StateProvider<String>((ref) => '');

/// 过滤后的代码片段Provider
final filteredSnippetsProvider = FutureProvider<List<JsCodeSnippet>>((ref) async {
  final query = ref.watch(searchQueryProvider);
  final storageService = ref.watch(codeStorageServiceProvider);
  
  if (query.trim().isEmpty) {
    return await storageService.getAllSnippets();
  } else {
    return await storageService.searchSnippets(query);
  }
});

/// 自动执行处理器Provider
final autoExecutorProvider = Provider<AutoExecutor>((ref) {
  return AutoExecutor(ref);
});

/// 自动执行处理器类
class AutoExecutor {
  final Ref _ref;
  final BehaviorSubject<String> _inputSubject = BehaviorSubject<String>();
  
  AutoExecutor(this._ref) {
    _setupAutoExecution();
  }

  /// 设置自动执行逻辑
  void _setupAutoExecution() {
    // 监听输入变化，防抖处理
    _inputSubject
        .debounceTime(const Duration(milliseconds: 800))
        .distinct()
        .listen((input) {
      _executeCurrentCode(input);
    });

    // 监听输入文本变化
    _ref.listen<String>(inputTextProvider, (previous, next) {
      if (next != previous) {
        _inputSubject.add(next);
      }
    });

    // 监听代码编辑器变化
    _ref.listen<String>(codeEditorProvider, (previous, next) {
      if (next != previous) {
        final input = _ref.read(inputTextProvider);
        if (input.isNotEmpty) {
          _inputSubject.add(input);
        }
      }
    });

    // 监听选中代码片段变化
    _ref.listen<JsCodeSnippet?>(selectedSnippetProvider, (previous, next) {
      if (next != null && next != previous) {
        // 更新代码编辑器
        _ref.read(codeEditorProvider.notifier).state = next.code;
        
        // 如果有输入，立即执行
        final input = _ref.read(inputTextProvider);
        if (input.isNotEmpty) {
          _executeCurrentCode(input);
        }
      }
    });
  }

  /// 执行当前代码
  Future<void> _executeCurrentCode(String input) async {
    final code = _ref.read(codeEditorProvider);
    if (code.trim().isEmpty) {
      return;
    }

    try {
      _ref.read(isExecutingProvider.notifier).state = true;
      
      final executorService = _ref.read(jsExecutorServiceProvider);
      final result = await executorService.executeCode(code, input: input);
      
      _ref.read(executionResultProvider.notifier).state = result;
      _ref.read(outputTextProvider.notifier).state = result.displayText;
      
      AppLogger.info('自动执行完成', {
        'success': result.isSuccess,
        'execution_time': result.executionTimeMs,
      });
      
    } catch (e, stackTrace) {
      AppLogger.error('自动执行失败', e, stackTrace);
      
      final errorResult = JsExecutionResult.createError(
        error: '执行失败: ${e.toString()}',
      );
      
      _ref.read(executionResultProvider.notifier).state = errorResult;
      _ref.read(outputTextProvider.notifier).state = errorResult.displayText;
      
    } finally {
      _ref.read(isExecutingProvider.notifier).state = false;
    }
  }

  /// 手动执行代码
  Future<void> executeManually() async {
    final input = _ref.read(inputTextProvider);
    await _executeCurrentCode(input);
  }

  /// 清理资源
  void dispose() {
    _inputSubject.close();
  }
}

/// 代码片段管理器Provider
final snippetManagerProvider = Provider<SnippetManager>((ref) {
  return SnippetManager(ref);
});

/// 代码片段管理器类
class SnippetManager {
  final Ref _ref;
  
  SnippetManager(this._ref);

  /// 保存当前代码为新片段
  Future<bool> saveCurrentCode(String name, String description) async {
    try {
      final code = _ref.read(codeEditorProvider);
      if (code.trim().isEmpty) {
        AppLogger.warning('尝试保存空代码');
        return false;
      }

      final snippet = JsCodeSnippet(
        name: name,
        code: code,
        description: description,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final storageService = _ref.read(codeStorageServiceProvider);
      await storageService.saveSnippet(snippet);
      
      // 刷新代码片段列表
      _ref.invalidate(codeSnippetsProvider);
      _ref.invalidate(filteredSnippetsProvider);
      
      AppLogger.info('代码片段保存成功', {'name': name});
      return true;
      
    } catch (e, stackTrace) {
      AppLogger.error('保存代码片段失败', e, stackTrace);
      return false;
    }
  }

  /// 更新现有代码片段
  Future<bool> updateSnippet(JsCodeSnippet snippet) async {
    try {
      final storageService = _ref.read(codeStorageServiceProvider);
      await storageService.updateSnippet(snippet);
      
      // 刷新代码片段列表
      _ref.invalidate(codeSnippetsProvider);
      _ref.invalidate(filteredSnippetsProvider);
      
      // 如果是当前选中的片段，更新选中状态
      final currentSelected = _ref.read(selectedSnippetProvider);
      if (currentSelected?.id == snippet.id) {
        _ref.read(selectedSnippetProvider.notifier).state = snippet;
      }
      
      AppLogger.info('代码片段更新成功', {'id': snippet.id, 'name': snippet.name});
      return true;
      
    } catch (e, stackTrace) {
      AppLogger.error('更新代码片段失败', e, stackTrace);
      return false;
    }
  }

  /// 删除代码片段
  Future<bool> deleteSnippet(int id) async {
    try {
      final storageService = _ref.read(codeStorageServiceProvider);
      final success = await storageService.deleteSnippet(id);
      
      if (success) {
        // 刷新代码片段列表
        _ref.invalidate(codeSnippetsProvider);
        _ref.invalidate(filteredSnippetsProvider);
        
        // 如果删除的是当前选中的片段，清除选中状态
        final currentSelected = _ref.read(selectedSnippetProvider);
        if (currentSelected?.id == id) {
          _ref.read(selectedSnippetProvider.notifier).state = null;
          _ref.read(codeEditorProvider.notifier).state = '';
        }
        
        AppLogger.info('代码片段删除成功', {'id': id});
      }
      
      return success;
      
    } catch (e, stackTrace) {
      AppLogger.error('删除代码片段失败', e, stackTrace);
      return false;
    }
  }
}
