import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/material.dart';

part 'tool_definition.freezed.dart';

/// 工具定义模型
@freezed
class ToolDefinition with _$ToolDefinition {
  const factory ToolDefinition({
    required String id,
    required String name,
    required String description,
    required IconData icon,
    required String route,
    required ToolCategory category,
    @Default(false) bool isEnabled,
  }) = _ToolDefinition;
}

/// 工具分类
enum ToolCategory {
  security('安全工具'),
  text('文本工具'),
  image('图像工具'),
  network('网络工具'),
  system('系统工具'),
  other('其他工具');

  const ToolCategory(this.displayName);
  final String displayName;
}
