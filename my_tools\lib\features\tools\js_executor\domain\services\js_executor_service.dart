import '../models/js_execution_result.dart';

/// JavaScript执行服务接口
abstract class JsExecutorService {
  /// 执行JavaScript代码
  /// 
  /// [code] - 要执行的JavaScript代码
  /// [input] - 输入数据（可选）
  /// 
  /// 返回执行结果
  Future<JsExecutionResult> executeCode(String code, {String? input});
  
  /// 验证JavaScript代码语法
  /// 
  /// [code] - 要验证的JavaScript代码
  /// 
  /// 返回验证结果，如果语法正确返回null，否则返回错误信息
  Future<String?> validateSyntax(String code);
  
  /// 设置执行超时时间
  /// 
  /// [timeoutMs] - 超时时间（毫秒）
  void setTimeout(int timeoutMs);
  
  /// 获取当前超时时间
  int getTimeout();
  
  /// 清理资源
  void dispose();
}
