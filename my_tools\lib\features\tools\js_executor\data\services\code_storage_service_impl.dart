import 'package:objectbox/objectbox.dart';

import '../../domain/models/js_code_snippet.dart';
import '../../domain/services/code_storage_service.dart';
import '../../../../../shared/utils/app_logger.dart';
import '../database/objectbox_manager.dart';

/// 代码存储服务实现
class CodeStorageServiceImpl implements CodeStorageService {
  late final Box<JsCodeSnippetEntity> _box;

  CodeStorageServiceImpl() {
    _initializeBox();
  }

  /// 初始化ObjectBox
  void _initializeBox() {
    try {
      _box = ObjectBoxManager.instance.store.box<JsCodeSnippetEntity>();
      AppLogger.info('代码存储服务初始化成功');
    } catch (e, stackTrace) {
      AppLogger.error('代码存储服务初始化失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<JsCodeSnippet> saveSnippet(JsCodeSnippet snippet) async {
    try {
      AppLogger.info('保存代码片段', {
        'name': snippet.name,
        'code_length': snippet.code.length,
      });

      final entity = JsCodeSnippetEntity.fromModel(snippet.copyWith(
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ));

      final id = _box.put(entity);
      entity.id = id;

      final savedSnippet = entity.toModel();
      
      AppLogger.info('代码片段保存成功', {'id': id});
      return savedSnippet;
    } catch (e, stackTrace) {
      AppLogger.error('保存代码片段失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<JsCodeSnippet> updateSnippet(JsCodeSnippet snippet) async {
    try {
      AppLogger.info('更新代码片段', {
        'id': snippet.id,
        'name': snippet.name,
      });

      final entity = JsCodeSnippetEntity.fromModel(snippet.copyWith(
        updatedAt: DateTime.now(),
      ));

      _box.put(entity);

      final updatedSnippet = entity.toModel();
      
      AppLogger.info('代码片段更新成功', {'id': snippet.id});
      return updatedSnippet;
    } catch (e, stackTrace) {
      AppLogger.error('更新代码片段失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<bool> deleteSnippet(int id) async {
    try {
      AppLogger.info('删除代码片段', {'id': id});

      final success = _box.remove(id);
      
      if (success) {
        AppLogger.info('代码片段删除成功', {'id': id});
      } else {
        AppLogger.warning('代码片段删除失败，可能不存在', {'id': id});
      }
      
      return success;
    } catch (e, stackTrace) {
      AppLogger.error('删除代码片段失败', e, stackTrace);
      return false;
    }
  }

  @override
  Future<List<JsCodeSnippet>> getAllSnippets() async {
    try {
      AppLogger.debug('获取所有代码片段');

      final entities = _box.getAll();
      final snippets = entities.map((entity) => entity.toModel()).toList();
      
      // 按更新时间倒序排列
      snippets.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      
      AppLogger.debug('获取代码片段成功', {'count': snippets.length});
      return snippets;
    } catch (e, stackTrace) {
      AppLogger.error('获取所有代码片段失败', e, stackTrace);
      return [];
    }
  }

  @override
  Future<JsCodeSnippet?> getSnippetById(int id) async {
    try {
      AppLogger.debug('根据ID获取代码片段', {'id': id});

      final entity = _box.get(id);
      if (entity == null) {
        AppLogger.debug('代码片段不存在', {'id': id});
        return null;
      }

      final snippet = entity.toModel();
      AppLogger.debug('获取代码片段成功', {'id': id, 'name': snippet.name});
      return snippet;
    } catch (e, stackTrace) {
      AppLogger.error('根据ID获取代码片段失败', e, stackTrace);
      return null;
    }
  }

  @override
  Future<List<JsCodeSnippet>> searchSnippets(String query) async {
    try {
      AppLogger.debug('搜索代码片段', {'query': query});

      if (query.trim().isEmpty) {
        return await getAllSnippets();
      }

      final lowerQuery = query.toLowerCase();
      final allSnippets = await getAllSnippets();
      
      final filteredSnippets = allSnippets.where((snippet) {
        return snippet.name.toLowerCase().contains(lowerQuery) ||
               snippet.description.toLowerCase().contains(lowerQuery) ||
               snippet.code.toLowerCase().contains(lowerQuery);
      }).toList();
      
      AppLogger.debug('搜索代码片段完成', {
        'query': query,
        'result_count': filteredSnippets.length,
      });
      
      return filteredSnippets;
    } catch (e, stackTrace) {
      AppLogger.error('搜索代码片段失败', e, stackTrace);
      return [];
    }
  }

  @override
  Future<List<JsCodeSnippet>> getEnabledSnippets() async {
    try {
      AppLogger.debug('获取启用的代码片段');

      final allSnippets = await getAllSnippets();
      final enabledSnippets = allSnippets.where((snippet) => snippet.isEnabled).toList();
      
      AppLogger.debug('获取启用的代码片段完成', {'count': enabledSnippets.length});
      return enabledSnippets;
    } catch (e, stackTrace) {
      AppLogger.error('获取启用的代码片段失败', e, stackTrace);
      return [];
    }
  }

  @override
  void dispose() {
    AppLogger.info('代码存储服务已释放');
    // ObjectBox的Box不需要手动释放，由Store管理
  }
}
