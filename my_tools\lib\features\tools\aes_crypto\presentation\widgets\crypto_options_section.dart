import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../shared/widgets/custom_text_field.dart';
import '../../../../../shared/widgets/option_selector.dart';
import '../../domain/models/crypto_config.dart';
import '../providers/aes_crypto_providers.dart';

/// 加密选项配置区域组件
class CryptoOptionsSection extends ConsumerStatefulWidget {
  const CryptoOptionsSection({super.key});

  @override
  ConsumerState<CryptoOptionsSection> createState() => _CryptoOptionsSectionState();
}

class _CryptoOptionsSectionState extends ConsumerState<CryptoOptionsSection> {
  late TextEditingController _keyController;
  late TextEditingController _ivController;

  @override
  void initState() {
    super.initState();
    _keyController = TextEditingController();
    _ivController = TextEditingController();
  }

  @override
  void dispose() {
    _keyController.dispose();
    _ivController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final config = ref.watch(cryptoConfigProvider);
    final configNotifier = ref.read(cryptoConfigProvider.notifier);

    // 同步控制器文本
    if (_keyController.text != config.key) {
      _keyController.text = config.key;
    }
    if (_ivController.text != config.iv) {
      _ivController.text = config.iv;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Text(
                  '加密配置',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                // 重置按钮
                IconButton(
                  onPressed: () {
                    configNotifier.reset();
                    _keyController.clear();
                    _ivController.clear();
                  },
                  icon: const Icon(Icons.refresh),
                  tooltip: '重置配置',
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 第一行：密钥和IV
            LayoutBuilder(
              builder: (context, constraints) {
                // 在小屏幕上使用垂直布局
                if (constraints.maxWidth < 600) {
                  return Column(
                    children: [
                      CustomTextField(
                        label: '密钥 (Key)',
                        controller: _keyController,
                        hintText: '请输入加密密钥',
                        onChanged: (value) {
                          configNotifier.updateKey(value);
                          _triggerReprocess();
                        },
                      ),
                      const SizedBox(height: 16),
                      CustomTextField(
                        label: '初始向量 (IV)',
                        controller: _ivController,
                        hintText: config.mode == AesMode.ecb ? 'ECB模式不需要IV' : '请输入IV',
                        enabled: config.mode != AesMode.ecb,
                        onChanged: (value) {
                          configNotifier.updateIV(value);
                          _triggerReprocess();
                        },
                        suffixIcon: config.mode != AesMode.ecb
                            ? IconButton(
                                onPressed: _generateRandomIV,
                                icon: const Icon(Icons.shuffle),
                                tooltip: '生成随机IV',
                              )
                            : null,
                      ),
                    ],
                  );
                }

                // 在大屏幕上使用水平布局
                return Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: CustomTextField(
                        label: '密钥 (Key)',
                        controller: _keyController,
                        hintText: '请输入加密密钥',
                        onChanged: (value) {
                          configNotifier.updateKey(value);
                          _triggerReprocess();
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CustomTextField(
                        label: '初始向量 (IV)',
                        controller: _ivController,
                        hintText: config.mode == AesMode.ecb ? 'ECB模式不需要IV' : '请输入IV',
                        enabled: config.mode != AesMode.ecb,
                        onChanged: (value) {
                          configNotifier.updateIV(value);
                          _triggerReprocess();
                        },
                        suffixIcon: config.mode != AesMode.ecb
                            ? IconButton(
                                onPressed: _generateRandomIV,
                                icon: const Icon(Icons.shuffle),
                                tooltip: '生成随机IV',
                              )
                            : null,
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),

            // 第二行：加密模式和密钥长度
            LayoutBuilder(
              builder: (context, constraints) {
                // 在小屏幕上使用垂直布局
                if (constraints.maxWidth < 600) {
                  return Column(
                    children: [
                      OptionSelector<AesMode>(
                        label: '加密模式',
                        value: config.mode,
                        options: AesMode.values,
                        getDisplayText: (mode) => mode.displayName,
                        onChanged: (mode) {
                          if (mode != null) {
                            configNotifier.updateMode(mode);
                            // 如果切换到ECB模式，清空IV
                            if (mode == AesMode.ecb) {
                              _ivController.clear();
                              configNotifier.updateIV('');
                            }
                            _triggerReprocess();
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      OptionSelector<AesKeySize>(
                        label: '密钥长度',
                        value: config.keySize,
                        options: AesKeySize.values,
                        getDisplayText: (keySize) => keySize.displayName,
                        onChanged: (keySize) {
                          if (keySize != null) {
                            configNotifier.updateKeySize(keySize);
                            _triggerReprocess();
                          }
                        },
                      ),
                    ],
                  );
                }

                // 在大屏幕上使用水平布局
                return Row(
                  children: [
                    Expanded(
                      child: OptionSelector<AesMode>(
                        label: '加密模式',
                        value: config.mode,
                        options: AesMode.values,
                        getDisplayText: (mode) => mode.displayName,
                        onChanged: (mode) {
                          if (mode != null) {
                            configNotifier.updateMode(mode);
                            // 如果切换到ECB模式，清空IV
                            if (mode == AesMode.ecb) {
                              _ivController.clear();
                              configNotifier.updateIV('');
                            }
                            _triggerReprocess();
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: OptionSelector<AesKeySize>(
                        label: '密钥长度',
                        value: config.keySize,
                        options: AesKeySize.values,
                        getDisplayText: (keySize) => keySize.displayName,
                        onChanged: (keySize) {
                          if (keySize != null) {
                            configNotifier.updateKeySize(keySize);
                            _triggerReprocess();
                          }
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),

            // 第三行：填充方式和编码格式
            Row(
              children: [
                Expanded(
                  child: OptionSelector<AesPadding>(
                    label: '填充方式',
                    value: config.padding,
                    options: AesPadding.values,
                    getDisplayText: (padding) => padding.displayName,
                    onChanged: (padding) {
                      if (padding != null) {
                        configNotifier.updatePadding(padding);
                        _triggerReprocess();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OptionSelector<EncodingFormat>(
                    label: '输入格式',
                    value: config.inputFormat,
                    options: EncodingFormat.values,
                    getDisplayText: (format) => format.displayName,
                    onChanged: (format) {
                      if (format != null) {
                        configNotifier.updateInputFormat(format);
                        _triggerReprocess();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OptionSelector<EncodingFormat>(
                    label: '输出格式',
                    value: config.outputFormat,
                    options: EncodingFormat.values,
                    getDisplayText: (format) => format.displayName,
                    onChanged: (format) {
                      if (format != null) {
                        configNotifier.updateOutputFormat(format);
                        _triggerReprocess();
                      }
                    },
                  ),
                ),
              ],
            ),

            // 配置提示
            if (config.mode != AesMode.ecb && config.iv.isEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange.shade600, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${config.mode.displayName} 模式需要提供初始向量(IV)',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 生成随机IV
  void _generateRandomIV() {
    final service = ref.read(aesCryptoServiceProvider);
    final config = ref.read(cryptoConfigProvider);
    final randomIV = service.generateRandomIV(config.mode);
    
    _ivController.text = randomIV;
    ref.read(cryptoConfigProvider.notifier).updateIV(randomIV);
    _triggerReprocess();
  }

  /// 触发重新处理
  void _triggerReprocess() {
    // 延迟一点时间，让配置更新完成
    Future.delayed(const Duration(milliseconds: 100), () {
      ref.read(autoProcessProvider).processCurrentField();
    });
  }
}
