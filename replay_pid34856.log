version 2
JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 1
JvmtiExport can_post_on_exceptions 0
# 345 ciObject found
instanceKlass lombok/patcher/PatchScript$MethodPatcher
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass lombok/core/LombokInternalAliasing
instanceKlass lombok/core/HandlerPriority
instanceKlass lombok/eclipse/DeferUntilPostDiet
instanceKlass lombok/eclipse/HandlerLibrary$AnnotationHandlerContainer
instanceKlass lombok/experimental/Accessors
instanceKlass lombok/eclipse/EclipseAnnotationHandler
instanceKlass lombok/core/SpiLoadUtil$1$1
instanceKlass lombok/core/SpiLoadUtil$1
instanceKlass java/util/Vector$1
instanceKlass lombok/core/SpiLoadUtil
instanceKlass lombok/core/configuration/ConfigurationKeysLoader
instanceKlass lombok/core/configuration/CheckerFrameworkVersion
instanceKlass lombok/core/configuration/TypeName
instanceKlass lombok/core/configuration/LogDeclaration
instanceKlass lombok/core/configuration/IdentifierName
instanceKlass lombok/core/configuration/ConfigurationDataType$6
instanceKlass lombok/core/configuration/ConfigurationDataType$7
instanceKlass lombok/core/configuration/NullAnnotationLibrary
instanceKlass lombok/core/configuration/ConfigurationValueType
instanceKlass lombok/core/configuration/ConfigurationDataType$5
instanceKlass lombok/core/configuration/ConfigurationDataType$4
instanceKlass lombok/core/configuration/ConfigurationDataType$3
instanceKlass lombok/core/configuration/ConfigurationDataType$2
instanceKlass lombok/core/configuration/ConfigurationDataType$1
instanceKlass lombok/core/configuration/ConfigurationValueParser
instanceKlass lombok/core/configuration/ConfigurationDataType
instanceKlass  @bci java/util/regex/CharPredicates ASCII_WORD ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x000002962f2ed440
instanceKlass lombok/core/configuration/ConfigurationKey
instanceKlass lombok/ConfigurationKeys
instanceKlass lombok/core/configuration/ConfigurationKeysLoader$LoaderLoader
instanceKlass lombok/core/TypeLibrary
instanceKlass lombok/eclipse/HandlerLibrary
instanceKlass lombok/eclipse/EclipseASTVisitor
instanceKlass lombok/eclipse/TransformEclipseAST
instanceKlass lombok/launch/PackageShader
instanceKlass lombok/launch/Main
instanceKlass lombok/launch/PatchFixesHider$Transform
instanceKlass org/eclipse/jdt/core/dom/ModuleModifier$ModuleModifierKeyword
instanceKlass org/eclipse/jdt/core/dom/PrimitiveType$Code
instanceKlass org/eclipse/jdt/core/dom/InfixExpression$Operator
instanceKlass org/eclipse/jdt/core/dom/ASTConverter$ISetJavaDoc
instanceKlass org/eclipse/jdt/core/dom/ASTConverter$IGetJavaDoc
instanceKlass org/eclipse/jdt/core/dom/ASTConverter
instanceKlass lombok/patcher/scripts/WrapMethodCallScript$1
instanceKlass org/eclipse/jdt/internal/compiler/ReadManager
instanceKlass lombok/eclipse/agent/PatchValEclipse$Reflection
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f3b9c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f3b9800
instanceKlass org/eclipse/jdt/core/dom/IExtendedModifier
instanceKlass org/eclipse/jdt/core/dom/Modifier$ModifierKeyword
instanceKlass lombok/eclipse/agent/PatchValEclipse
instanceKlass lombok/eclipse/agent/PatchValEclipsePortal$Reflection
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f3b9400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f3b9000
instanceKlass lombok/eclipse/agent/PatchValEclipsePortal
instanceKlass lombok/core/LombokNode
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/PatchFixesHider$ValPortal
instanceKlass org/eclipse/jdt/internal/compiler/flow/FlowContext
instanceKlass org/eclipse/jdt/internal/compiler/flow/FlowInfo
instanceKlass org/eclipse/jdt/internal/compiler/util/Messages
instanceKlass org/eclipse/jdt/internal/compiler/ast/CompilationUnitDeclaration$1
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Scope$Substitutor
instanceKlass org/eclipse/jdt/internal/compiler/CompilationResult$1
instanceKlass org/eclipse/jdt/internal/compiler/ast/IJavadocTypeReference
instanceKlass org/eclipse/jdt/internal/compiler/parser/AbstractCommentParser
instanceKlass org/eclipse/jdt/internal/compiler/parser/JavadocTagConstants
instanceKlass org/eclipse/jdt/core/compiler/CategorizedProblem
instanceKlass org/eclipse/jdt/internal/compiler/problem/DefaultProblemFactory
instanceKlass org/eclipse/jdt/internal/compiler/DefaultErrorHandlingPolicies$4
instanceKlass org/eclipse/jdt/internal/compiler/DefaultErrorHandlingPolicies
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f3a4c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f3a4800
instanceKlass lombok/permit/Permit$Fake
instanceKlass sun/misc/Unsafe
instanceKlass lombok/permit/Permit
instanceKlass lombok/launch/PatchFixesHider$Util
instanceKlass lombok/launch/PatchFixesHider$ModuleClassLoading
instanceKlass org/eclipse/jdt/internal/compiler/parser/RecoveredElement
instanceKlass org/eclipse/jdt/internal/compiler/ast/IGenerateTypeCheck
instanceKlass org/eclipse/jdt/internal/compiler/batch/Main$ResourceBundleFactory
instanceKlass  @bci jdk/internal/reflect/MethodHandleIntegerFieldAccessorImpl getInt (Ljava/lang/Object;)I 11 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f384800
instanceKlass org/eclipse/jdt/internal/compiler/util/HashtableOfInt
instanceKlass org/eclipse/jdt/internal/compiler/batch/Main$Logger
instanceKlass org/eclipse/jdt/internal/compiler/batch/FileSystem$ClasspathSectionProblemReporter
instanceKlass  @bci org/eclipse/core/internal/resources/ContentDescriptionManager clearContentFlags (Lorg/eclipse/core/runtime/IPath;Lorg/eclipse/core/runtime/IProgressMonitor;)V 24 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/ContentDescriptionManager$$Lambda+0x000002962f36d8f8
instanceKlass  @cpi org/eclipse/core/internal/resources/ContentDescriptionManager 761 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f384400
instanceKlass org/eclipse/jdt/internal/compiler/batch/Main
instanceKlass org/eclipse/jdt/internal/core/BasicCompilationUnit
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$ZipCache
instanceKlass  @bci org/eclipse/jdt/core/dom/ASTParser internalCreateAST (Lorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/jdt/core/dom/ASTNode; 2 <appendix> member <vmtarget> ; # org/eclipse/jdt/core/dom/ASTParser$$Lambda+0x000002962f38b1f0
instanceKlass org/eclipse/jdt/core/JavaCore$JavaCallable
instanceKlass org/eclipse/jdt/core/dom/CompilationUnitResolver$ECJCompilationUnitResolver
instanceKlass org/eclipse/jdt/internal/compiler/IErrorHandlingPolicy
instanceKlass org/eclipse/jdt/internal/core/INameEnvironmentWithProgress
instanceKlass org/eclipse/jdt/internal/compiler/env/INameEnvironmentExtension
instanceKlass org/eclipse/jdt/internal/core/dom/ICompilationUnitResolver
instanceKlass org/eclipse/jdt/core/dom/CompilationUnitResolverDiscovery
instanceKlass org/eclipse/jdt/internal/core/dom/util/DOMASTUtil
instanceKlass org/eclipse/jdt/core/dom/ASTRequestor
instanceKlass org/eclipse/jdt/core/dom/ASTParser
instanceKlass org/eclipse/jdt/core/dom/NodeEventHandler
instanceKlass org/eclipse/jdt/core/dom/BindingResolver
instanceKlass org/eclipse/jdt/core/dom/AST
instanceKlass org/eclipse/jdt/internal/corext/dom/IASTSharedValues
instanceKlass org/eclipse/jdt/internal/compiler/classfmt/ExternalAnnotationDecorator$ZipFileProducer
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f384000
instanceKlass  @bci org/eclipse/jdt/internal/core/JarPackageFragment computeNonJavaResources (Ljava/util/List;)[Ljava/lang/Object; 465 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JarPackageFragment$$Lambda+0x000002962f37ee90
instanceKlass org/eclipse/jdt/core/IJarEntryResource
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/managers/ProjectsManager cleanupResources (Lorg/eclipse/core/resources/IProject;)V 42 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/managers/ProjectsManager$$Lambda+0x000002962f355dc8
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/managers/ProjectsManager cleanupResources (Lorg/eclipse/core/resources/IProject;)V 31 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/managers/ProjectsManager$$Lambda+0x000002962f355b90
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/managers/ProjectsManager cleanupResources (Lorg/eclipse/core/resources/IProject;)V 20 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/managers/ProjectsManager$$Lambda+0x000002962f355948
instanceKlass org/eclipse/jdt/internal/compiler/codegen/ConstantPool
instanceKlass org/eclipse/jdt/internal/compiler/classfmt/ClassFileConstants
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryElementValuePair
instanceKlass org/eclipse/jdt/internal/compiler/codegen/AttributeNamesConstants
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryNestedType
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryTypeAnnotation
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryModule
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryAnnotation
instanceKlass org/eclipse/jdt/internal/compiler/env/IRecordComponent
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryMethod
instanceKlass org/eclipse/jdt/internal/compiler/env/IGenericMethod
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryField
instanceKlass org/eclipse/jdt/internal/compiler/env/IGenericField
instanceKlass org/eclipse/jdt/internal/compiler/classfmt/ClassFileStruct
instanceKlass  @bci org/eclipse/jdt/internal/core/JarPackageFragmentRoot createChildren (Ljava/util/Collection;)[Lorg/eclipse/jdt/core/IJavaElement; 30 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JarPackageFragmentRoot$$Lambda+0x000002962f37db20
instanceKlass  @bci org/eclipse/jdt/internal/core/JarPackageFragmentRoot lambda$0 (Ljava/util/List;)[Ljava/lang/String; 1 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JarPackageFragmentRoot$$Lambda+0x000002962f37d910
instanceKlass  @bci org/eclipse/jdt/internal/core/JarPackageFragmentRoot createChildren (Ljava/util/Collection;)[Lorg/eclipse/jdt/core/IJavaElement; 10 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JarPackageFragmentRoot$$Lambda+0x000002962f37d6e0
instanceKlass org/eclipse/jdt/internal/core/util/WeakHashSetOfCharArray
instanceKlass org/eclipse/jdt/internal/core/util/WeakHashSet
instanceKlass org/eclipse/jdt/internal/core/util/DeduplicationUtil
instanceKlass  @bci org/eclipse/jdt/internal/core/index/MetaIndex remove (Ljava/lang/String;)V 16 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/index/MetaIndex$$Lambda+0x000002962f37c248
instanceKlass org/eclipse/jdt/internal/compiler/util/SimpleSetOfCharArray
instanceKlass org/eclipse/jdt/internal/core/index/DiskIndex
instanceKlass org/eclipse/jdt/internal/core/util/SimpleWordSet
instanceKlass org/eclipse/jdt/internal/core/index/MemoryIndex
instanceKlass org/eclipse/jdt/internal/core/search/indexing/ReadWriteMonitor
instanceKlass org/eclipse/jdt/internal/core/index/MetaIndex
instanceKlass  @bci org/eclipse/jdt/internal/core/search/processing/JobManager isJobWaiting (Lorg/eclipse/jdt/internal/core/search/processing/IJob;)Z 22 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/search/processing/JobManager$$Lambda+0x000002962f377148
instanceKlass  @cpi org/eclipse/jdt/internal/core/search/processing/JobManager 524 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f378000
instanceKlass  @bci jdk/xml/internal/SecuritySupport getResourceBundle (Ljava/lang/String;Ljava/util/Locale;)Ljava/util/ResourceBundle; 2 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002962f2ec938
instanceKlass com/sun/org/apache/xerces/internal/dom/DOMMessageFormatter
instanceKlass org/eclipse/jdt/internal/compiler/util/GenericXMLWriter$1
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$InvalidArchiveInfo
instanceKlass org/eclipse/jdt/internal/core/UserLibraryClasspathContainer
instanceKlass org/eclipse/jdt/internal/core/ClasspathValidation
instanceKlass org/eclipse/jdt/internal/core/ClasspathAttribute
instanceKlass org/eclipse/jdt/internal/launching/JREContainer$1
instanceKlass org/eclipse/jdt/internal/launching/JREContainer
instanceKlass org/eclipse/jdt/internal/core/util/LRUCache$LRUCacheEntry
instanceKlass org/eclipse/jdt/internal/core/util/ILRUCacheable
instanceKlass org/eclipse/jdt/core/ICompletionRequestor
instanceKlass org/eclipse/jdt/core/IImportDeclaration
instanceKlass org/eclipse/jdt/core/IImportContainer
instanceKlass org/eclipse/jdt/core/IPackageDeclaration
instanceKlass org/eclipse/jdt/core/IModularClassFile
instanceKlass org/eclipse/jdt/internal/compiler/util/ObjectVector
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/CharDeduplication <clinit> ()V 44 <appendix> argL0 ; # org/eclipse/jdt/internal/compiler/util/CharDeduplication$$Lambda+0x000002962f2b6ec8
instanceKlass org/eclipse/jdt/internal/compiler/util/CharDeduplication
instanceKlass org/eclipse/jdt/internal/compiler/parser/Scanner
instanceKlass org/eclipse/jdt/core/JavaConventions
instanceKlass org/eclipse/jdt/internal/core/JavaProject$3
instanceKlass org/eclipse/jdt/internal/core/JavaProject$2
instanceKlass org/eclipse/jdt/internal/core/builder/BuildNotifier
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$OutputsInfo
instanceKlass org/eclipse/jdt/internal/core/JavaElementDelta$Key
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$RootInfo
instanceKlass org/eclipse/jdt/internal/core/JavaProject$ResolvedClasspath
instanceKlass org/eclipse/jdt/internal/core/ExternalFolderChange
instanceKlass org/eclipse/jdt/internal/core/ClasspathChange
instanceKlass org/eclipse/jdt/internal/core/ClasspathEntry
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$SecondaryTypes
instanceKlass  @bci org/eclipse/core/internal/properties/PropertyBucket$PropertyEntry <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/core/internal/properties/PropertyBucket$PropertyEntry$$Lambda+0x000002962f36d418
instanceKlass org/eclipse/jdt/internal/core/JavaNature
instanceKlass org/eclipse/core/resources/IProjectNature
instanceKlass org/eclipse/core/internal/resources/NatureManager$1
instanceKlass org/eclipse/core/internal/resources/ProjectNatureDescriptor
instanceKlass org/eclipse/core/internal/filesystem/FileStoreUtil
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences internalChildNames ()[Ljava/lang/String; 41 <appendix> argL0 ; # org/eclipse/core/internal/preferences/EclipsePreferences$$Lambda+0x000002962f1c1bf0
instanceKlass  @bci org/eclipse/core/internal/resources/ProjectPreferences save ()V 42 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/ProjectPreferences$$Lambda+0x000002962f36c788
instanceKlass java/util/AbstractList$SubList$1
instanceKlass org/eclipse/core/internal/localstore/UnifiedTreeNode
instanceKlass org/eclipse/core/internal/localstore/UnifiedTree
instanceKlass  @bci java/util/Map$Entry comparingByKey ()Ljava/util/Comparator; 0 <appendix> argL0 ; # java/util/Map$Entry$$Lambda+0x000002962f2eac40
instanceKlass  @bci org/eclipse/core/internal/resources/SaveManager visitAndSnap (Lorg/eclipse/core/resources/IResource;)V 203 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f36ac00
instanceKlass  @bci org/eclipse/core/internal/resources/SaveManager visitAndSnap (Lorg/eclipse/core/resources/IResource;)V 203 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/SaveManager$$Lambda+0x000002962f35f6f8
instanceKlass  @cpi org/eclipse/core/internal/resources/SaveManager 2131 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f36a800
instanceKlass org/eclipse/core/internal/dtree/DataTreeWriter
instanceKlass org/eclipse/core/internal/watson/ElementTreeWriter$1
instanceKlass org/eclipse/core/internal/dtree/IDataFlattener
instanceKlass org/eclipse/core/internal/watson/ElementTreeWriter
instanceKlass  @bci java/util/stream/Nodes$CollectorTask$OfRef <init> (Ljava/util/stream/PipelineHelper;Ljava/util/function/IntFunction;Ljava/util/Spliterator;)V 9 <appendix> argL0 ; # java/util/stream/Nodes$CollectorTask$OfRef$$Lambda+0x000002962f2eaa08
instanceKlass java/util/stream/Nodes$AbstractConcNode
instanceKlass  @bci java/util/stream/Nodes$CollectorTask$OfRef <init> (Ljava/util/stream/PipelineHelper;Ljava/util/function/IntFunction;Ljava/util/Spliterator;)V 4 <appendix> member <vmtarget> ; # java/util/stream/Nodes$CollectorTask$OfRef$$Lambda+0x000002962f2ea2a0
instanceKlass java/util/function/LongFunction
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager lambda$6 (Lorg/eclipse/core/resources/ISaveContext;Ljava/util/ArrayList;)[Lorg/eclipse/core/runtime/IStatus; 34 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000002962f361dc0
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager lambda$6 (Lorg/eclipse/core/resources/ISaveContext;Ljava/util/ArrayList;)[Lorg/eclipse/core/runtime/IStatus; 24 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000002962f361b80
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager lambda$6 (Lorg/eclipse/core/resources/ISaveContext;Ljava/util/ArrayList;)[Lorg/eclipse/core/runtime/IStatus; 14 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000002962f361948
instanceKlass java/util/concurrent/ForkJoinTask$Aux
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager savingTimed (Lorg/eclipse/core/resources/ISaveContext;)V 239 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000002962f361730
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager savingTimed (Lorg/eclipse/core/resources/ISaveContext;)V 221 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000002962f361518
instanceKlass org/eclipse/jdt/internal/compiler/util/HashtableOfObjectToInt
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$VariablesAndContainersSaveHelper
instanceKlass org/eclipse/core/internal/resources/SaveManager$1
instanceKlass  @bci org/eclipse/core/internal/resources/LocalMetaArea getSafeTableLocationFor (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 44 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f36a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f36a000
instanceKlass  @bci org/eclipse/core/internal/resources/LocalMetaArea getSafeTableLocationFor (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 44 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f369c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f369800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f369400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f369000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f368c00
instanceKlass  @bci org/eclipse/core/internal/resources/LocalMetaArea getSafeTableLocationFor (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 44 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f368800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f368400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f368000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f359400
instanceKlass org/eclipse/core/internal/resources/SafeFileTable
instanceKlass org/eclipse/core/internal/resources/SaveContext
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager reportBlocked (Lorg/eclipse/core/runtime/IProgressMonitor;Ljava/util/Collection;)V 6 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000002962f166a38
instanceKlass org/eclipse/core/resources/IResourceStatus
instanceKlass org/eclipse/debug/internal/core/IInternalDebugCoreConstants
instanceKlass org/eclipse/debug/internal/core/Preferences
instanceKlass org/eclipse/debug/core/commands/IDebugCommandRequest
instanceKlass org/eclipse/debug/core/IRequest
instanceKlass org/eclipse/debug/internal/core/StepFilterManager
instanceKlass org/eclipse/debug/internal/core/LaunchManager$LaunchManagerVisitor
instanceKlass org/eclipse/jdt/internal/core/SimpleDelta
instanceKlass org/eclipse/jdt/internal/compiler/env/IModulePathEntry
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$PerProjectInfo
instanceKlass org/eclipse/jdt/core/eval/IEvaluationContext
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$2
instanceKlass  @bci org/eclipse/core/internal/resources/CheckMissingNaturesListener resourceChanged (Lorg/eclipse/core/resources/IResourceChangeEvent;)V 25 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/CheckMissingNaturesListener$$Lambda+0x000002962f35dd48
instanceKlass org/eclipse/core/internal/events/NotificationManager$1
instanceKlass  @bci org/eclipse/core/internal/events/ResourceChangeListenerList getListeners ()[Lorg/eclipse/core/internal/events/ResourceChangeListenerList$ListenerEntry; 4 <appendix> argL0 ; # org/eclipse/core/internal/events/ResourceChangeListenerList$$Lambda+0x000002962f35d908
instanceKlass org/eclipse/core/internal/resources/ModelObjectWriter
instanceKlass org/eclipse/core/internal/resources/IModelObjectConstants
instanceKlass org/eclipse/core/internal/filesystem/local/InfiniteProgress
instanceKlass org/eclipse/core/internal/utils/BitMask
instanceKlass org/eclipse/core/internal/events/LifecycleEvent
instanceKlass java/util/AbstractMap$2$1
instanceKlass  @bci org/eclipse/core/internal/resources/ProjectVariableProviderManager <clinit> ()V 14 <appendix> argL0 ; # org/eclipse/core/internal/resources/ProjectVariableProviderManager$$Lambda+0x000002962f35c5f8
instanceKlass  @cpi org/eclipse/core/internal/resources/ProjectVariableProviderManager 171 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f359000
instanceKlass org/eclipse/core/resources/variableresolvers/PathVariableResolver
instanceKlass org/eclipse/core/internal/resources/ProjectVariableProviderManager$Descriptor
instanceKlass org/eclipse/core/internal/resources/ProjectVariableProviderManager
instanceKlass org/eclipse/core/internal/resources/ProjectPathVariableManager
instanceKlass org/eclipse/core/internal/resources/OS
instanceKlass  @bci org/eclipse/core/internal/resources/AliasManager findResources (Lorg/eclipse/core/filesystem/IFileStore;)[Lorg/eclipse/core/resources/IResource; 14 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/AliasManager$$Lambda+0x000002962f1ee4f8
instanceKlass org/eclipse/core/internal/localstore/FileStoreRoot
instanceKlass java/lang/Short$ShortCache
instanceKlass com/sun/jna/Native$Buffers
instanceKlass org/eclipse/core/internal/filesystem/local/Win32Handler$FileAPIh
instanceKlass org/eclipse/core/filesystem/provider/FileInfo
instanceKlass org/eclipse/core/internal/filesystem/local/NativeHandler
instanceKlass org/eclipse/core/internal/filesystem/local/LocalFileNativesManager
instanceKlass  @bci org/eclipse/core/internal/filesystem/local/LocalFile createExecutor (I)Ljava/util/concurrent/ForkJoinPool; 15 <appendix> argL0 ; # org/eclipse/core/internal/filesystem/local/LocalFile$$Lambda+0x000002962f35ab90
instanceKlass  @bci org/eclipse/core/internal/filesystem/local/LocalFile createExecutor (I)Ljava/util/concurrent/ForkJoinPool; 5 <appendix> argL0 ; # org/eclipse/core/internal/filesystem/local/LocalFile$$Lambda+0x000002962f35a980
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JLSFsUtils
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JDTLSFilesystemActivator$1
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JDTLSFilesystemActivator
instanceKlass org/eclipse/core/filesystem/IFileSystem
instanceKlass org/eclipse/core/internal/filesystem/InternalFileSystemCore
instanceKlass org/eclipse/core/filesystem/IFileInfo
instanceKlass org/eclipse/core/filesystem/EFS
instanceKlass org/eclipse/core/filesystem/URIUtil
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/JDTUtils resolveCompilationUnit (Ljava/net/URI;)Lorg/eclipse/jdt/core/ICompilationUnit; 39 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/JDTUtils$$Lambda+0x000002962f333db0
instanceKlass org/eclipse/core/runtime/URIUtil
instanceKlass org/eclipse/jdt/core/dom/IDocElement
instanceKlass org/eclipse/jdt/ls/core/internal/JDTUtils
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler didChangeWatchedFiles (Lorg/eclipse/lsp4j/DidChangeWatchedFilesParams;)V 15 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler$$Lambda+0x000002962f333138
instanceKlass java/util/stream/DistinctOps
instanceKlass org/eclipse/lsp4j/FileEvent
instanceKlass com/google/gson/JsonParser
instanceKlass org/eclipse/lsp4j/RelativePattern
instanceKlass org/eclipse/lsp4j/DidChangeWatchedFilesRegistrationOptions
instanceKlass org/eclipse/lsp4j/FileSystemWatcher
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/syntaxserver/SyntaxProjectsManager registerWatchers ()Ljava/util/List; 62 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/syntaxserver/SyntaxProjectsManager$$Lambda+0x000002962f332f08
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionHandler$2
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionHandler$1
instanceKlass org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$PendingRequestInfo
instanceKlass org/eclipse/lsp4j/Registration
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint handleRequest (Lorg/eclipse/lsp4j/jsonrpc/messages/RequestMessage;)V 142 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x000002962f320a98
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint handleRequest (Lorg/eclipse/lsp4j/jsonrpc/messages/RequestMessage;)V 132 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x000002962f320860
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f354000
instanceKlass org/eclipse/lsp4j/FileOperationPatternOptions
instanceKlass org/eclipse/lsp4j/FileOperationPattern
instanceKlass org/eclipse/lsp4j/FileOperationFilter
instanceKlass org/eclipse/lsp4j/FileOperationOptions
instanceKlass org/eclipse/lsp4j/FileOperationsServerCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemOptions
instanceKlass org/eclipse/lsp4j/NotebookSelectorCell
instanceKlass org/eclipse/lsp4j/NotebookDocumentFilter
instanceKlass org/eclipse/lsp4j/NotebookSelector
instanceKlass sun/nio/ch/WindowsAsynchronousFileChannelImpl$WriteTask
instanceKlass org/eclipse/lsp4j/DocumentOnTypeFormattingOptions
instanceKlass org/eclipse/lsp4j/AbstractWorkDoneProgressOptions
instanceKlass org/eclipse/lsp4j/NotebookDocumentSyncRegistrationOptions
instanceKlass com/google/gson/annotations/Expose
instanceKlass org/eclipse/lsp4j/ServerInfo
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint handleRequest (Lorg/eclipse/lsp4j/jsonrpc/messages/RequestMessage;)V 121 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x000002962f320638
instanceKlass java/util/concurrent/ForkJoinTask
instanceKlass com/google/gson/internal/Streams
instanceKlass java/util/concurrent/CompletableFuture$AsynchronousCompletionTask
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/syntaxserver/SyntaxProjectsManager cleanInvalidProjects (Ljava/util/Collection;Lorg/eclipse/core/runtime/IProgressMonitor;)V 6 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/syntaxserver/SyntaxProjectsManager$$Lambda+0x000002962f332340
instanceKlass java/util/concurrent/CompletableFuture$AltResult
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/handlers/SemanticTokensHandler legend ()Lorg/eclipse/lsp4j/SemanticTokensLegend; 37 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/handlers/SemanticTokensHandler$$Lambda+0x000002962f332110
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/handlers/SemanticTokensHandler legend ()Lorg/eclipse/lsp4j/SemanticTokensLegend; 10 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/handlers/SemanticTokensHandler$$Lambda+0x000002962f331ab8
instanceKlass org/eclipse/jdt/core/dom/IBinding
instanceKlass org/eclipse/lsp4j/SemanticTokensLegend
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/SemanticTokensHandler
instanceKlass org/eclipse/lsp4j/DocumentFilter
instanceKlass org/eclipse/lsp4j/SemanticTokensServerFull
instanceKlass org/eclipse/lsp4j/TextDocumentRegistrationOptions
instanceKlass org/eclipse/lsp4j/WorkDoneProgressOptions
instanceKlass org/eclipse/lsp4j/WorkspaceFoldersOptions
instanceKlass org/eclipse/lsp4j/WorkspaceServerCapabilities
instanceKlass org/eclipse/lsp4j/SaveOptions
instanceKlass org/eclipse/lsp4j/TextDocumentSyncOptions
instanceKlass org/apache/commons/lang3/BooleanUtils
instanceKlass org/eclipse/lsp4j/ServerCapabilities
instanceKlass org/eclipse/jdt/core/ClasspathContainerInitializer
instanceKlass org/eclipse/jdt/launching/environments/CompatibleEnvironment
instanceKlass org/eclipse/jdt/internal/launching/environments/ExecutionEnvironmentAnalyzer
instanceKlass org/eclipse/jdt/internal/launching/environments/Analyzer
instanceKlass org/eclipse/jdt/launching/environments/IExecutionEnvironmentAnalyzerDelegate
instanceKlass org/eclipse/jdt/internal/launching/environments/AccessRuleParticipant
instanceKlass org/eclipse/jdt/internal/launching/environments/ExecutionEnvironment$1
instanceKlass org/eclipse/jdt/launching/environments/IAccessRuleParticipant
instanceKlass org/eclipse/jdt/internal/launching/environments/ExecutionEnvironment
instanceKlass  @bci org/eclipse/jdt/internal/launching/environments/EnvironmentsManager initializeExtensions ()V 14 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/environments/EnvironmentsManager$$Lambda+0x000002962f346f68
instanceKlass  @bci org/eclipse/jdt/internal/launching/environments/EnvironmentsManager initializeExtensions ()V 9 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/launching/environments/EnvironmentsManager$$Lambda+0x000002962f346cd8
instanceKlass  @cpi org/eclipse/jdt/internal/launching/environments/EnvironmentsManager 651 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f348800
instanceKlass org/eclipse/jdt/launching/environments/IExecutionEnvironment
instanceKlass org/eclipse/jdt/internal/launching/environments/EnvironmentsManager
instanceKlass org/eclipse/jdt/core/ClasspathVariableInitializer
instanceKlass org/eclipse/jdt/ls/core/internal/javafx/FXLibraryLocationResolver
instanceKlass org/eclipse/jdt/internal/launching/JavaFxLibraryResolver
instanceKlass org/eclipse/jdt/launching/ILibraryLocationResolver
instanceKlass  @bci org/eclipse/jdt/internal/launching/StandardVMType getLibraryLocationResolvers ()[Lorg/eclipse/jdt/launching/ILibraryLocationResolver; 27 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/StandardVMType$$Lambda+0x000002962f345db8
instanceKlass org/eclipse/jdt/launching/sourcelookup/IJavaSourceLocation
instanceKlass org/eclipse/debug/core/model/IDebugTarget
instanceKlass org/eclipse/debug/core/model/IMemoryBlockRetrieval
instanceKlass org/eclipse/debug/core/IBreakpointListener
instanceKlass  @bci org/eclipse/debug/internal/core/InputStreamMonitor startMonitoring (Ljava/lang/String;)V 17 <appendix> member <vmtarget> ; # org/eclipse/debug/internal/core/InputStreamMonitor$$Lambda+0x000002962f34eb78
instanceKlass  @bci org/eclipse/debug/internal/core/OutputStreamMonitor startMonitoring (Ljava/lang/String;)V 25 <appendix> member <vmtarget> ; # org/eclipse/debug/internal/core/OutputStreamMonitor$$Lambda+0x000002962f34e960
instanceKlass org/eclipse/debug/internal/core/LaunchManager$LaunchesNotifier
instanceKlass org/eclipse/debug/internal/core/LaunchManager$LaunchNotifier
instanceKlass org/eclipse/debug/internal/core/InputStreamMonitor
instanceKlass org/eclipse/debug/internal/core/StreamDecoder
instanceKlass org/eclipse/debug/internal/core/OutputStreamMonitor
instanceKlass org/eclipse/debug/core/model/IBinaryStreamMonitor
instanceKlass org/eclipse/debug/core/model/IFlushableStreamMonitor
instanceKlass org/eclipse/debug/core/model/IStreamMonitor
instanceKlass org/eclipse/debug/internal/core/StreamsProxy
instanceKlass org/eclipse/debug/core/model/IBinaryStreamsProxy
instanceKlass org/eclipse/debug/core/model/IStreamsProxy2
instanceKlass org/eclipse/debug/core/model/IStreamsProxy
instanceKlass org/eclipse/debug/core/ILaunchListener
instanceKlass java/lang/ProcessImpl$2
instanceKlass  @bci java/lang/ProcessImpl <init> ([Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[JZZ)V 286 <appendix> member <vmtarget> ; # java/lang/ProcessImpl$$Lambda+0x000002962f2e5930
instanceKlass  @cpi java/lang/ProcessImpl 640 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f348400
instanceKlass java/lang/Process
instanceKlass java/lang/ProcessBuilder
instanceKlass  @bci org/eclipse/debug/core/DebugPlugin exec ([Ljava/lang/String;Ljava/io/File;[Ljava/lang/String;Z)Ljava/lang/Process; 18 <appendix> argL0 ; # org/eclipse/debug/core/DebugPlugin$$Lambda+0x000002962f33f868
instanceKlass  @bci org/eclipse/debug/core/DebugPlugin getExecFactories ()Ljava/util/List; 225 <appendix> argL0 ; # org/eclipse/debug/core/DebugPlugin$$Lambda+0x000002962f33f658
instanceKlass org/eclipse/debug/core/DebugPlugin$ExecFactoryFacade
instanceKlass org/eclipse/debug/core/ExecFactory
instanceKlass org/eclipse/jdt/core/compiler/CompilationParticipant
instanceKlass org/eclipse/jdt/internal/launching/LaunchingPlugin$VMChanges$1
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager variableNames ()[Ljava/lang/String; 7 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000002962f2b0fe0
instanceKlass org/eclipse/jdt/internal/launching/LaunchingPlugin$VMChanges
instanceKlass  @bci org/eclipse/jdt/launching/AbstractVMInstallType getVMInstalls ()[Lorg/eclipse/jdt/launching/IVMInstall; 4 <appendix> argL0 ; # org/eclipse/jdt/launching/AbstractVMInstallType$$Lambda+0x000002962f344ce0
instanceKlass  @bci org/eclipse/jdt/launching/VMStandin convertToRealVM ()Lorg/eclipse/jdt/launching/IVMInstall; 155 <appendix> member <vmtarget> ; # org/eclipse/jdt/launching/VMStandin$$Lambda+0x000002962f344568
instanceKlass  @cpi org/eclipse/jdt/launching/VMStandin 173 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f348000
instanceKlass org/eclipse/jdt/launching/IVMRunner
instanceKlass org/eclipse/jdt/internal/launching/CompositeId
instanceKlass org/eclipse/jdt/launching/AbstractVMInstall
instanceKlass org/eclipse/jdt/launching/IVMInstall3
instanceKlass org/eclipse/jdt/launching/IVMInstall2
instanceKlass org/eclipse/jdt/launching/LibraryLocation
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerProxy$1
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerSetter
instanceKlass org/eclipse/osgi/internal/url/NullURLStreamHandlerService
instanceKlass org/osgi/service/url/URLStreamHandlerSetter
instanceKlass  @bci org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl createInternalURLStreamHandler (Ljava/lang/String;)Ljava/net/URLStreamHandler; 18 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl$$Lambda+0x000002962f1ddae0
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl$PlurlFactoryHolder getHandler (Ljava/lang/String;)Ljava/lang/Object; 35 <appendix> member <vmtarget> ; # org/eclipse/equinox/plurl/impl/PlurlImpl$PlurlFactoryHolder$$Lambda+0x000002962f1dd8a8
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl$4
instanceKlass org/eclipse/jdt/internal/launching/LibraryInfo
instanceKlass  @bci org/eclipse/jdt/internal/launching/StandardVMType readReleaseVersion (Ljava/io/File;)Ljava/lang/String; 63 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/StandardVMType$$Lambda+0x000002962f342678
instanceKlass java/nio/file/FileChannelLinesSpliterator$1
instanceKlass  @bci org/eclipse/jdt/internal/launching/StandardVMType readReleaseVersion (Ljava/io/File;)Ljava/lang/String; 48 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/StandardVMType$$Lambda+0x000002962f342438
instanceKlass java/util/stream/Streams$1
instanceKlass  @bci java/nio/file/Files createFileChannelLinesStream (Ljava/nio/channels/FileChannel;Ljava/nio/charset/Charset;)Ljava/util/stream/Stream; 53 <appendix> member <vmtarget> ; # java/nio/file/Files$$Lambda+0x000002962f2e4400
instanceKlass  @bci java/nio/file/Files asUncheckedRunnable (Ljava/io/Closeable;)Ljava/lang/Runnable; 1 <appendix> member <vmtarget> ; # java/nio/file/Files$$Lambda+0x000002962f2e41e8
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel$1
instanceKlass sun/nio/ch/Interruptible
instanceKlass java/nio/file/FileChannelLinesSpliterator
instanceKlass org/eclipse/jdt/internal/launching/VMListener
instanceKlass org/eclipse/jdt/internal/launching/VMDefinitionsContainer
instanceKlass  @bci org/eclipse/jdt/internal/launching/StandardVMType <clinit> ()V 78 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/StandardVMType$$Lambda+0x000002962f341860
instanceKlass  @bci org/eclipse/jdt/internal/launching/StandardVMType <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/StandardVMType$$Lambda+0x000002962f341650
instanceKlass org/eclipse/jdt/launching/AbstractVMInstallType
instanceKlass org/eclipse/jdt/launching/IVMInstallType
instanceKlass org/eclipse/debug/core/model/ISourceLocator
instanceKlass org/eclipse/jdt/internal/launching/sourcelookup/advanced/AdvancedSourceLookupSupport
instanceKlass org/eclipse/debug/internal/core/groups/GroupMemberChangeListener
instanceKlass org/eclipse/core/internal/watson/ElementTreeIterator
instanceKlass  @bci org/eclipse/core/internal/resources/Resource accept (Lorg/eclipse/core/resources/IResourceProxyVisitor;II)V 50 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f340c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f340800
instanceKlass  @bci org/eclipse/core/internal/resources/Resource accept (Lorg/eclipse/core/resources/IResourceProxyVisitor;II)V 50 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f340400
instanceKlass  @bci org/eclipse/core/internal/resources/Resource accept (Lorg/eclipse/core/resources/IResourceProxyVisitor;II)V 50 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/Resource$$Lambda+0x000002962f1edc88
instanceKlass  @cpi org/eclipse/core/internal/resources/Resource 1632 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f340000
instanceKlass org/eclipse/core/internal/resources/ResourceProxy
instanceKlass org/eclipse/debug/internal/core/LaunchManager$ResourceProxyVisitor
instanceKlass org/eclipse/debug/core/ILaunchMode
instanceKlass org/eclipse/core/resources/IResourceProxyVisitor
instanceKlass org/eclipse/debug/core/ILaunchConfiguration
instanceKlass org/eclipse/debug/core/ILaunchDelegate
instanceKlass org/eclipse/jdt/launching/StandardClasspathProvider
instanceKlass org/eclipse/jdt/launching/IVMConnector
instanceKlass org/eclipse/jdt/launching/IRuntimeClasspathEntryResolver
instanceKlass org/eclipse/jdt/launching/IRuntimeClasspathEntry
instanceKlass org/eclipse/jdt/launching/environments/IExecutionEnvironmentsManager
instanceKlass org/eclipse/jdt/launching/IVMInstall
instanceKlass org/eclipse/jdt/launching/IRuntimeClasspathProvider
instanceKlass org/eclipse/jdt/launching/JavaRuntime
instanceKlass org/eclipse/jdt/internal/launching/LaunchingPlugin$1$1
instanceKlass org/eclipse/debug/core/model/IDebugElement
instanceKlass org/eclipse/debug/core/ILaunch
instanceKlass org/eclipse/debug/core/model/ISuspendResume
instanceKlass org/eclipse/debug/core/model/IStepFilters
instanceKlass org/eclipse/debug/core/model/IStep
instanceKlass org/eclipse/debug/core/model/IDropToFrame
instanceKlass  @bci org/eclipse/core/internal/runtime/AdapterManager registerFactory (Lorg/eclipse/core/runtime/IAdapterFactory;Ljava/lang/String;)V 5 <appendix> argL0 ; # org/eclipse/core/internal/runtime/AdapterManager$$Lambda+0x000002962f1d4ea0
instanceKlass org/eclipse/debug/core/model/IDisconnect
instanceKlass org/eclipse/debug/internal/core/commands/ForEachCommand$ExclusiveRule
instanceKlass org/eclipse/debug/core/commands/AbstractDebugCommand
instanceKlass org/eclipse/debug/core/commands/IStepFiltersHandler
instanceKlass org/eclipse/debug/core/commands/IResumeHandler
instanceKlass org/eclipse/debug/core/commands/ISuspendHandler
instanceKlass org/eclipse/debug/core/commands/IDisconnectHandler
instanceKlass org/eclipse/debug/core/commands/IDropToFrameHandler
instanceKlass org/eclipse/debug/core/commands/IStepReturnHandler
instanceKlass org/eclipse/debug/core/commands/IStepIntoHandler
instanceKlass org/eclipse/debug/core/commands/IStepOverHandler
instanceKlass org/eclipse/debug/core/commands/ITerminateHandler
instanceKlass org/eclipse/debug/core/commands/IDebugCommandHandler
instanceKlass org/eclipse/debug/internal/core/commands/CommandAdapterFactory
instanceKlass org/eclipse/debug/core/DebugPlugin$1$1
instanceKlass org/eclipse/debug/internal/core/DebugOptions
instanceKlass org/eclipse/debug/core/DebugPlugin$AsynchRunner
instanceKlass org/eclipse/debug/core/DebugPlugin$EventNotifier
instanceKlass org/eclipse/debug/core/model/IProcess
instanceKlass org/eclipse/debug/core/model/ITerminate
instanceKlass org/eclipse/debug/core/IExpressionManager
instanceKlass org/eclipse/debug/core/IMemoryBlockManager
instanceKlass org/eclipse/debug/core/IBreakpointManager
instanceKlass org/eclipse/debug/core/ILaunchManager
instanceKlass org/eclipse/debug/core/ILaunchConfigurationListener
instanceKlass org/eclipse/debug/core/IDebugEventSetListener
instanceKlass org/eclipse/debug/core/ILaunchesListener
instanceKlass org/eclipse/jdt/ls/core/internal/JVMConfigurator
instanceKlass org/eclipse/jdt/launching/IVMInstallChangedListener
instanceKlass sun/nio/fs/WindowsFileCopy
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/preferences/Preferences createFrom (Ljava/util/Map;)Lorg/eclipse/jdt/ls/core/internal/preferences/Preferences; 2196 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/preferences/Preferences$$Lambda+0x000002962f330498
instanceKlass com/google/common/base/CommonPattern
instanceKlass com/google/common/base/Platform$JdkPatternCompiler
instanceKlass com/google/common/base/PatternCompiler
instanceKlass com/google/common/base/Platform
instanceKlass com/google/common/base/Strings
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/preferences/Preferences createFrom (Ljava/util/Map;)Lorg/eclipse/jdt/ls/core/internal/preferences/Preferences; 1617 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/preferences/Preferences$$Lambda+0x000002962f330270
instanceKlass org/eclipse/jdt/ls/core/internal/RuntimeEnvironment
instanceKlass org/eclipse/jdt/internal/corext/util/TypeFilter
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/preferences/Preferences setResourceFilters (Ljava/util/List;)Lorg/eclipse/jdt/ls/core/internal/preferences/Preferences; 11 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/preferences/Preferences$$Lambda+0x000002962f30fc38
instanceKlass com/google/common/base/Ascii
instanceKlass com/google/common/base/Preconditions
instanceKlass com/google/common/base/CharMatcher
instanceKlass com/google/common/base/Predicate
instanceKlass com/google/common/base/Converter
instanceKlass com/google/common/base/Function
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/MapFlattener
instanceKlass sun/nio/fs/WindowsUriSupport
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/ClientPreferences
instanceKlass com/google/gson/internal/bind/ObjectTypeAdapter$2
instanceKlass com/google/gson/internal/LinkedTreeMap$LinkedTreeMapIterator
instanceKlass  @bci com/google/gson/internal/ConstructorConstructor newMapConstructor (Ljava/lang/reflect/Type;Ljava/lang/Class;)Lcom/google/gson/internal/ObjectConstructor; 16 <appendix> argL0 ; # com/google/gson/internal/ConstructorConstructor$$Lambda+0x000002962f3112e8
instanceKlass org/eclipse/jdt/ls/core/internal/JSONUtility
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/BaseInitHandler
instanceKlass  @cpi org/eclipse/jdt/ls/core/internal/JDTUtils 1907 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f328000
instanceKlass com/google/gson/internal/LinkedTreeMap$Node
instanceKlass com/google/gson/internal/LinkedTreeMap$1
instanceKlass com/google/gson/internal/bind/JsonElementTypeAdapter$1
instanceKlass org/eclipse/lsp4j/jsonrpc/util/Preconditions
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/JsonElementTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/StaleRequestCapabilities
instanceKlass org/eclipse/lsp4j/MarkdownCapabilities
instanceKlass org/eclipse/lsp4j/RegularExpressionsCapabilities
instanceKlass org/eclipse/lsp4j/WindowShowMessageRequestActionItemCapabilities
instanceKlass org/eclipse/lsp4j/ShowDocumentCapabilities
instanceKlass org/eclipse/lsp4j/WindowShowMessageRequestCapabilities
instanceKlass org/eclipse/lsp4j/InlayHintResolveSupportCapabilities
instanceKlass org/eclipse/lsp4j/SemanticTokensClientCapabilitiesRequestsFull
instanceKlass org/eclipse/lsp4j/SemanticTokensClientCapabilitiesRequests
instanceKlass org/eclipse/lsp4j/FoldingRangeSupportCapabilities
instanceKlass org/eclipse/lsp4j/FoldingRangeKindSupportCapabilities
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/EitherTypeAdapter$EitherTypeArgument
instanceKlass org/eclipse/lsp4j/DiagnosticsTagSupport
instanceKlass org/eclipse/lsp4j/CodeActionKindCapabilities
instanceKlass org/eclipse/lsp4j/CodeActionResolveSupportCapabilities
instanceKlass org/eclipse/lsp4j/CodeActionLiteralSupportCapabilities
instanceKlass org/eclipse/lsp4j/ParameterInformationCapabilities
instanceKlass org/eclipse/lsp4j/SignatureInformationCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemInsertTextModeSupportCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemResolveSupportCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemTagSupportCapabilities
instanceKlass org/eclipse/lsp4j/CompletionListCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemCapabilities
instanceKlass org/eclipse/lsp4j/CompletionItemKindCapabilities
instanceKlass org/eclipse/equinox/internal/app/EclipseAppHandle$1
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppHandle getStartupMonitors ()[Lorg/osgi/framework/ServiceReference; 34 <appendix> argL0 ; # org/eclipse/equinox/internal/app/EclipseAppHandle$$Lambda+0x000002962f156908
instanceKlass org/eclipse/lsp4j/PublishDiagnosticsCapabilities
instanceKlass java/time/LocalTime$1
instanceKlass  @bci java/util/regex/Pattern CIRange (II)Ljava/util/regex/Pattern$CharPredicate; 2 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x000002962f2e29c0
instanceKlass  @bci java/util/regex/Pattern SingleI (II)Ljava/util/regex/Pattern$BmpCharPredicate; 2 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x000002962f2e2740
instanceKlass java/time/format/Parsed
instanceKlass java/time/format/DateTimeParseContext
instanceKlass org/eclipse/jdt/ls/core/internal/LogReader$LogEntry
instanceKlass java/io/PrintWriter$1
instanceKlass jdk/internal/access/JavaIOPrintWriterAccess
instanceKlass org/eclipse/jdt/ls/core/internal/LogReader$LogSession
instanceKlass org/eclipse/lsp4j/WorkspaceSymbolResolveSupportCapabilities
instanceKlass org/eclipse/lsp4j/SymbolTagSupportCapabilities
instanceKlass org/eclipse/lsp4j/SymbolKindCapabilities
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/json/adapters/CollectionTypeAdapter$Factory getConstructor (Ljava/lang/Class;)Ljava/util/function/Supplier; 62 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/json/adapters/CollectionTypeAdapter$Factory$$Lambda+0x000002962f30bdc0
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/TypeUtils$ParameterizedTypeImpl
instanceKlass org/eclipse/lsp4j/WorkspaceEditChangeAnnotationSupportCapabilities
instanceKlass org/eclipse/lsp4j/DiagnosticWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/InlineValueWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/InlayHintWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/CodeLensWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/SemanticTokensWorkspaceCapabilities
instanceKlass org/eclipse/lsp4j/DynamicRegistrationCapabilities
instanceKlass org/eclipse/lsp4j/WorkspaceEditCapabilities
instanceKlass org/eclipse/lsp4j/GeneralClientCapabilities
instanceKlass org/eclipse/lsp4j/NotebookDocumentClientCapabilities
instanceKlass org/eclipse/lsp4j/WindowClientCapabilities
instanceKlass org/eclipse/lsp4j/TextDocumentClientCapabilities
instanceKlass org/eclipse/lsp4j/WorkspaceClientCapabilities
instanceKlass org/eclipse/lsp4j/ClientCapabilities
instanceKlass com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$FieldsData
instanceKlass org/eclipse/lsp4j/jsonrpc/validation/NonNull
instanceKlass com/google/gson/annotations/SerializedName
instanceKlass org/eclipse/lsp4j/ClientInfo
instanceKlass com/google/gson/internal/Primitives
instanceKlass  @bci com/google/gson/internal/ConstructorConstructor newDefaultConstructor (Ljava/lang/Class;Lcom/google/gson/ReflectionAccessFilter$FilterResult;)Lcom/google/gson/internal/ObjectConstructor; 130 <appendix> member <vmtarget> ; # com/google/gson/internal/ConstructorConstructor$$Lambda+0x000002962f30c400
instanceKlass com/google/gson/internal/ObjectConstructor
instanceKlass com/google/gson/internal/ReflectionAccessFilterHelper
instanceKlass org/eclipse/lsp4j/adapters/InitializeParamsTypeAdapter$Factory
instanceKlass com/google/gson/annotations/JsonAdapter
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Tuple$Two
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Tuple
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/TypeUtils
instanceKlass com/google/gson/internal/reflect/ReflectionHelper$RecordHelper
instanceKlass com/google/gson/internal/reflect/ReflectionHelper
instanceKlass com/google/gson/internal/JsonReaderInternalAccess
instanceKlass sun/nio/ch/WindowsAsynchronousFileChannelImpl$ReadTask
instanceKlass sun/nio/ch/Iocp$ResultHandler
instanceKlass java/text/CalendarBuilder
instanceKlass java/text/ParsePosition
instanceKlass sun/nio/ch/PendingFuture
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageProducer$Headers
instanceKlass org/eclipse/jdt/ls/core/internal/LogReader
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ConcurrentMessageProcessor$1
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler <init> (Lorg/eclipse/jdt/ls/core/internal/managers/ProjectsManager;Lorg/eclipse/jdt/ls/core/internal/JavaClientConnection;Lorg/eclipse/jdt/ls/core/internal/handlers/BaseDocumentLifeCycleHandler;)V 35 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler$$Lambda+0x000002962f30e350
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler
instanceKlass org/eclipse/lsp4j/jsonrpc/StandardLauncher
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ConcurrentMessageProcessor
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageProducer
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 80 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x000002962f2e0b68
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/EndpointProxy <init> (Lorg/eclipse/lsp4j/jsonrpc/Endpoint;Ljava/util/Collection;)V 197 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/EndpointProxy$$Lambda+0x000002962f30a0d0
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/EndpointProxy <init> (Lorg/eclipse/lsp4j/jsonrpc/Endpoint;Ljava/util/Collection;)V 178 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/EndpointProxy$$Lambda+0x000002962f309ea8
instanceKlass org/eclipse/lsp4j/jsonrpc/services/EndpointProxy
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint <clinit> ()V 11 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x000002962f309a50
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/ResponseError
instanceKlass org/eclipse/lsp4j/jsonrpc/RemoteEndpoint
instanceKlass  @bci java/lang/reflect/Executable sharedToString (IZ[Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/String; 29 <appendix> argL0 ; # java/lang/reflect/Executable$$Lambda+0x000002962f2e0938
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 24 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x000002962f308238
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint 220 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f30c000
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$1 (Ljava/lang/Object;Lorg/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$MethodInfo;)V 3 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x000002962f308000
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 7 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x000002962f2c7d40
instanceKlass org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/ParentProcessWatcher apply (Lorg/eclipse/lsp4j/jsonrpc/MessageConsumer;)Lorg/eclipse/lsp4j/jsonrpc/MessageConsumer; 2 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/ParentProcessWatcher$$Lambda+0x000002962f2a7ab0
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Message
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageConsumer
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MessageConstants
instanceKlass com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField
instanceKlass com/google/gson/internal/bind/ReflectiveTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/MapTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/CollectionTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/ArrayTypeAdapter$1
instanceKlass com/google/gson/internal/bind/DefaultDateTypeAdapter$1
instanceKlass java/util/concurrent/atomic/AtomicLongArray
instanceKlass com/google/gson/internal/bind/NumberTypeAdapter$1
instanceKlass com/google/gson/internal/bind/ObjectTypeAdapter$1
instanceKlass com/google/gson/internal/bind/EnumTypeAdapter$1
instanceKlass com/google/gson/internal/bind/TypeAdapters$31
instanceKlass java/util/Currency
instanceKlass com/google/gson/internal/bind/TypeAdapters$32
instanceKlass java/util/concurrent/atomic/AtomicIntegerArray
instanceKlass com/google/gson/internal/bind/TypeAdapters$30
instanceKlass java/util/BitSet
instanceKlass com/google/gson/internal/bind/TypeAdapters$29
instanceKlass com/google/gson/internal/bind/TypeAdapters
instanceKlass com/google/gson/internal/ConstructorConstructor
instanceKlass com/google/gson/internal/sql/SqlTimestampTypeAdapter$1
instanceKlass com/google/gson/internal/sql/SqlTimeTypeAdapter$1
instanceKlass com/google/gson/internal/sql/SqlDateTypeAdapter$1
instanceKlass com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType
instanceKlass com/google/gson/internal/sql/SqlTypesSupport
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/MessageTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/EnumTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/TupleTypeAdapters$TwoTypeAdapterFactory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/EitherTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/ThrowableTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/CollectionTypeAdapter$Factory
instanceKlass com/google/gson/FormattingStyle
instanceKlass com/google/gson/stream/JsonReader
instanceKlass com/google/gson/stream/JsonWriter
instanceKlass com/google/gson/ToNumberStrategy
instanceKlass com/google/gson/Gson
instanceKlass com/google/gson/internal/Excluder
instanceKlass com/google/gson/FieldNamingStrategy
instanceKlass com/google/gson/GsonBuilder
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/CancelParams
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MessageJsonHandler
instanceKlass org/eclipse/lsp4j/DidCloseNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidChangeNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidSaveNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidOpenNotebookDocumentParams
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionResolveHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/HoverHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/NavigateToTypeDefinitionHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/NavigateToDefinitionHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/DocumentSymbolHandler
instanceKlass org/eclipse/lsp4j/TextDocumentIdentifier
instanceKlass org/eclipse/lsp4j/WorkspaceDiagnosticReport
instanceKlass org/eclipse/lsp4j/adapters/WorkspaceSymbolResponseAdapter
instanceKlass org/eclipse/lsp4j/DidChangeWorkspaceFoldersParams
instanceKlass org/eclipse/lsp4j/WorkspaceSymbol
instanceKlass org/eclipse/lsp4j/DidChangeConfigurationParams
instanceKlass org/eclipse/lsp4j/DidChangeWatchedFilesParams
instanceKlass org/eclipse/lsp4j/RenameFilesParams
instanceKlass org/eclipse/lsp4j/DeleteFilesParams
instanceKlass org/eclipse/lsp4j/CreateFilesParams
instanceKlass org/eclipse/lsp4j/SemanticTokensDelta
instanceKlass org/eclipse/lsp4j/adapters/SemanticTokensFullDeltaResponseAdapter
instanceKlass org/eclipse/lsp4j/CallHierarchyOutgoingCall
instanceKlass org/eclipse/lsp4j/CallHierarchyIncomingCall
instanceKlass org/eclipse/lsp4j/CallHierarchyItem
instanceKlass org/eclipse/lsp4j/TypeHierarchyItem
instanceKlass org/eclipse/lsp4j/Hover
instanceKlass org/eclipse/lsp4j/InlineValueEvaluatableExpression
instanceKlass org/eclipse/lsp4j/InlineValueVariableLookup
instanceKlass org/eclipse/lsp4j/adapters/InlineValueResponseAdapter
instanceKlass org/eclipse/lsp4j/LinkedEditingRanges
instanceKlass org/eclipse/lsp4j/ColorInformation
instanceKlass org/eclipse/lsp4j/PrepareRenameDefaultBehavior
instanceKlass org/eclipse/lsp4j/PrepareRenameResult
instanceKlass org/eclipse/lsp4j/Range
instanceKlass org/eclipse/lsp4j/adapters/PrepareRenameResponseAdapter
instanceKlass org/eclipse/lsp4j/ColorPresentation
instanceKlass org/eclipse/lsp4j/TextEdit
instanceKlass org/eclipse/lsp4j/SelectionRange
instanceKlass org/eclipse/lsp4j/FoldingRange
instanceKlass org/eclipse/lsp4j/DocumentSymbol
instanceKlass org/eclipse/lsp4j/SymbolInformation
instanceKlass org/eclipse/lsp4j/adapters/DocumentSymbolResponseAdapter
instanceKlass org/eclipse/lsp4j/SignatureHelp
instanceKlass org/eclipse/lsp4j/SemanticTokens
instanceKlass org/eclipse/lsp4j/Moniker
instanceKlass org/eclipse/lsp4j/Command
instanceKlass org/eclipse/lsp4j/adapters/CodeActionResponseAdapter
instanceKlass org/eclipse/lsp4j/adapters/DocumentDiagnosticReportTypeAdapter
instanceKlass org/eclipse/lsp4j/CompletionList
instanceKlass org/eclipse/lsp4j/LocationLink
instanceKlass org/eclipse/lsp4j/Location
instanceKlass com/google/gson/internal/GsonTypes$WildcardTypeImpl
instanceKlass com/google/gson/internal/GsonPreconditions
instanceKlass com/google/gson/internal/GsonTypes$ParameterizedTypeImpl
instanceKlass com/google/gson/internal/GsonTypes
instanceKlass com/google/gson/TypeAdapter
instanceKlass com/google/gson/reflect/TypeToken
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass org/eclipse/lsp4j/adapters/LocationLinkListAdapter
instanceKlass com/google/gson/TypeAdapterFactory
instanceKlass org/eclipse/lsp4j/WorkspaceEdit
instanceKlass org/eclipse/lsp4j/DocumentLink
instanceKlass org/eclipse/lsp4j/CompletionItem
instanceKlass org/eclipse/lsp4j/DidCloseTextDocumentParams
instanceKlass org/eclipse/lsp4j/DidChangeTextDocumentParams
instanceKlass org/eclipse/lsp4j/DocumentFormattingParams
instanceKlass org/eclipse/lsp4j/DidSaveTextDocumentParams
instanceKlass org/eclipse/lsp4j/DidOpenTextDocumentParams
instanceKlass org/eclipse/lsp4j/InlineValueParams
instanceKlass org/eclipse/lsp4j/InlayHint
instanceKlass org/eclipse/lsp4j/CodeAction
instanceKlass org/eclipse/lsp4j/DocumentRangeFormattingParams
instanceKlass org/eclipse/lsp4j/CodeLens
instanceKlass org/eclipse/lsp4j/InlayHintParams
instanceKlass org/eclipse/lsp4j/WorkDoneProgressAndPartialResultParams
instanceKlass org/eclipse/lsp4j/WillSaveTextDocumentParams
instanceKlass org/eclipse/lsp4j/InitializeResult
instanceKlass org/eclipse/lsp4j/services/NotebookDocumentService
instanceKlass org/eclipse/lsp4j/WorkDoneProgressCancelParams
instanceKlass org/eclipse/lsp4j/SetTraceParams
instanceKlass org/eclipse/lsp4j/InitializedParams
instanceKlass org/eclipse/lsp4j/InitializeParams
instanceKlass org/eclipse/jdt/ls/core/internal/JavaClientConnection
instanceKlass org/eclipse/lsp4j/jsonrpc/CancelChecker
instanceKlass jdk/internal/vm/annotation/IntrinsicCandidate
instanceKlass java/lang/Deprecated
instanceKlass org/eclipse/lsp4j/jsonrpc/json/JsonRpcMethodProvider
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonDelegate
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints getSupportedMethods (Ljava/lang/Class;Ljava/util/Set;)Ljava/util/Map; 29 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints$$Lambda+0x000002962f2bfae0
instanceKlass org/eclipse/jdt/ls/core/internal/StatusReport
instanceKlass org/eclipse/jdt/ls/core/internal/ProgressReport
instanceKlass org/eclipse/jdt/ls/core/internal/EventNotification
instanceKlass org/eclipse/jdt/ls/core/internal/ActionableNotification
instanceKlass org/eclipse/lsp4j/ExecuteCommandParams
instanceKlass org/eclipse/lsp4j/ApplyWorkspaceEditResponse
instanceKlass org/eclipse/lsp4j/ShowDocumentResult
instanceKlass org/eclipse/lsp4j/MessageActionItem
instanceKlass org/eclipse/lsp4j/WorkspaceFolder
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonNotification
instanceKlass org/eclipse/lsp4j/jsonrpc/json/JsonRpcMethod
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ResponseJsonAdapter
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil getParameterTypes (Ljava/lang/reflect/Method;)[Ljava/lang/reflect/Type; 17 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$$Lambda+0x000002962f2bf078
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil getParameterTypes (Ljava/lang/reflect/Method;)[Ljava/lang/reflect/Type; 7 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$$Lambda+0x000002962f2bee48
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonRequest
instanceKlass org/eclipse/lsp4j/LogTraceParams
instanceKlass org/eclipse/lsp4j/ApplyWorkspaceEditParams
instanceKlass org/eclipse/lsp4j/ProgressParams
instanceKlass org/eclipse/lsp4j/ShowDocumentParams
instanceKlass org/eclipse/lsp4j/WorkDoneProgressCreateParams
instanceKlass org/eclipse/lsp4j/MessageParams
instanceKlass org/eclipse/lsp4j/PublishDiagnosticsParams
instanceKlass org/eclipse/lsp4j/UnregistrationParams
instanceKlass org/eclipse/lsp4j/RegistrationParams
instanceKlass java/util/concurrent/CompletableFuture
instanceKlass org/eclipse/lsp4j/ConfigurationParams
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonSegment
instanceKlass org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints getSupportedMethods (Ljava/lang/Class;Ljava/util/Set;)Ljava/util/Map; 11 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints$$Lambda+0x000002962f2be3f8
instanceKlass org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$MethodInfo
instanceKlass org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MethodProvider
instanceKlass org/eclipse/lsp4j/jsonrpc/Endpoint
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageProducer
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageConsumer
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageIssueHandler
instanceKlass org/eclipse/lsp4j/jsonrpc/Launcher$Builder
instanceKlass org/eclipse/lsp4j/jsonrpc/Launcher
instanceKlass org/eclipse/jdt/ls/core/internal/JavaClientConnection$JavaLanguageClient
instanceKlass org/eclipse/jdt/ls/core/internal/lsp/ExecuteCommandProposedClient
instanceKlass org/eclipse/lsp4j/services/LanguageClient
instanceKlass org/eclipse/jdt/core/JavaCore$1
instanceKlass java/util/concurrent/ForkJoinPool$2
instanceKlass jdk/internal/access/JavaUtilConcurrentFJPAccess
instanceKlass java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$WorkQueue
instanceKlass java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
instanceKlass org/eclipse/core/internal/content/ContentTypeHandler
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller callOnce (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;Ljava/util/function/Consumer;)Z 14 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002962f1d4c68
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeManager$DebuggingHolder <clinit> ()V 9 <appendix> member <vmtarget> ; # org/eclipse/core/internal/content/ContentTypeManager$DebuggingHolder$$Lambda+0x000002962f2b99f0
instanceKlass  @cpi org/eclipse/core/internal/content/ContentTypeManager$DebuggingHolder 59 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f2bd000
instanceKlass org/eclipse/core/internal/content/ContentTypeManager$DebuggingHolder
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeBuilder applyPreferences (Lorg/eclipse/core/internal/content/ContentTypeCatalog;Lorg/eclipse/core/runtime/preferences/IEclipsePreferences;Z)V 4 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f2bcc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f2bc800
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeBuilder applyPreferences (Lorg/eclipse/core/internal/content/ContentTypeCatalog;Lorg/eclipse/core/runtime/preferences/IEclipsePreferences;Z)V 4 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f2bc400
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeBuilder applyPreferences (Lorg/eclipse/core/internal/content/ContentTypeCatalog;Lorg/eclipse/core/runtime/preferences/IEclipsePreferences;Z)V 4 <appendix> member <vmtarget> ; # org/eclipse/core/internal/content/ContentTypeBuilder$$Lambda+0x000002962f2b95e0
instanceKlass  @cpi org/eclipse/core/internal/content/ContentTypeBuilder 424 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f2bc000
instanceKlass org/eclipse/core/runtime/preferences/IPreferenceNodeVisitor
instanceKlass org/eclipse/core/internal/content/FileSpec
instanceKlass org/eclipse/core/internal/content/ContentType
instanceKlass org/eclipse/core/internal/content/Util
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 118 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x000002962f2b8798
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 109 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x000002962f2b8510
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 100 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x000002962f2b8288
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 91 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x000002962f2b8000
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 82 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x000002962f2a8c00
instanceKlass org/eclipse/jdt/internal/core/index/Index
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryType
instanceKlass org/eclipse/jdt/core/search/SearchMatch
instanceKlass org/eclipse/jdt/internal/compiler/lookup/IQualifiedTypeResolutionListener
instanceKlass org/eclipse/jdt/internal/core/search/matching/MatchLocator
instanceKlass org/eclipse/jdt/internal/core/search/IndexSelector
instanceKlass org/eclipse/jdt/core/search/SearchDocument
instanceKlass org/eclipse/jdt/internal/core/search/PatternSearchJob
instanceKlass org/eclipse/jdt/internal/core/search/matching/TypeDeclarationPattern$PackageNameSet
instanceKlass org/eclipse/jdt/ls/core/internal/ParentProcessWatcher
instanceKlass sun/nio/ch/PendingIoCache
instanceKlass sun/nio/ch/Invoker$GroupAndInvokeCount
instanceKlass sun/nio/ch/Invoker
instanceKlass sun/nio/ch/AsynchronousChannelGroupImpl$1
instanceKlass sun/nio/ch/AsynchronousChannelGroupImpl$2
instanceKlass sun/nio/ch/Iocp$EventHandlerTask
instanceKlass  @bci sun/nio/ch/ThreadPool defaultThreadFactory ()Ljava/util/concurrent/ThreadFactory; 6 <appendix> argL0 ; # sun/nio/ch/ThreadPool$$Lambda+0x000002962f1b9a88
instanceKlass sun/nio/ch/ThreadPool
instanceKlass sun/nio/ch/Iocp$CompletionStatus
instanceKlass java/nio/channels/AsynchronousChannelGroup
instanceKlass sun/nio/ch/WindowsAsynchronousFileChannelImpl$DefaultIocpHolder
instanceKlass org/eclipse/jdt/internal/core/search/TypeNameRequestorWrapper
instanceKlass sun/nio/ch/Groupable
instanceKlass sun/nio/ch/Iocp$OverlappedChannel
instanceKlass org/eclipse/jdt/internal/core/search/AbstractSearchScope
instanceKlass org/eclipse/jdt/internal/core/search/IRestrictedAccessMethodRequestor
instanceKlass org/eclipse/jdt/core/search/ISearchPattern
instanceKlass org/eclipse/jdt/core/search/SearchEngine
instanceKlass java/nio/channels/AsynchronousFileChannel
instanceKlass java/net/ProtocolFamily
instanceKlass org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory$PipeStreamProvider
instanceKlass org/eclipse/jdt/internal/core/ModelUpdater
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory getPipeFile ()Ljava/io/File; 58 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory$$Lambda+0x000002962f2a50d8
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$11
instanceKlass java/lang/ProcessHandleImpl$Info
instanceKlass java/lang/ProcessHandle$Info
instanceKlass  @bci java/lang/ProcessImpl <init> ([Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[JZZ)V 286 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f2a8800
instanceKlass  @bci java/lang/ProcessHandleImpl lambda$static$1 ()Ljava/util/concurrent/Executor; 45 <appendix> member <vmtarget> ; # java/lang/ProcessHandleImpl$$Lambda+0x000002962f1b75c0
instanceKlass  @cpi java/lang/ProcessHandleImpl 436 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f2a8400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f2a8000
instanceKlass  @bci java/lang/ProcessHandleImpl <clinit> ()V 32 <appendix> argL0 ; # java/lang/ProcessHandleImpl$$Lambda+0x000002962f1b73b0
instanceKlass java/lang/ProcessHandleImpl
instanceKlass java/lang/ProcessHandle
instanceKlass org/eclipse/jdt/ls/core/internal/JobHelpers$ProjectRegistryRefreshJobMatcher
instanceKlass org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory$StreamProvider
instanceKlass org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory
instanceKlass org/eclipse/jdt/ls/core/internal/JobHelpers$IJobMatcher
instanceKlass org/eclipse/jdt/core/manipulation/CoreASTProvider$WAIT_FLAG
instanceKlass org/eclipse/jdt/ls/core/internal/JobHelpers
instanceKlass org/eclipse/jdt/core/manipulation/CoreASTProvider
instanceKlass org/eclipse/jdt/ls/core/internal/MovingAverage
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/BaseDiagnosticsHandler
instanceKlass org/eclipse/jdt/core/IProblemRequestor
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/BaseDocumentLifeCycleHandler
instanceKlass org/eclipse/lsp4j/WorkDoneProgressParams
instanceKlass org/eclipse/lsp4j/PartialResultParams
instanceKlass org/eclipse/lsp4j/TextDocumentPositionParams
instanceKlass java/util/concurrent/Executors$DefaultThreadFactory
instanceKlass org/eclipse/jdt/ls/core/internal/ProjectUtils
instanceKlass com/google/gson/JsonElement
instanceKlass org/eclipse/jdt/ls/core/internal/managers/TelemetryManager
instanceKlass org/eclipse/core/runtime/jobs/ProgressProvider
instanceKlass org/eclipse/jdt/ls/core/internal/LanguageServerApplication
instanceKlass org/eclipse/equinox/app/IApplication
instanceKlass org/eclipse/core/runtime/internal/adaptor/EclipseAppLauncher
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/BundleUtils
instanceKlass com/sun/jna/platform/win32/WinNT$OVERLAPPED_COMPLETION_ROUTINE
instanceKlass com/sun/jna/win32/StdCallLibrary$StdCallCallback
instanceKlass com/sun/jna/platform/win32/Kernel32
instanceKlass com/sun/jna/platform/win32/Wincon
instanceKlass com/sun/jna/platform/win32/WinNT
instanceKlass com/sun/jna/platform/win32/WinBase
instanceKlass com/sun/jna/platform/win32/BaseTSD
instanceKlass com/sun/jna/platform/win32/WinError
instanceKlass com/sun/jna/platform/win32/WinBase$EnumResNameProc
instanceKlass com/sun/jna/platform/win32/WinBase$EnumResTypeProc
instanceKlass com/sun/jna/platform/win32/Kernel32Util
instanceKlass com/sun/jna/platform/win32/WinDef
instanceKlass org/eclipse/core/internal/net/StringUtil
instanceKlass org/eclipse/core/net/internal/proxy/win32/ProxyBypass
instanceKlass org/eclipse/core/net/internal/proxy/win32/ProxyProviderUtil
instanceKlass org/eclipse/core/net/internal/proxy/win32/StaticProxyConfig
instanceKlass  @bci jdk/internal/reflect/MethodHandleShortFieldAccessorImpl setShort (Ljava/lang/Object;S)V 41 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f28c000
instanceKlass com/sun/jna/Structure$ByReference
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f28bc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f28b800
instanceKlass  @bci jdk/internal/reflect/MethodHandleShortFieldAccessorImpl getShort (Ljava/lang/Object;)S 20 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f28b400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f28b000
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000002962f28ac00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f28a800
instanceKlass com/sun/jna/Structure$StructField
instanceKlass com/sun/jna/Structure$LayoutInfo
instanceKlass java/lang/Class$AnnotationData
instanceKlass java/lang/annotation/Documented
instanceKlass com/sun/jna/Structure$FieldOrder
instanceKlass  @bci com/sun/jna/Structure fieldOrder ()Ljava/util/List; 84 <appendix> member <vmtarget> ; # com/sun/jna/Structure$$Lambda+0x000002962f286878
instanceKlass com/sun/jna/Klass
instanceKlass com/sun/jna/NativeMappedConverter
instanceKlass  @bci com/sun/jna/Structure getFieldList ()Ljava/util/List; 84 <appendix> member <vmtarget> ; # com/sun/jna/Structure$$Lambda+0x000002962f2861d8
instanceKlass  @bci com/sun/jna/Structure validateFields ()V 75 <appendix> member <vmtarget> ; # com/sun/jna/Structure$$Lambda+0x000002962f285fa0
instanceKlass org/eclipse/equinox/internal/security/storage/SecurePreferencesWrapper
instanceKlass org/eclipse/equinox/security/storage/ISecurePreferences
instanceKlass org/eclipse/equinox/internal/security/storage/SecurePreferencesContainer
instanceKlass  @bci org/eclipse/equinox/internal/security/storage/SecurePreferencesRoot load ()V 241 <appendix> member <vmtarget> ; # org/eclipse/equinox/internal/security/storage/SecurePreferencesRoot$$Lambda+0x000002962f2892c8
instanceKlass javax/crypto/spec/PBEKeySpec
instanceKlass org/eclipse/equinox/internal/security/storage/PasswordExt
instanceKlass org/eclipse/equinox/internal/security/storage/JavaEncryption
instanceKlass org/eclipse/equinox/security/storage/provider/IPreferencesContainer
instanceKlass org/eclipse/equinox/internal/security/storage/SecurePreferences
instanceKlass org/eclipse/equinox/internal/security/storage/friends/IStorageConstants
instanceKlass org/eclipse/equinox/internal/security/storage/StorageUtils
instanceKlass org/eclipse/equinox/internal/security/storage/SecurePreferencesMapper
instanceKlass org/eclipse/equinox/security/storage/SecurePreferencesFactory
instanceKlass com/sun/jna/Function$PostCallRead
instanceKlass com/sun/jna/Memory$MemoryDisposer
instanceKlass com/sun/jna/WeakMemoryHolder
instanceKlass com/sun/jna/NativeString
instanceKlass com/sun/jna/Library$Handler$FunctionInfo
instanceKlass com/sun/jna/VarArgsChecker
instanceKlass com/sun/jna/internal/ReflectionUtils
instanceKlass com/sun/jna/Native$3
instanceKlass com/sun/jna/NativeLibrary$NativeLibraryDisposer
instanceKlass com/sun/jna/internal/Cleaner$Cleanable
instanceKlass com/sun/jna/internal/Cleaner
instanceKlass com/sun/jna/NativeLibrary$1
instanceKlass com/sun/jna/SymbolProvider
instanceKlass com/sun/jna/NativeLibrary
instanceKlass com/sun/jna/Library$Handler
instanceKlass com/sun/jna/Native$2
instanceKlass com/sun/jna/Structure$FFIType$FFITypes
instanceKlass com/sun/jna/Native$ffi_callback
instanceKlass com/sun/jna/JNIEnv
instanceKlass com/sun/jna/PointerType
instanceKlass com/sun/jna/NativeMapped
instanceKlass com/sun/jna/WString
instanceKlass com/sun/jna/win32/DLLCallback
instanceKlass com/sun/jna/CallbackProxy
instanceKlass com/sun/jna/Callback
instanceKlass com/sun/jna/Structure$ByValue
instanceKlass com/sun/jna/ToNativeContext
instanceKlass com/sun/jna/Structure
instanceKlass com/sun/jna/Pointer
instanceKlass jdk/internal/loader/NativeLibraries$Unloader
instanceKlass java/io/File$TempDirectory
instanceKlass com/sun/jna/Native$5
instanceKlass com/sun/jna/Platform
instanceKlass com/sun/jna/Native$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/LoggerFinderLoader
instanceKlass  @bci java/lang/System$LoggerFinder accessProvider ()Ljava/lang/System$LoggerFinder; 8 <appendix> argL0 ; # java/lang/System$LoggerFinder$$Lambda+0x000002962f1b3568
instanceKlass  @bci java/util/logging/Level$KnownLevel findByName (Ljava/lang/String;Ljava/util/function/Function;)Ljava/util/Optional; 29 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000025
instanceKlass  @bci java/util/logging/Level findLevel (Ljava/lang/String;)Ljava/util/logging/Level; 13 <appendix> argL0 ; # java/util/logging/Level$$Lambda+0x800000013
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/logging/LogManager$2
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 49 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000024
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 19 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000023
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass com/sun/jna/FromNativeContext
instanceKlass com/sun/jna/Callback$UncaughtExceptionHandler
instanceKlass com/sun/jna/Native
instanceKlass com/sun/jna/Version
instanceKlass com/sun/jna/win32/W32APIFunctionMapper
instanceKlass com/sun/jna/win32/W32APITypeMapper$2
instanceKlass com/sun/jna/DefaultTypeMapper$Entry
instanceKlass com/sun/jna/win32/W32APITypeMapper$1
instanceKlass com/sun/jna/TypeConverter
instanceKlass com/sun/jna/ToNativeConverter
instanceKlass com/sun/jna/FromNativeConverter
instanceKlass com/sun/jna/DefaultTypeMapper
instanceKlass com/sun/jna/TypeMapper
instanceKlass com/sun/jna/FunctionMapper
instanceKlass com/sun/jna/win32/W32APIOptions
instanceKlass org/eclipse/core/net/internal/proxy/win32/ProxyProviderWin32$WinHttp
instanceKlass com/sun/jna/win32/StdCallLibrary
instanceKlass com/sun/jna/win32/StdCall
instanceKlass com/sun/jna/AltCallingConvention
instanceKlass org/eclipse/core/internal/net/ProxyData
instanceKlass com/sun/jna/Library
instanceKlass org/eclipse/equinox/internal/security/auth/AuthPlugin
instanceKlass org/eclipse/core/internal/net/ProxyType
instanceKlass org/eclipse/core/net/proxy/IProxyChangeEvent
instanceKlass org/eclipse/core/internal/net/AbstractProxyProvider
instanceKlass org/eclipse/core/internal/net/ProxyManager
instanceKlass org/eclipse/core/net/proxy/IProxyService
instanceKlass  @bci org/eclipse/core/internal/net/Policy <clinit> ()V 8 <appendix> argL0 ; # org/eclipse/core/internal/net/Policy$$Lambda+0x000002962f2755e8
instanceKlass org/eclipse/core/internal/net/Policy
instanceKlass org/eclipse/core/net/proxy/IProxyData
instanceKlass org/eclipse/core/internal/net/PreferenceManager
instanceKlass org/eclipse/core/internal/net/Activator
instanceKlass org/eclipse/core/internal/net/ProxySelector
instanceKlass java/text/DontCareFieldPosition$1
instanceKlass java/io/FileOutputStream$1
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogEntryImpl
instanceKlass org/eclipse/equinox/log/ExtendedLogEntry
instanceKlass java/lang/StackTraceElement$HashedModules
instanceKlass org/eclipse/osgi/framework/log/FrameworkLogEntry
instanceKlass org/eclipse/jdt/ls/core/internal/DiagnosticsState
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ContentProviderManager
instanceKlass org/eclipse/jdt/ls/core/internal/managers/DigestStore
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Either
instanceKlass com/sun/org/apache/xerces/internal/dom/DeepNodeListImpl
instanceKlass com/sun/org/apache/xerces/internal/dom/DeferredDocumentImpl$RefCount
instanceKlass com/sun/org/apache/xerces/internal/dom/DeferredNode
instanceKlass org/eclipse/text/templates/TemplateStoreCore
instanceKlass com/sun/org/apache/xml/internal/serializer/WriterChain
instanceKlass com/sun/org/apache/xalan/internal/xsltc/trax/DOM2TO
instanceKlass javax/xml/transform/stax/StAXSource
instanceKlass javax/xml/transform/sax/SAXSource
instanceKlass javax/xml/transform/stream/StreamSource
instanceKlass com/sun/org/apache/xml/internal/serializer/NamespaceMappings$MappingRecord
instanceKlass com/sun/org/apache/xml/internal/serializer/NamespaceMappings
instanceKlass sun/nio/cs/DelegatableDecoder
instanceKlass java/nio/charset/Charset$1
instanceKlass java/nio/charset/Charset$2
instanceKlass java/nio/charset/Charset$ThreadTrackHolder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass java/nio/charset/Charset$ExtendedProviderHolder$1
instanceKlass java/nio/charset/Charset$ExtendedProviderHolder
instanceKlass  @bci jdk/xml/internal/SecuritySupport getResourceAsStream (Ljava/lang/String;)Ljava/io/InputStream; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002962f1ab5a0
instanceKlass com/sun/org/apache/xml/internal/serializer/Encodings$EncodingInfos
instanceKlass com/sun/org/apache/xml/internal/serializer/Encodings
instanceKlass com/sun/org/apache/xml/internal/serializer/ToStream$CharacterBuffer
instanceKlass com/sun/org/apache/xml/internal/serializer/EncodingInfo
instanceKlass com/sun/org/apache/xml/internal/serializer/ToStream$BoolStack
instanceKlass com/sun/org/apache/xml/internal/serializer/ElemContext
instanceKlass org/xml/sax/helpers/AttributesImpl
instanceKlass com/sun/org/apache/xml/internal/serializer/CharInfo$CharKey
instanceKlass sun/util/ResourceBundleEnumeration
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper loadPropertyResourceBundle (Ljava/lang/Module;Ljava/lang/Module;Ljava/lang/String;Ljava/util/Locale;)Ljava/util/ResourceBundle; 14 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x000002962f1a9cd8
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper loadResourceBundle (Ljava/lang/Module;Ljava/lang/Module;Ljava/lang/String;Ljava/util/Locale;)Ljava/util/ResourceBundle; 13 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x000002962f1a9ac0
instanceKlass java/util/ResourceBundle$3
instanceKlass com/sun/org/apache/xml/internal/serializer/CharInfo
instanceKlass com/sun/org/apache/xml/internal/serializer/SerializerBase
instanceKlass com/sun/org/apache/xml/internal/serializer/SerializerConstants
instanceKlass com/sun/org/apache/xml/internal/serializer/SerializationHandler
instanceKlass com/sun/org/apache/xml/internal/serializer/Serializer
instanceKlass com/sun/org/apache/xml/internal/serializer/DOMSerializer
instanceKlass org/xml/sax/ext/DeclHandler
instanceKlass com/sun/org/apache/xml/internal/serializer/XSLOutputAttributes
instanceKlass com/sun/org/apache/xml/internal/serializer/ExtendedLexicalHandler
instanceKlass org/xml/sax/ext/LexicalHandler
instanceKlass com/sun/org/apache/xml/internal/serializer/ExtendedContentHandler
instanceKlass javax/xml/transform/dom/DOMResult
instanceKlass javax/xml/transform/stax/StAXResult
instanceKlass javax/xml/transform/sax/SAXResult
instanceKlass com/sun/org/apache/xalan/internal/xsltc/runtime/output/TransletOutputHandlerFactory
instanceKlass javax/xml/transform/dom/DOMSource
instanceKlass com/sun/org/apache/xml/internal/utils/XMLReaderManager
instanceKlass com/sun/org/apache/xml/internal/serializer/OutputPropertiesFactory
instanceKlass javax/xml/transform/Transformer
instanceKlass com/sun/org/apache/xalan/internal/xsltc/DOMCache
instanceKlass  @bci javax/xml/catalog/CatalogFeatures setProperties (Ljavax/xml/catalog/CatalogFeatures$Builder;)V 5 <appendix> member <vmtarget> ; # javax/xml/catalog/CatalogFeatures$$Lambda+0x000002962f1a4658
instanceKlass javax/xml/catalog/CatalogMessages
instanceKlass javax/xml/catalog/Util
instanceKlass  @bci javax/xml/transform/FactoryFinder newInstance (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/ClassLoader;Z)Ljava/lang/Object; 107 <appendix> member <vmtarget> ; # javax/xml/transform/FactoryFinder$$Lambda+0x000002962f1a3c30
instanceKlass jdk/xml/internal/JdkProperty
instanceKlass jdk/xml/internal/XMLSecurityManager
instanceKlass com/sun/org/apache/xalan/internal/utils/FeaturePropertyBase
instanceKlass jdk/xml/internal/JdkXmlFeatures
instanceKlass javax/xml/catalog/CatalogFeatures$Builder
instanceKlass javax/xml/catalog/CatalogFeatures
instanceKlass jdk/xml/internal/TransformErrorListener
instanceKlass javax/xml/transform/ErrorListener
instanceKlass com/sun/org/apache/xalan/internal/xsltc/compiler/SourceLoader
instanceKlass  @bci javax/xml/transform/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 123 <appendix> member <vmtarget> ; # javax/xml/transform/FactoryFinder$$Lambda+0x000002962f1a02a0
instanceKlass javax/xml/transform/FactoryFinder$1
instanceKlass  @bci javax/xml/transform/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 24 <appendix> member <vmtarget> ; # javax/xml/transform/FactoryFinder$$Lambda+0x000002962f19fe68
instanceKlass javax/xml/transform/FactoryFinder
instanceKlass javax/xml/transform/TransformerFactory
instanceKlass com/sun/org/apache/xerces/internal/dom/CharacterDataImpl$1
instanceKlass org/w3c/dom/Text
instanceKlass org/w3c/dom/CharacterData
instanceKlass org/w3c/dom/Attr
instanceKlass com/sun/org/apache/xerces/internal/dom/NamedNodeMapImpl
instanceKlass org/w3c/dom/NamedNodeMap
instanceKlass com/sun/org/apache/xerces/internal/dom/NodeListCache
instanceKlass org/w3c/dom/TypeInfo
instanceKlass org/w3c/dom/ElementTraversal
instanceKlass org/w3c/dom/Element
instanceKlass org/w3c/dom/DocumentType
instanceKlass com/sun/org/apache/xerces/internal/dom/NodeImpl
instanceKlass org/w3c/dom/events/EventTarget
instanceKlass org/w3c/dom/NodeList
instanceKlass org/w3c/dom/Document
instanceKlass org/w3c/dom/ranges/DocumentRange
instanceKlass org/w3c/dom/events/DocumentEvent
instanceKlass org/w3c/dom/traversal/DocumentTraversal
instanceKlass javax/xml/parsers/DocumentBuilder
instanceKlass javax/xml/parsers/DocumentBuilderFactory
instanceKlass org/eclipse/core/internal/runtime/XmlProcessorFactory
instanceKlass javax/xml/transform/stream/StreamResult
instanceKlass javax/xml/transform/Source
instanceKlass javax/xml/transform/Result
instanceKlass org/w3c/dom/Node
instanceKlass org/eclipse/text/templates/TemplateReaderWriter
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/preferences/PreferenceManager reloadTemplateStore ()V 24 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/preferences/PreferenceManager$$Lambda+0x000002962f2660e8
instanceKlass org/eclipse/text/templates/TemplatePersistenceData
instanceKlass org/eclipse/jface/text/templates/Template
instanceKlass java/util/ResourceBundle$Control$2
instanceKlass java/util/ResourceBundle$CacheKeyReference
instanceKlass java/util/ResourceBundle$CacheKey
instanceKlass  @bci java/util/ResourceBundle getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/util/ResourceBundle$$Lambda+0x000002962f196160
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleControlProviderHolder lambda$static$0 ()Ljava/util/List; 11 <appendix> argL0 ; # java/util/ResourceBundle$ResourceBundleControlProviderHolder$$Lambda+0x000002962f195f30
instanceKlass java/util/ServiceLoader$ProviderSpliterator
instanceKlass java/util/spi/ResourceBundleControlProvider
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleControlProviderHolder <clinit> ()V 0 <appendix> argL0 ; # java/util/ResourceBundle$ResourceBundleControlProviderHolder$$Lambda+0x000002962f1958a0
instanceKlass java/util/ResourceBundle$ResourceBundleControlProviderHolder
instanceKlass org/eclipse/jface/text/templates/TextTemplateMessages
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager$TouchJob runInWorkspace (Lorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/core/runtime/IStatus; 24 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/JavaModelManager$TouchJob$$Lambda+0x000002962f2638f0
instanceKlass org/eclipse/jface/text/IRegion
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/FormatterHandler
instanceKlass org/eclipse/jdt/internal/core/util/Util
instanceKlass org/apache/commons/lang3/text/StrTokenizer
instanceKlass org/apache/commons/lang3/text/StrBuilder
instanceKlass org/apache/commons/lang3/builder/Builder
instanceKlass  @bci java/util/regex/CharPredicates forUnicodeBlock (Ljava/lang/String;)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/CharPredicates$$Lambda+0x000002962f194df8
instanceKlass java/lang/Character$Subset
instanceKlass org/apache/commons/lang3/StringUtils
instanceKlass java/util/DualPivotQuicksort
instanceKlass org/apache/commons/lang3/ArraySorter
instanceKlass org/apache/commons/lang3/text/StrMatcher
instanceKlass org/apache/commons/lang3/text/StrSubstitutor
instanceKlass org/apache/commons/lang3/text/StrLookup
instanceKlass org/eclipse/osgi/storage/bundlefile/ZipBundleFile$1
instanceKlass  @bci org/eclipse/osgi/storage/bundlefile/ZipBundleFile getPaths ()Ljava/lang/Iterable; 1 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/bundlefile/ZipBundleFile$$Lambda+0x000002962f1dc868
instanceKlass org/eclipse/jdt/ls/core/internal/ResourceUtils
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences$ReferencedLibraries
instanceKlass sun/security/provider/AbstractDrbg$NonceProvider
instanceKlass  @bci sun/security/provider/AbstractDrbg$SeederHolder <clinit> ()V 42 <appendix> member <vmtarget> ; # sun/security/provider/AbstractDrbg$SeederHolder$$Lambda+0x000002962f194020
instanceKlass  @cpi sun/security/provider/AbstractDrbg$SeederHolder 91 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f25ac00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f25a800
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/net/NetworkInterface$1
instanceKlass java/net/DefaultInterface
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass java/net/InetAddress$PlatformResolver
instanceKlass java/net/spi/InetAddressResolver
instanceKlass java/net/spi/InetAddressResolver$LookupPolicy
instanceKlass java/net/Inet4AddressImpl
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass jdk/internal/access/JavaNetInetAddressAccess
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder
instanceKlass java/security/DrbgParameters$NextBytes
instanceKlass  @bci sun/security/provider/AbstractDrbg <clinit> ()V 12 <appendix> argL0 ; # sun/security/provider/AbstractDrbg$$Lambda+0x000002962f191798
instanceKlass  @cpi sun/security/provider/AbstractDrbg 383 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f25a400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f25a000
instanceKlass sun/security/provider/EntropySource
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass  @bci sun/security/provider/DRBG <init> (Ljava/security/SecureRandomParameters;)V 26 <appendix> argL0 ; # sun/security/provider/DRBG$$Lambda+0x000002962f1902d8
instanceKlass java/security/SecureRandomSpi
instanceKlass java/security/SecureRandomParameters
instanceKlass java/util/UUID$Holder
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences
instanceKlass org/eclipse/jface/text/templates/TemplateContextType
instanceKlass org/eclipse/jface/text/templates/TemplateVariableResolver
instanceKlass sun/nio/ch/IOStatus
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/Util
instanceKlass java/nio/channels/NetworkChannel
instanceKlass sun/nio/ch/SelChImpl
instanceKlass sun/nio/ch/Streams
instanceKlass java/nio/channels/Channels
instanceKlass sun/nio/fs/WindowsChannelFactory$2
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo$TZCanonicalIDMapHolder
instanceKlass sun/util/resources/provider/NonBaseLocaleDataMetaInfo
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter createSupportedLocaleString (Ljava/lang/String;)Ljava/lang/String; 6 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x000002962f18ba98
instanceKlass sun/util/locale/provider/BaseLocaleDataMetaInfo
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getTimeZoneNameProvider ()Ljava/util/spi/TimeZoneNameProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x000002962f18b638
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getTimeZoneNameProvider ()Ljava/util/spi/TimeZoneNameProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x000002962f18afc0
instanceKlass sun/util/locale/provider/TimeZoneNameUtility$TimeZoneNameGetter
instanceKlass sun/util/locale/provider/TimeZoneNameUtility
instanceKlass sun/nio/cs/Surrogate
instanceKlass sun/nio/cs/Surrogate$Parser
instanceKlass sun/nio/fs/WindowsPath$1
instanceKlass java/nio/file/Path$1
instanceKlass sun/nio/fs/WindowsSecurityDescriptor
instanceKlass  @bci org/eclipse/core/internal/preferences/SortedProperties <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/core/internal/preferences/SortedProperties$$Lambda+0x000002962f1c0fe0
instanceKlass  @bci org/eclipse/core/runtime/Plugin savePluginPreferences ()V 41 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/Plugin$$Lambda+0x000002962f1cc9d8
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/eclipse/jdt/ls/core/internal/Environment
instanceKlass org/eclipse/jdt/ls/core/internal/JDTEnvironmentUtils
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/LogHandler$1
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatSymbolsProvider ()Ljava/text/spi/DateFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006a
instanceKlass java/text/DateFormatSymbols
instanceKlass java/util/stream/Nodes$ArrayNode
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 16 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x80000006e
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 6 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x80000006d
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getCalendarDataProvider ()Ljava/util/spi/CalendarDataProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000067
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Calendar$Builder
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getCalendarProvider ()Lsun/util/spi/CalendarProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000068
instanceKlass java/util/Calendar
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatProvider ()Ljava/text/spi/DateFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000069
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/DefaultLogFilter
instanceKlass org/eclipse/core/runtime/ILogListener
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/LogHandler
instanceKlass org/eclipse/jdt/internal/core/manipulation/MembersOrderPreferenceCacheCommon
instanceKlass org/eclipse/jdt/core/manipulation/JavaManipulation
instanceKlass org/eclipse/text/templates/ContextTypeRegistry
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$21$1
instanceKlass  @bci org/eclipse/jdt/internal/core/search/processing/JobManager reset ()V 38 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/search/processing/JobManager$$Lambda+0x000002962f257b00
instanceKlass org/eclipse/jdt/internal/core/ExternalAnnotationTracker$DirectoryNode
instanceKlass org/eclipse/jdt/internal/core/ExternalAnnotationTracker
instanceKlass org/eclipse/jdt/core/IJavaModelStatusConstants
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f258000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f251400
instanceKlass org/eclipse/jdt/internal/compiler/CompilationResult
instanceKlass lombok/eclipse/agent/PatchDelegate$BindingTuple
instanceKlass org/eclipse/jdt/internal/compiler/ast/TypeOrLambda
instanceKlass org/eclipse/jdt/internal/compiler/ast/Invocation
instanceKlass org/eclipse/jdt/internal/compiler/ast/IPolyExpression
instanceKlass lombok/eclipse/agent/PatchDelegate
instanceKlass lombok/eclipse/agent/PatchDelegatePortal
instanceKlass lombok/core/LombokNode
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/PatchFixesHider$Util
instanceKlass lombok/launch/PatchFixesHider$Delegate
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessingState$RootInfos
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$20
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$19
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$14
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$13
instanceKlass org/eclipse/jdt/internal/formatter/DefaultCodeFormatterOptions
instanceKlass org/eclipse/jdt/core/formatter/DefaultCodeFormatterConstants
instanceKlass org/eclipse/jdt/internal/compiler/util/Util
instanceKlass org/eclipse/jdt/internal/compiler/impl/IrritantSet
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f251000
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f250c00
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/EclipsePreferences$$Lambda+0x000002962f1c0db8
instanceKlass  @cpi org/eclipse/core/internal/preferences/EclipsePreferences 1057 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f250800
instanceKlass org/eclipse/jdt/internal/core/util/ICacheEnumeration
instanceKlass org/eclipse/jdt/internal/formatter/TokenTraverser
instanceKlass org/eclipse/text/edits/TextEdit
instanceKlass org/eclipse/jdt/core/formatter/CodeFormatter
instanceKlass lombok/patcher/scripts/WrapperMethodDescriptor
instanceKlass lombok/patcher/scripts/SetSymbolDuringMethodCallScript$1
instanceKlass org/eclipse/jdt/core/SourceRange
instanceKlass org/eclipse/jdt/internal/compiler/util/JRTUtil$JrtFileVisitor
instanceKlass org/eclipse/jdt/core/IOrdinaryClassFile
instanceKlass org/eclipse/jdt/internal/core/util/ReferenceInfoAdapter
instanceKlass org/eclipse/jdt/internal/compiler/ISourceElementRequestor
instanceKlass org/eclipse/jdt/core/search/TypeNameMatchRequestor
instanceKlass org/eclipse/jdt/internal/compiler/env/ISourceType
instanceKlass org/eclipse/jdt/internal/compiler/env/IGenericType
instanceKlass org/eclipse/jdt/internal/compiler/problem/ProblemHandler
instanceKlass org/eclipse/jdt/core/search/MethodNameMatch
instanceKlass org/eclipse/jdt/core/search/IJavaSearchScope
instanceKlass org/eclipse/jdt/core/search/SearchParticipant
instanceKlass org/eclipse/jdt/internal/compiler/ASTVisitor
instanceKlass org/eclipse/jdt/core/search/TypeNameMatch
instanceKlass org/eclipse/jdt/internal/core/search/IndexQueryRequestor
instanceKlass org/eclipse/jdt/core/search/SearchPattern
instanceKlass org/eclipse/jdt/core/search/IParallelizable
instanceKlass org/eclipse/jdt/internal/core/search/BasicSearchEngine
instanceKlass org/eclipse/jdt/core/ITypeParameter
instanceKlass org/eclipse/jdt/core/ISourceRange
instanceKlass org/eclipse/jdt/core/IAnnotation
instanceKlass org/eclipse/jdt/internal/core/AbstractModule
instanceKlass org/eclipse/jdt/core/IModuleDescription
instanceKlass org/eclipse/jdt/internal/core/NameLookup
instanceKlass org/eclipse/jface/text/IDocument
instanceKlass org/eclipse/jdt/internal/core/JavaModelCache$1
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryInfo
instanceKlass org/eclipse/jdt/internal/core/JavaModelCache
instanceKlass org/eclipse/jdt/core/IType
instanceKlass org/eclipse/jdt/core/IAnnotatable
instanceKlass org/eclipse/jdt/core/IMember
instanceKlass org/eclipse/jdt/internal/core/hierarchy/HierarchyBuilder
instanceKlass org/eclipse/jdt/internal/core/hierarchy/TypeHierarchy
instanceKlass org/eclipse/jdt/core/ITypeHierarchy
instanceKlass org/eclipse/jdt/core/dom/ASTNode
instanceKlass org/eclipse/jdt/core/dom/StructuralPropertyDescriptor
instanceKlass org/eclipse/jdt/internal/core/dom/rewrite/RewriteEvent
instanceKlass org/eclipse/jdt/internal/core/dom/rewrite/RewriteEventStore
instanceKlass org/eclipse/jdt/core/dom/ASTVisitor
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor
instanceKlass org/eclipse/jdt/internal/codeassist/CompletionEngine$1
instanceKlass  @bci org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding <clinit> ()V 46 <appendix> argL0 ; # org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$$Lambda+0x000002962f22c820
instanceKlass  @cpi org/eclipse/jdt/internal/launching/StandardVMType 1152 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f230000
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ParameterNonNullDefaultProvider
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$3
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$2
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReductionResult
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ElementValuePair
instanceKlass org/eclipse/jdt/internal/compiler/lookup/AnnotationBinding
instanceKlass org/eclipse/jdt/internal/compiler/env/IUpdatableModule
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeBindingVisitor
instanceKlass org/eclipse/jdt/internal/compiler/lookup/HotSwappable
instanceKlass org/eclipse/jdt/internal/compiler/parser/ScannerHelper
instanceKlass org/eclipse/jdt/core/Signature
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeConstants$CloseMethodRecord
instanceKlass org/eclipse/jdt/internal/core/INamingRequestor
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Substitution
instanceKlass org/eclipse/jdt/internal/core/IJavaElementRequestor
instanceKlass org/eclipse/jdt/core/CompletionRequestor
instanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$1
instanceKlass org/eclipse/jdt/internal/codeassist/complete/CompletionNode
instanceKlass org/eclipse/jdt/core/search/SearchRequestor
instanceKlass org/eclipse/jdt/internal/codeassist/UnresolvedReferenceNameFinder$UnresolvedReferenceNameRequestor
instanceKlass org/eclipse/jdt/internal/codeassist/MissingTypesGuesser$GuessedTypeRequestor
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Binding
instanceKlass org/eclipse/jdt/core/CompletionProposal
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Scope
instanceKlass org/eclipse/jdt/core/compiler/IProblem
instanceKlass org/eclipse/jdt/core/CompletionContext
instanceKlass org/eclipse/jdt/internal/compiler/env/INameEnvironment
instanceKlass org/eclipse/jdt/internal/compiler/lookup/InvocationSite
instanceKlass org/eclipse/jdt/internal/compiler/ast/ASTNode
instanceKlass org/eclipse/jdt/internal/codeassist/impl/Engine
instanceKlass org/eclipse/jdt/internal/codeassist/ICompletionEngine
instanceKlass org/eclipse/jdt/internal/codeassist/RelevanceConstants
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeConstants
instanceKlass org/eclipse/jdt/internal/codeassist/ISearchRequestor
instanceKlass org/eclipse/jdt/internal/compiler/impl/ReferenceContext
instanceKlass org/eclipse/jdt/internal/compiler/ICompilerRequestor
instanceKlass org/eclipse/jdt/internal/compiler/Compiler
instanceKlass org/eclipse/jdt/internal/compiler/problem/ProblemSeverities
instanceKlass org/eclipse/jdt/internal/compiler/impl/ITypeRequestor
instanceKlass org/eclipse/jdt/internal/core/builder/ClasspathLocation
instanceKlass org/eclipse/jdt/core/IBuffer
instanceKlass org/eclipse/jdt/core/IBufferFactory
instanceKlass org/eclipse/jdt/internal/core/BufferManager
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$8
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager <clinit> ()V 139 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000002962f2078f8
instanceKlass  @bci org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry <init> (Ljava/io/File;Lorg/eclipse/core/runtime/IPath;)V 24 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry$$Lambda+0x000002962f2076e0
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry
instanceKlass org/eclipse/jdt/internal/compiler/util/SimpleLookupTable
instanceKlass org/eclipse/jdt/internal/core/index/IndexLocation
instanceKlass org/eclipse/jdt/internal/compiler/parser/Parser
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeIds
instanceKlass org/eclipse/jdt/internal/compiler/ast/OperatorIds
instanceKlass org/eclipse/jdt/internal/compiler/parser/ConflictedParser
instanceKlass org/eclipse/jdt/internal/compiler/parser/ParserBasicInformation
instanceKlass org/eclipse/jdt/internal/compiler/IProblemFactory
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IndexRequest
instanceKlass org/eclipse/jdt/internal/core/search/processing/JobManager
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IIndexConstants
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager <init> ()V 404 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000002962f201f30
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$3
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$2
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$EclipsePreferencesListener
instanceKlass org/eclipse/jdt/core/IElementChangedListener
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessingState
instanceKlass org/eclipse/core/internal/dtree/DataTreeLookup
instanceKlass org/eclipse/jdt/internal/core/ExternalFoldersManager
instanceKlass org/eclipse/jdt/internal/core/util/Util$Comparer
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$CompilationParticipants
instanceKlass org/eclipse/jdt/internal/core/BatchInitializationMonitor
instanceKlass org/eclipse/jdt/internal/core/JavaModelOperation
instanceKlass org/eclipse/jdt/internal/core/JavaElementInfo
instanceKlass org/eclipse/jdt/internal/codeassist/ISelectionRequestor
instanceKlass org/eclipse/jdt/core/IJavaModelStatus
instanceKlass org/eclipse/jdt/internal/compiler/env/IElementInfo
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$1
instanceKlass org/eclipse/jdt/core/IClassFile
instanceKlass org/eclipse/jdt/core/IPackageFragment
instanceKlass org/eclipse/jdt/internal/compiler/util/Util$Displayable
instanceKlass org/eclipse/jdt/internal/core/search/processing/IJob
instanceKlass org/eclipse/jdt/internal/core/util/LRUCache
instanceKlass org/eclipse/jdt/internal/core/search/IRestrictedAccessTypeRequestor
instanceKlass org/eclipse/jdt/core/IPackageFragmentRoot
instanceKlass org/eclipse/jdt/core/IJavaElementDelta
instanceKlass org/eclipse/jdt/core/IBufferChangedListener
instanceKlass org/eclipse/jdt/internal/compiler/util/SuffixConstants
instanceKlass org/eclipse/jdt/internal/compiler/env/ICompilationUnit
instanceKlass org/eclipse/jdt/internal/compiler/env/IDependent
instanceKlass org/eclipse/jdt/core/ICompilationUnit
instanceKlass org/eclipse/jdt/core/ISourceManipulation
instanceKlass org/eclipse/jdt/core/ITypeRoot
instanceKlass org/eclipse/jdt/core/ICodeAssist
instanceKlass org/eclipse/jdt/core/ISourceReference
instanceKlass org/lombokweb/asm/Handle
instanceKlass org/eclipse/jdt/core/IAccessRule
instanceKlass org/eclipse/jdt/core/IJavaProject
instanceKlass org/eclipse/jdt/core/IClasspathContainer
instanceKlass org/eclipse/core/internal/jobs/JobQueue$2
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager
instanceKlass  @bci java/util/Comparator comparingDouble (Ljava/util/function/ToDoubleFunction;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002962f1863d8
instanceKlass  @bci org/eclipse/jdt/core/JavaCore <clinit> ()V 200 <appendix> argL0 ; # org/eclipse/jdt/core/JavaCore$$Lambda+0x000002962f1f1cb0
instanceKlass  @cpi org/eclipse/jdt/core/JavaCore 2468 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1f3400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f1f3000
instanceKlass java/util/function/ToDoubleFunction
instanceKlass org/eclipse/jdt/core/compiler/CharOperation
instanceKlass org/eclipse/jdt/internal/compiler/impl/CompilerOptions
instanceKlass org/eclipse/jdt/core/IWorkingCopy
instanceKlass org/eclipse/jdt/core/IClasspathAttribute
instanceKlass org/eclipse/jdt/internal/compiler/env/IModule
instanceKlass org/eclipse/jdt/core/search/TypeNameRequestor
instanceKlass org/eclipse/jdt/core/IRegion
instanceKlass org/eclipse/jdt/core/IClasspathEntry
instanceKlass org/eclipse/jdt/core/IJavaModel
instanceKlass org/eclipse/jdt/core/IParent
instanceKlass org/eclipse/jdt/core/IOpenable
instanceKlass org/eclipse/jdt/core/IJavaElement
instanceKlass org/eclipse/core/resources/IWorkspaceRunnable
instanceKlass org/eclipse/jdt/core/WorkingCopyOwner
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ISourceDownloader
instanceKlass org/eclipse/jdt/ls/core/contentassist/ICompletionContributionService
instanceKlass java/net/Authenticator
instanceKlass java/nio/channels/AsynchronousByteChannel
instanceKlass java/nio/channels/AsynchronousChannel
instanceKlass java/net/SocketAddress
instanceKlass org/eclipse/jdt/ls/core/internal/lsp/JavaProtocolExtensions
instanceKlass org/eclipse/jdt/ls/core/internal/syntaxserver/IExtendedProtocol
instanceKlass org/eclipse/lsp4j/services/WorkspaceService
instanceKlass org/eclipse/lsp4j/services/TextDocumentService
instanceKlass org/eclipse/lsp4j/services/LanguageServer
instanceKlass org/eclipse/core/internal/events/NodeIDMap
instanceKlass org/eclipse/core/internal/events/ResourceDeltaInfo
instanceKlass org/eclipse/core/internal/dtree/NodeComparison
instanceKlass org/eclipse/core/internal/events/ResourceComparator
instanceKlass org/eclipse/jdt/ls/core/internal/BaseJDTLanguageServer
instanceKlass org/eclipse/core/internal/events/ResourceDeltaFactory
instanceKlass org/eclipse/core/resources/IMarkerDelta
instanceKlass org/eclipse/core/runtime/SubMonitor$RootInfo
instanceKlass org/eclipse/core/runtime/SubMonitor
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/PreferenceManager
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$Sync$HoldCounter
instanceKlass org/eclipse/core/resources/team/ResourceRuleFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f1e8000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f1e3c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f1e3800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f1e3400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f1e3000
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$KeyIterator
instanceKlass org/eclipse/core/resources/IResourceChangeEvent
instanceKlass java/util/concurrent/atomic/Striped64$1
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass  @bci org/apache/felix/scr/impl/manager/ComponentContextImpl createNewFieldHandlerMap ()Ljava/util/Map; 4 <appendix> argL0 ; # org/apache/felix/scr/impl/manager/ComponentContextImpl$$Lambda+0x000002962f13faf0
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getLog (Lorg/osgi/framework/Bundle;)Lorg/eclipse/core/runtime/ILog; 13 <appendix> member <vmtarget> ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x000002962f1cc7a0
instanceKlass org/eclipse/core/internal/runtime/Log
instanceKlass org/eclipse/core/internal/preferences/BundleStateScope
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$Resolved
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils$FieldSearchResult
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils$1
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils
instanceKlass org/eclipse/core/internal/resources/Rules
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$1
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$ReferenceMethodImpl
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$State
instanceKlass org/apache/felix/scr/impl/inject/InitReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler
instanceKlass org/apache/felix/scr/impl/inject/field/FieldMethods
instanceKlass org/eclipse/core/internal/resources/CheckMissingNaturesListener
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f1e2c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f1e2800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1e2400
instanceKlass org/eclipse/core/internal/resources/ResourceChangeListenerRegistrar
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000002962f165dc0
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1512 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1e2000
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f1e1c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f1e1800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1e1400
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000002962f165b88
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1468 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1e1000
instanceKlass  @bci org/eclipse/core/internal/resources/AliasManager$LocationMap <init> (Lorg/eclipse/core/internal/resources/AliasManager;)V 14 <appendix> argL0 ; # org/eclipse/core/internal/resources/AliasManager$LocationMap$$Lambda+0x000002962f1e5708
instanceKlass org/eclipse/core/filesystem/IFileStore
instanceKlass org/eclipse/core/internal/resources/AliasManager$LocationMap
instanceKlass org/eclipse/core/internal/resources/AliasManager
instanceKlass org/eclipse/core/resources/refresh/IRefreshMonitor
instanceKlass org/eclipse/core/internal/refresh/MonitorManager
instanceKlass org/eclipse/core/resources/IResourceDeltaVisitor
instanceKlass org/eclipse/core/resources/IPathVariableChangeListener
instanceKlass org/eclipse/core/internal/refresh/RefreshManager
instanceKlass org/eclipse/core/resources/refresh/IRefreshResult
instanceKlass  @bci org/eclipse/core/internal/resources/ContentDescriptionManager getCurrentPlatformState ()Ljava/lang/String; 39 <appendix> argL0 ; # org/eclipse/core/internal/resources/ContentDescriptionManager$$Lambda+0x000002962f1dbba8
instanceKlass org/eclipse/core/internal/resources/ProjectContentTypes
instanceKlass org/eclipse/core/internal/utils/Cache
instanceKlass org/eclipse/core/internal/resources/ContentDescriptionManager
instanceKlass  @bci org/eclipse/core/internal/resources/CharsetManager initPreferenceChangeListener ()V 2 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/CharsetManager$$Lambda+0x000002962f1dac78
instanceKlass  @bci org/eclipse/jdt/internal/compiler/parser/Parser initTables ()V 9 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f1e0400
instanceKlass  @bci org/eclipse/jdt/internal/compiler/parser/Parser initTables ()V 9 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f1e0000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f1d9c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f1d9800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f1d9400
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager now ()J 5 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000002962f165950
instanceKlass java/util/function/LongUnaryOperator
instanceKlass org/eclipse/core/internal/jobs/JobChangeEvent
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f1d9000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f1d8c00
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1d8800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000002962f1654c0
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1485 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1d8400
instanceKlass org/eclipse/core/internal/resources/CharsetDeltaJob$ICharsetListenerFilter
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 29 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002962f1dc000
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 19 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002962f11fcc0
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 9 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002962f11fa88
instanceKlass org/eclipse/core/runtime/PerformanceStats
instanceKlass org/eclipse/core/internal/events/ResourceStats
instanceKlass org/eclipse/core/internal/events/ResourceChangeListenerList$ListenerEntry
instanceKlass org/eclipse/core/internal/resources/CharsetManager$ResourceChangeListener
instanceKlass org/eclipse/core/resources/IResourceChangeListener
instanceKlass org/eclipse/core/internal/resources/CharsetManager
instanceKlass org/eclipse/core/internal/localstore/Bucket$Entry
instanceKlass org/eclipse/core/internal/localstore/BucketTree
instanceKlass org/eclipse/core/internal/localstore/Bucket$Visitor
instanceKlass org/eclipse/core/internal/localstore/Bucket
instanceKlass org/eclipse/core/internal/properties/PropertyManager2
instanceKlass org/eclipse/core/resources/IFileState
instanceKlass org/eclipse/core/internal/watson/ElementTree$ChildIDsCache
instanceKlass  @bci org/eclipse/core/internal/resources/SaveManager initSnap (Lorg/eclipse/core/runtime/IProgressMonitor;)V 67 <appendix> argL0 ; # org/eclipse/core/internal/resources/SaveManager$$Lambda+0x000002962f1d07e0
instanceKlass java/io/FilenameFilter
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 24 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1d8000
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager cancel (Lorg/eclipse/core/internal/jobs/InternalJob;)Z 15 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000002962f165288
instanceKlass org/eclipse/core/resources/ISaveContext
instanceKlass org/eclipse/core/resources/ISavedState
instanceKlass org/eclipse/core/internal/resources/SaveManager
instanceKlass org/eclipse/core/internal/watson/IElementInfoFlattener
instanceKlass org/eclipse/core/internal/resources/SyncInfoWriter
instanceKlass org/eclipse/core/internal/resources/Synchronizer
instanceKlass org/eclipse/core/internal/resources/MarkerWriter
instanceKlass org/eclipse/core/internal/resources/MarkerDeltaManager
instanceKlass org/eclipse/core/internal/resources/MarkerTypeDefinitionCache$MarkerTypeDefinition
instanceKlass org/eclipse/core/internal/resources/MarkerTypeDefinitionCache
instanceKlass org/eclipse/core/internal/resources/MarkerInfo
instanceKlass org/eclipse/core/internal/resources/IMarkerSetElement
instanceKlass org/eclipse/core/internal/resources/MarkerManager
instanceKlass  @bci org/eclipse/core/internal/events/NotificationManager$NotifyJob <init> (Lorg/eclipse/core/internal/events/NotificationManager;)V 13 <appendix> argL0 ; # org/eclipse/core/internal/events/NotificationManager$NotifyJob$$Lambda+0x000002962f1c8db0
instanceKlass org/eclipse/core/internal/events/ResourceChangeListenerList
instanceKlass org/eclipse/core/internal/events/NotificationManager
instanceKlass java/util/stream/SortedOps
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getBundles0 (Ljava/lang/String;Ljava/lang/String;)Ljava/util/stream/Stream; 90 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x000002962f1cc000
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getBundles0 (Ljava/lang/String;Ljava/lang/String;)Ljava/util/stream/Stream; 80 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x000002962f173cb8
instanceKlass org/eclipse/core/internal/events/BuildManager$DeltaCache
instanceKlass org/eclipse/core/resources/ICommand
instanceKlass org/eclipse/core/resources/IResourceDelta
instanceKlass org/eclipse/core/internal/events/InternalBuilder
instanceKlass org/eclipse/core/resources/IBuildContext
instanceKlass org/eclipse/core/internal/events/BuildManager
instanceKlass org/eclipse/core/internal/resources/FilterTypeManager$1
instanceKlass org/eclipse/core/internal/resources/FilterDescriptor
instanceKlass org/eclipse/core/resources/IFilterMatcherDescriptor
instanceKlass org/eclipse/core/internal/resources/FilterTypeManager
instanceKlass org/eclipse/core/resources/IProjectNatureDescriptor
instanceKlass org/eclipse/core/internal/resources/NatureManager
instanceKlass org/eclipse/core/internal/events/ILifecycleListener
instanceKlass org/eclipse/core/internal/resources/PathVariableManager
instanceKlass  @bci org/eclipse/core/internal/resources/WorkspaceRoot getProject (Ljava/lang/String;)Lorg/eclipse/core/resources/IProject; 95 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/WorkspaceRoot$$Lambda+0x000002962f1c66b8
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass java/text/FieldPosition$Delegate
instanceKlass java/text/Format$FieldDelegate
instanceKlass java/text/DigitList
instanceKlass java/lang/StringUTF16$CharsSpliterator
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x800000049
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004b
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004a
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004c
instanceKlass java/util/OptionalInt
instanceKlass  @bci java/text/DecimalFormatSymbols findNonFormatChar (Ljava/lang/String;C)C 4 <appendix> argL0 ; # java/text/DecimalFormatSymbols$$Lambda+0x80000000d
instanceKlass java/util/function/IntPredicate
instanceKlass java/lang/StringLatin1$CharsSpliterator
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDecimalFormatSymbolsProvider ()Ljava/text/spi/DecimalFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006b
instanceKlass java/text/DecimalFormatSymbols
instanceKlass java/lang/Class$1
instanceKlass sun/util/resources/Bundles$2
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x800000012
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter applyAliases (Ljava/util/Locale;)Ljava/util/Locale; 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000064
instanceKlass sun/util/resources/LocaleData$LocaleDataResourceBundleProvider
instanceKlass java/util/spi/ResourceBundleProvider
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getNumberFormatProvider ()Ljava/text/spi/NumberFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006c
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter <init> ()V 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000066
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass java/text/FieldPosition
instanceKlass java/text/Format
instanceKlass  @bci org/eclipse/core/internal/localstore/FileSystemResourceManager <init> (Lorg/eclipse/core/internal/resources/Workspace;)V 6 <appendix> member <vmtarget> ; # org/eclipse/core/internal/localstore/FileSystemResourceManager$$Lambda+0x000002962f1c52e8
instanceKlass  @bci org/eclipse/jdt/core/dom/ASTParser internalCreateAST (Lorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/jdt/core/dom/ASTNode; 2 <bsm> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f1c2000
instanceKlass org/eclipse/core/internal/localstore/RefreshLocalVisitor
instanceKlass org/eclipse/core/internal/localstore/ILocalStoreConstants
instanceKlass org/eclipse/core/internal/localstore/IHistoryStore
instanceKlass org/eclipse/core/internal/localstore/IUnifiedTreeVisitor
instanceKlass org/eclipse/core/internal/localstore/FileSystemResourceManager
instanceKlass org/eclipse/core/runtime/jobs/MultiRule
instanceKlass org/eclipse/core/internal/jobs/OrderedLock
instanceKlass java/lang/invoke/DirectMethodHandle$1
instanceKlass org/eclipse/core/internal/runtime/LocalizationUtils
instanceKlass org/eclipse/core/runtime/Status
instanceKlass org/eclipse/core/internal/resources/WorkManager$NotifyRule
instanceKlass org/eclipse/core/internal/resources/WorkManager
instanceKlass org/eclipse/core/runtime/NullProgressMonitor
instanceKlass org/eclipse/core/runtime/preferences/IExportedPreferences
instanceKlass org/eclipse/core/runtime/Preferences$1
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences firePreferenceEvent (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V 41 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/EclipsePreferences$$Lambda+0x000002962f1c0000
instanceKlass sun/management/Util
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002962f180430
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002962f180210
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000002962f180000
instanceKlass  @bci sun/management/spi/PlatformMBeanProvider$PlatformComponent getMBeans (Ljava/lang/Class;)Ljava/util/List; 63 <appendix> member <vmtarget> ; # sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda+0x000002962f17fca8
instanceKlass  @bci sun/management/spi/PlatformMBeanProvider$PlatformComponent getMBeans (Ljava/lang/Class;)Ljava/util/List; 47 <appendix> member <vmtarget> ; # sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda+0x000002962f17fa60
instanceKlass java/util/Collections$2
instanceKlass sun/management/RuntimeImpl
instanceKlass  @bci java/lang/management/ManagementFactory$PlatformMBeanFinder findSingleton (Ljava/lang/Class;)Lsun/management/spi/PlatformMBeanProvider$PlatformComponent; 30 <appendix> member <vmtarget> ; # java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda+0x000002962f17f248
instanceKlass  @bci java/lang/management/ManagementFactory$PlatformMBeanFinder findSingleton (Ljava/lang/Class;)Lsun/management/spi/PlatformMBeanProvider$PlatformComponent; 19 <appendix> member <vmtarget> ; # java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda+0x000002962f17f000
instanceKlass jdk/management/jfr/internal/FlightRecorderMXBeanProvider$SingleMBeanComponent
instanceKlass jdk/management/jfr/FlightRecorderMXBean
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$11
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$10
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$9
instanceKlass java/util/logging/LogManager
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess$1
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$8
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$7
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$6
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$5
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$4
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$3
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$2
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$1
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$5
instanceKlass sun/management/VMManagementImpl
instanceKlass sun/management/VMManagement
instanceKlass sun/management/ManagementFactoryHelper
instanceKlass sun/management/NotificationEmitterSupport
instanceKlass javax/management/NotificationEmitter
instanceKlass javax/management/NotificationBroadcaster
instanceKlass com/sun/management/DiagnosticCommandMBean
instanceKlass javax/management/DynamicMBean
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$4
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$3
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$2
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$1
instanceKlass sun/management/spi/PlatformMBeanProvider$PlatformComponent
instanceKlass  @bci com/sun/management/internal/PlatformMBeanProviderImpl <clinit> ()V 8 <appendix> argL0 ; # com/sun/management/internal/PlatformMBeanProviderImpl$$Lambda+0x000002962f0fafb8
instanceKlass sun/management/spi/PlatformMBeanProvider
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$1
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder
instanceKlass java/lang/management/RuntimeMXBean
instanceKlass java/lang/management/PlatformManagedObject
instanceKlass  @bci java/lang/management/ManagementFactory loadNativeLib ()V 0 <appendix> argL0 ; # java/lang/management/ManagementFactory$$Lambda+0x000002962f0f9dd8
instanceKlass java/lang/management/ManagementFactory
instanceKlass org/eclipse/core/internal/preferences/PreferencesService$1
instanceKlass  @bci org/eclipse/core/internal/preferences/PreferencesService getLookupOrder (Ljava/lang/String;Ljava/lang/String;)[Ljava/lang/String; 29 <appendix> argL0 ; # org/eclipse/core/internal/preferences/PreferencesService$$Lambda+0x000002962f1478d0
instanceKlass  @bci org/eclipse/core/internal/resources/WorkspacePreferences <init> ()V 189 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/WorkspacePreferences$$Lambda+0x000002962f177530
instanceKlass org/eclipse/core/runtime/Preferences$IPropertyChangeListener
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences$INodeChangeListener
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences$IPreferenceChangeListener
instanceKlass  @bci org/eclipse/core/runtime/Plugin getPluginPreferences ()Lorg/eclipse/core/runtime/Preferences; 65 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/Plugin$$Lambda+0x000002962f1730f0
instanceKlass org/eclipse/core/runtime/Preferences
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f17e800
instanceKlass lombok/eclipse/agent/PatchFixesShadowLoaded
instanceKlass lombok/core/LombokNode
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/PatchFixesHider$Util
instanceKlass lombok/launch/PatchFixesHider$LombokDeps
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 41 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f17d400
instanceKlass lombok/launch/PackageShader
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f17d000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f17cc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f17c800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f17c400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f17c000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f17a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f17a000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f179c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f179400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f179000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f178c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f178800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f178400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f178000
instanceKlass jdk/internal/vm/annotation/ForceInline
instanceKlass java/lang/constant/ClassDesc
instanceKlass java/io/ObjectOutput
instanceKlass java/io/ObjectStreamConstants
instanceKlass java/io/ObjectInput
instanceKlass java/lang/foreign/ValueLayout
instanceKlass java/lang/foreign/MemoryLayout
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f171c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f171800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f171400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f171000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f170c00
instanceKlass org/eclipse/core/internal/runtime/Product
instanceKlass org/eclipse/core/runtime/IProduct
instanceKlass lombok/patcher/scripts/WrapReturnValuesScript$1
instanceKlass org/eclipse/equinox/internal/app/ProductExtensionBranding
instanceKlass org/eclipse/core/internal/runtime/FindSupport
instanceKlass org/eclipse/core/runtime/FileLocator
instanceKlass  @bci org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl getBundles (Ljava/lang/String;Ljava/lang/String;)[Lorg/osgi/framework/Bundle; 281 <appendix> argL0 ; # org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl$$Lambda+0x000002962f11f858
instanceKlass org/eclipse/core/runtime/SafeRunner
instanceKlass  @bci org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper runInitializer (Lorg/eclipse/core/runtime/IConfigurationElement;)V 18 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper$$Lambda+0x000002962f1470c0
instanceKlass org/eclipse/core/runtime/IExecutableExtensionFactory
instanceKlass org/eclipse/core/runtime/IExecutableExtension
instanceKlass org/eclipse/core/runtime/preferences/AbstractPreferenceInitializer
instanceKlass org/eclipse/core/internal/dtree/AbstractDataTree
instanceKlass org/eclipse/core/internal/dtree/AbstractDataTreeNode
instanceKlass org/eclipse/core/internal/watson/ElementTree
instanceKlass org/eclipse/core/internal/runtime/DataArea
instanceKlass org/eclipse/core/internal/runtime/MetaDataKeeper
instanceKlass org/eclipse/core/internal/resources/LocalMetaArea
instanceKlass org/eclipse/core/internal/resources/LocationValidator
instanceKlass org/eclipse/core/runtime/Platform$OS
instanceKlass org/eclipse/core/internal/utils/FileUtil
instanceKlass org/eclipse/core/internal/watson/IElementContentVisitor
instanceKlass org/eclipse/core/resources/IResourceFilterDescription
instanceKlass org/eclipse/core/resources/team/IResourceTree
instanceKlass org/eclipse/core/resources/IMarker
instanceKlass org/eclipse/core/resources/IResourceProxy
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 29 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f170800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f170400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f170000
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass java/util/ResourceBundle
instanceKlass  @bci org/eclipse/osgi/util/NLS <clinit> ()V 7 <appendix> argL0 ; # org/eclipse/osgi/util/NLS$$Lambda+0x000002962f11f1f0
instanceKlass org/eclipse/osgi/util/NLS
instanceKlass org/eclipse/core/runtime/Platform
instanceKlass org/eclipse/core/resources/team/FileModificationValidationContext
instanceKlass org/eclipse/core/resources/team/IMoveDeleteHook
instanceKlass org/eclipse/core/internal/resources/ModelObject
instanceKlass org/eclipse/core/resources/IProject
instanceKlass org/eclipse/core/resources/IPathVariableManager
instanceKlass org/eclipse/core/resources/IProjectDescription
instanceKlass org/eclipse/core/internal/resources/InternalTeamHook
instanceKlass org/eclipse/core/internal/watson/IElementComparator
instanceKlass org/eclipse/core/internal/dtree/IComparator
instanceKlass org/eclipse/core/resources/ISynchronizer
instanceKlass org/eclipse/core/resources/IResourceRuleFactory
instanceKlass org/eclipse/core/resources/IBuildConfiguration
instanceKlass org/eclipse/core/internal/properties/IPropertyManager
instanceKlass org/eclipse/core/internal/resources/IManager
instanceKlass org/eclipse/core/resources/IWorkspaceDescription
instanceKlass org/eclipse/core/resources/IFile
instanceKlass org/eclipse/core/resources/IEncodedStorage
instanceKlass org/eclipse/core/resources/IStorage
instanceKlass org/eclipse/core/resources/IFolder
instanceKlass org/eclipse/core/internal/watson/IPathRequestor
instanceKlass org/eclipse/core/internal/resources/ResourceInfo
instanceKlass org/eclipse/core/internal/utils/IStringPoolParticipant
instanceKlass org/eclipse/core/runtime/ICoreRunnable
instanceKlass org/eclipse/core/internal/watson/IElementTreeData
instanceKlass org/eclipse/core/internal/jobs/WorkerPool
instanceKlass org/eclipse/core/internal/jobs/JobQueue
instanceKlass org/eclipse/core/internal/jobs/DeadlockDetector
instanceKlass org/eclipse/core/internal/jobs/LockManager
instanceKlass org/eclipse/core/runtime/jobs/JobChangeAdapter
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager <init> ()V 52 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000002962f162c60
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 50 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002962f162a50
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 41 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002962f162840
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 32 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002962f162630
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 23 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002962f162420
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 14 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002962f162210
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 5 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x000002962f162000
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobListeners 385 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f161000
instanceKlass org/eclipse/core/internal/jobs/JobListeners$IListenerDoit
instanceKlass org/eclipse/core/runtime/jobs/IJobChangeEvent
instanceKlass org/eclipse/core/internal/jobs/JobListeners
instanceKlass org/eclipse/core/internal/jobs/ImplicitJobs
instanceKlass org/eclipse/core/internal/jobs/JobManager$1
instanceKlass org/eclipse/core/runtime/ProgressMonitorWrapper
instanceKlass org/eclipse/core/runtime/IProgressMonitorWithBlocking
instanceKlass org/eclipse/core/internal/jobs/InternalJobGroup
instanceKlass org/eclipse/core/runtime/jobs/ILock
instanceKlass org/eclipse/core/runtime/jobs/IJobChangeListener
instanceKlass org/eclipse/core/internal/jobs/JobManager
instanceKlass org/eclipse/core/runtime/jobs/IJobManager
instanceKlass org/eclipse/core/internal/jobs/JobOSGiUtils
instanceKlass org/eclipse/core/internal/jobs/JobActivator
instanceKlass org/eclipse/core/resources/IWorkspaceRoot
instanceKlass org/eclipse/core/resources/IContainer
instanceKlass org/eclipse/core/resources/IResource
instanceKlass org/eclipse/core/runtime/jobs/ISchedulingRule
instanceKlass org/eclipse/core/runtime/PlatformObject
instanceKlass org/eclipse/core/internal/resources/ICoreConstants
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000033
instanceKlass  @bci java/util/regex/Pattern Range (II)Ljava/util/regex/Pattern$CharPredicate; 23 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002b
instanceKlass java/util/Formatter
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1075 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x800000010
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1067 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x80000000f
instanceKlass java/time/Period
instanceKlass java/time/chrono/ChronoPeriod
instanceKlass java/time/format/DateTimeFormatterBuilder$TextPrinterParser
instanceKlass java/time/format/DateTimeTextProvider$1
instanceKlass java/time/format/DateTimeTextProvider
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass java/time/format/DateTimeTextProvider$LocaleStore
instanceKlass java/time/format/DateTimeFormatterBuilder$InstantPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$StringLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$ZoneIdPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$OffsetIdPrinterParser
instanceKlass java/time/format/DecimalStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$CompositePrinterParser
instanceKlass java/time/chrono/AbstractChronology
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/format/DateTimeFormatterBuilder$CharLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$NumberPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$DateTimePrinterParser
instanceKlass java/time/temporal/JulianFields
instanceKlass java/time/temporal/IsoFields
instanceKlass  @bci java/time/format/DateTimeFormatterBuilder <clinit> ()V 0 <appendix> argL0 ; # java/time/format/DateTimeFormatterBuilder$$Lambda+0x800000011
instanceKlass java/time/temporal/TemporalQuery
instanceKlass java/time/format/DateTimeFormatterBuilder
instanceKlass java/time/format/DateTimeFormatter
instanceKlass org/eclipse/osgi/internal/debug/EclipseDebugTrace
instanceKlass org/eclipse/core/internal/utils/Policy$1
instanceKlass org/eclipse/core/runtime/IProgressMonitor
instanceKlass org/eclipse/core/internal/utils/Policy
instanceKlass org/eclipse/core/resources/ResourcesPlugin$WorkspaceInitCustomizer
instanceKlass org/eclipse/core/resources/IWorkspace
instanceKlass org/eclipse/core/runtime/IAdaptable
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ProjectsManager
instanceKlass org/eclipse/jdt/ls/core/internal/managers/IProjectsManager
instanceKlass org/eclipse/core/resources/ISaveParticipant
instanceKlass org/eclipse/core/internal/runtime/LogServiceFactory
instanceKlass org/eclipse/equinox/internal/app/AppCommands
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f15a400
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f15a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f159c00
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form names 6 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f159800
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f159400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f159000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f158c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f158800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f158400
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form names 10 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f158000
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f157c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f157800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f157400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f157000
instanceKlass org/eclipse/equinox/internal/app/EclipseAppContainer$RegisterService
instanceKlass org/eclipse/core/runtime/spi/RegistryContributor
instanceKlass org/eclipse/equinox/app/IApplicationContext
instanceKlass org/osgi/service/application/ApplicationHandle
instanceKlass org/osgi/service/application/ApplicationDescriptor
instanceKlass org/eclipse/osgi/service/runnable/ApplicationLauncher
instanceKlass org/eclipse/osgi/service/runnable/ApplicationRunnable
instanceKlass org/eclipse/equinox/internal/app/IBranding
instanceKlass org/eclipse/osgi/service/runnable/ParameterizedRunnable
instanceKlass org/eclipse/equinox/internal/app/EclipseAppContainer
instanceKlass org/osgi/service/application/ScheduledApplication
instanceKlass org/eclipse/equinox/internal/app/AppPersistence
instanceKlass org/eclipse/equinox/internal/app/Activator
instanceKlass org/eclipse/equinox/internal/app/CommandLineArgs
instanceKlass org/eclipse/core/internal/preferences/legacy/InitLegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/legacy/ProductPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/IProductPreferencesService
instanceKlass org/eclipse/core/internal/runtime/AuthorizationHandler
instanceKlass org/eclipse/core/runtime/IBundleGroupProvider
instanceKlass java/util/Collections$ReverseComparator2
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform <clinit> ()V 98 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x000002962f153000
instanceKlass org/eclipse/core/runtime/ILog
instanceKlass org/eclipse/core/internal/runtime/InternalPlatform
instanceKlass org/eclipse/core/runtime/Plugin
instanceKlass org/eclipse/osgi/internal/loader/buddy/PolicyHandler
instanceKlass org/osgi/util/promise/DeferredPromiseImpl$ResolveWith
instanceKlass  @bci org/osgi/util/promise/PromiseFactory$All run ()V 81 <appendix> member <vmtarget> ; # org/osgi/util/promise/PromiseFactory$All$$Lambda+0x000002962f1501f0
instanceKlass  @cpi org/osgi/util/promise/PromiseFactory$All 144 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f152000
instanceKlass org/osgi/util/promise/PromiseImpl$Result
instanceKlass org/osgi/util/promise/PromiseFactory$All
instanceKlass org/osgi/util/promise/PromiseImpl$InlineCallback
instanceKlass org/eclipse/core/runtime/QualifiedName
instanceKlass org/apache/felix/scr/impl/inject/methods/ActivateMethod$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f14b800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f14b400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f14b000
instanceKlass org/apache/felix/scr/impl/inject/MethodResult
instanceKlass org/eclipse/core/internal/content/ContentTypeManager$ContentTypeRegistryChangeListener
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$OpenStatusImpl
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod$1
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$Resolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$MethodInfo
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$1
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 22 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000044
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 17 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000042
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 12 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003f
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 7 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x800000047
instanceKlass  @bci java/lang/Class methodToString (Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/String; 42 <appendix> argL0 ; # java/lang/Class$$Lambda+0x000002962f0f39d0
instanceKlass org/eclipse/core/internal/content/BasicDescription
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$ISelectionPolicy
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$IContentTypeChangeListener
instanceKlass org/eclipse/core/internal/preferences/BundleStateScopeServiceFactory
instanceKlass org/eclipse/core/internal/preferences/Activator$1
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/Messages buildVariants (Ljava/lang/String;)[Ljava/lang/String; 24 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f14ac00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f14a800
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/Messages buildVariants (Ljava/lang/String;)[Ljava/lang/String; 24 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f14a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f14a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f149c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f149800
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry$ListenerInfo
instanceKlass org/eclipse/core/runtime/IContributor
instanceKlass org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper
instanceKlass  @bci org/eclipse/debug/core/model/RuntimeProcess getPidInfo (Ljava/lang/Process;Lorg/eclipse/debug/core/ILaunch;)Ljava/lang/String; 41 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f149400
instanceKlass  @bci org/eclipse/debug/core/model/RuntimeProcess getPidInfo (Ljava/lang/Process;Lorg/eclipse/debug/core/ILaunch;)Ljava/lang/String; 41 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f149000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f148c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f148800
instanceKlass  @bci org/eclipse/debug/core/model/RuntimeProcess getPidInfo (Ljava/lang/Process;Lorg/eclipse/debug/core/ILaunch;)Ljava/lang/String; 41 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f148400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f148000
instanceKlass org/eclipse/core/runtime/ListenerList$ListenerListIterator
instanceKlass org/eclipse/core/internal/preferences/OSGiPreferencesServiceManager
instanceKlass org/osgi/service/prefs/PreferencesService
instanceKlass org/eclipse/core/internal/preferences/AbstractScope
instanceKlass org/eclipse/core/runtime/Assert
instanceKlass org/eclipse/core/runtime/Path
instanceKlass org/eclipse/core/runtime/Path$Constants
instanceKlass org/eclipse/core/runtime/IPath
instanceKlass org/eclipse/core/internal/preferences/ImmutableMap
instanceKlass org/eclipse/core/internal/preferences/EclipsePreferences
instanceKlass org/eclipse/core/runtime/preferences/IScope
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences
instanceKlass org/eclipse/core/internal/preferences/PreferencesService
instanceKlass org/eclipse/core/runtime/preferences/IPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/ILegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/PreferencesOSGiUtils
instanceKlass org/eclipse/core/internal/preferences/Activator
instanceKlass org/eclipse/core/runtime/preferences/IScopeContext
instanceKlass org/eclipse/core/internal/content/ContentTypeBuilder
instanceKlass org/eclipse/core/internal/content/ContentTypeCatalog
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils
instanceKlass org/eclipse/core/internal/content/ILazySource
instanceKlass org/eclipse/core/internal/adapter/AdapterManagerListener
instanceKlass org/eclipse/core/internal/runtime/IAdapterManagerProvider
instanceKlass org/eclipse/core/runtime/IRegistryEventListener
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryCommandProvider
instanceKlass org/eclipse/osgi/framework/console/CommandProvider
instanceKlass org/eclipse/core/internal/registry/RegistryProviderFactory
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryProviderOSGI
instanceKlass org/eclipse/core/internal/registry/osgi/EclipseBundleListener
instanceKlass org/eclipse/core/internal/registry/OffsetTable
instanceKlass org/eclipse/osgi/compatibility/state/ReadOnlyState
instanceKlass org/eclipse/core/internal/registry/HashtableOfStringAndInt
instanceKlass org/eclipse/core/internal/registry/KeyedHashSet
instanceKlass org/eclipse/core/runtime/IConfigurationElement
instanceKlass org/eclipse/core/internal/registry/Handle
instanceKlass org/eclipse/core/internal/registry/RegistryObjectManager
instanceKlass org/eclipse/core/internal/registry/IObjectManager
instanceKlass  @bci org/eclipse/core/internal/registry/RegistryProperties getContextProperty (Ljava/lang/String;)Ljava/lang/String; 18 <appendix> member <vmtarget> ; # org/eclipse/core/internal/registry/RegistryProperties$$Lambda+0x000002962f1331d8
instanceKlass org/eclipse/core/internal/registry/RegistryTimestamp
instanceKlass org/eclipse/core/internal/registry/TableReader
instanceKlass org/eclipse/core/runtime/ListenerList
instanceKlass org/eclipse/core/internal/registry/ReadWriteMonitor
instanceKlass org/eclipse/core/internal/registry/RegistryObjectFactory
instanceKlass org/eclipse/core/runtime/IExtensionPoint
instanceKlass org/eclipse/core/runtime/IExtensionDelta
instanceKlass org/eclipse/core/runtime/ISafeRunnable
instanceKlass org/eclipse/core/runtime/IExtension
instanceKlass org/eclipse/core/internal/registry/RegistryObject
instanceKlass org/eclipse/core/internal/registry/KeyedElement
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry
instanceKlass org/eclipse/core/runtime/spi/IDynamicExtensionRegistry
instanceKlass org/eclipse/core/runtime/IExtensionRegistry
instanceKlass org/eclipse/core/runtime/RegistryFactory
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$IEntry
instanceKlass org/eclipse/core/internal/registry/ReferenceMap
instanceKlass org/eclipse/core/internal/registry/osgi/OSGIUtils
instanceKlass org/eclipse/core/internal/registry/osgi/EquinoxUtils
instanceKlass org/eclipse/core/internal/registry/RegistryProperties
instanceKlass org/eclipse/core/runtime/spi/IRegistryProvider
instanceKlass org/eclipse/core/runtime/spi/RegistryStrategy
instanceKlass org/eclipse/core/internal/registry/osgi/Activator
instanceKlass org/eclipse/core/runtime/IRegistryChangeListener
instanceKlass org/eclipse/core/internal/content/IContentTypeInfo
instanceKlass org/eclipse/core/runtime/content/IContentDescription
instanceKlass org/eclipse/core/runtime/content/IContentType
instanceKlass org/eclipse/core/runtime/content/IContentTypeSettings
instanceKlass org/osgi/service/prefs/Preferences
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentConstructorImpl
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/methods/BindMethods
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotApplicable
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$State
instanceKlass org/apache/felix/scr/impl/inject/BaseParameter
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod
instanceKlass org/eclipse/core/internal/content/ContentTypeMatcher
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager
instanceKlass org/eclipse/core/runtime/content/IContentTypeMatcher
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$1
instanceKlass org/apache/felix/scr/impl/helper/ComponentServiceObjectsHelper
instanceKlass org/apache/felix/scr/impl/manager/EdgeInfo
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl$ComponentInstanceImpl
instanceKlass org/osgi/service/component/ComponentInstance
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegStateWrapper
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator$ListenerInfo
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker$AbstractTracked
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListener
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker
instanceKlass java/util/Collections$ReverseComparator
instanceKlass org/apache/felix/scr/impl/helper/Coercions
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$AbstractCustomizer
instanceKlass org/apache/felix/scr/impl/inject/RefPair
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$Customizer
instanceKlass org/apache/felix/scr/impl/manager/ServiceTrackerCustomizer
instanceKlass org/apache/felix/scr/impl/inject/OpenStatus
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager
instanceKlass org/apache/felix/scr/impl/manager/ReferenceManager
instanceKlass org/osgi/util/promise/Deferred
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$SetImplementationObject
instanceKlass org/apache/felix/scr/impl/inject/ScrComponentContext
instanceKlass org/apache/felix/scr/component/ExtComponentContext
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker$1
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker
instanceKlass java/util/Timer$ThreadReaper
instanceKlass java/util/TaskQueue
instanceKlass java/util/Timer
instanceKlass org/apache/felix/scr/impl/inject/ComponentConstructor
instanceKlass org/apache/felix/scr/impl/inject/LifecycleMethod
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentMethodsImpl
instanceKlass org/apache/felix/scr/impl/metadata/TargetedPID
instanceKlass java/util/concurrent/CompletionStage
instanceKlass org/osgi/util/promise/PromiseImpl
instanceKlass org/osgi/util/promise/Promise
instanceKlass org/osgi/util/promise/PromiseFactory
instanceKlass org/osgi/util/promise/Promises
instanceKlass org/apache/felix/scr/impl/inject/ComponentMethods
instanceKlass org/osgi/service/component/ComponentFactory
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ConfigurableComponentHolder
instanceKlass org/apache/felix/scr/impl/manager/ComponentContainer
instanceKlass org/apache/felix/scr/impl/ComponentRegistryKey
instanceKlass org/apache/felix/scr/impl/metadata/PropertyMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ComponentMetadata
instanceKlass org/apache/felix/scr/impl/xml/XmlConstants
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants$ArrayEnumeration
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$LocatorProxy
instanceKlass org/xml/sax/ext/Locator2
instanceKlass org/xml/sax/Locator
instanceKlass com/sun/org/apache/xerces/internal/util/XMLSymbols
instanceKlass com/sun/org/apache/xerces/internal/util/XMLChar
instanceKlass com/sun/xml/internal/stream/Entity
instanceKlass com/sun/xml/internal/stream/util/BufferAllocator
instanceKlass com/sun/xml/internal/stream/util/ThreadLocalBufferAllocator
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$EncodingInfo
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLLimitAnalyzer
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLInputSource
instanceKlass com/sun/org/apache/xerces/internal/util/ErrorHandlerWrapper
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLErrorHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/ExternalSubsetResolver
instanceKlass com/sun/org/apache/xerces/internal/util/EntityResolverWrapper
instanceKlass org/xml/sax/ext/EntityResolver2
instanceKlass org/xml/sax/InputSource
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$AttributesProxy
instanceKlass org/xml/sax/ext/Attributes2
instanceKlass org/xml/sax/Attributes
instanceKlass org/xml/sax/AttributeList
instanceKlass com/sun/org/apache/xerces/internal/util/FeatureState
instanceKlass com/sun/org/apache/xerces/internal/util/PropertyState
instanceKlass com/sun/org/apache/xerces/internal/impl/msg/XMLMessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/util/MessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLVersionDetector
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationManager
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NMTOKENDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NOTATIONDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ENTITYDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ListDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDREFDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/StringDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DTDDVFactory
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/DTDGrammarBucket
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLAttributeDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLSimpleType
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLElementDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationState
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/ValidationContext
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/RevalidationHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidatorFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentFilter
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLEntityDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDProcessor
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelSource
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDSource
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLDTDDescription
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLGrammarDescription
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$TrailingMiscDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$PrologDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$XMLDeclDriver
instanceKlass com/sun/org/apache/xerces/internal/util/NamespaceSupport
instanceKlass com/sun/org/apache/xerces/internal/xni/NamespaceContext
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl$Attribute
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLAttributes
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$FragmentContentDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$Driver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack2
instanceKlass com/sun/org/apache/xerces/internal/xni/QName
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLString
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner
instanceKlass com/sun/xml/internal/stream/XMLBufferListener
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentSource
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLErrorReporter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLLocator
instanceKlass com/sun/xml/internal/stream/XMLEntityStorage
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl$AugmentationsItemsContainer
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/Augmentations
instanceKlass com/sun/org/apache/xerces/internal/util/XMLResourceIdentifierImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLResourceIdentifier
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLEntityResolver
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponent
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable$Entry
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable
instanceKlass jdk/xml/internal/JdkConstants
instanceKlass jdk/xml/internal/JdkXmlUtils
instanceKlass com/sun/org/apache/xerces/internal/util/ParserConfigurationSettings
instanceKlass com/sun/org/apache/xerces/internal/parsers/XML11Configurable
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLPullParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager
instanceKlass com/sun/org/apache/xerces/internal/parsers/XMLParser
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDContentModelHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDocumentHandler
instanceKlass org/xml/sax/XMLReader
instanceKlass org/xml/sax/Parser
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager
instanceKlass javax/xml/parsers/SAXParser
instanceKlass com/sun/org/apache/xerces/internal/xs/PSVIProvider
instanceKlass com/sun/org/apache/xerces/internal/jaxp/JAXPConstants
instanceKlass  @bci javax/xml/parsers/FactoryFinder newInstance (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/ClassLoader;ZZ)Ljava/lang/Object; 104 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x000002962f0d9818
instanceKlass  @bci jdk/xml/internal/SecuritySupport getContextClassLoader ()Ljava/lang/ClassLoader; 0 <appendix> argL0 ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002962f0d93b0
instanceKlass  @bci javax/xml/parsers/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 104 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x000002962f0d9198
instanceKlass javax/xml/parsers/FactoryFinder$1
instanceKlass  @bci jdk/xml/internal/SecuritySupport getFileInputStream (Ljava/io/File;)Ljava/io/FileInputStream; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002962f0d8d60
instanceKlass  @bci jdk/xml/internal/SecuritySupport doesFileExist (Ljava/io/File;)Z 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002962f0d8b48
instanceKlass  @bci javax/xml/parsers/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 6 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x000002962f0d8930
instanceKlass  @bci jdk/xml/internal/SecuritySupport getSystemProperty (Ljava/lang/String;)Ljava/lang/String; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002962f0d8718
instanceKlass jdk/xml/internal/SecuritySupport
instanceKlass javax/xml/parsers/FactoryFinder
instanceKlass javax/xml/parsers/SAXParserFactory
instanceKlass  @bci org/eclipse/osgi/storage/Storage lambda$7 (Ljava/util/List;Ljava/lang/String;)Ljava/util/stream/Stream; 7 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000002962f11cd38
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$1
instanceKlass  @bci org/eclipse/osgi/storage/Storage findEntries (Ljava/util/List;Ljava/lang/String;Ljava/lang/String;I)Ljava/util/Enumeration; 101 <appendix> argL0 ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000002962f11c8b8
instanceKlass  @bci org/eclipse/osgi/storage/Storage findEntries (Ljava/util/List;Ljava/lang/String;Ljava/lang/String;I)Ljava/util/Enumeration; 91 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000002962f10bd88
instanceKlass org/xml/sax/helpers/DefaultHandler
instanceKlass org/xml/sax/ErrorHandler
instanceKlass org/xml/sax/ContentHandler
instanceKlass org/xml/sax/DTDHandler
instanceKlass org/xml/sax/EntityResolver
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListenerContext
instanceKlass java/util/AbstractList$Itr
instanceKlass org/eclipse/core/internal/runtime/IAdapterFactoryExt
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge$LazyAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge
instanceKlass org/eclipse/core/runtime/IAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/TracingOptions$1
instanceKlass org/eclipse/core/internal/runtime/TracingOptions
instanceKlass java/util/AbstractMap$1$1
instanceKlass org/osgi/service/url/URLStreamHandlerService
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller current ()Ljava/util/Optional; 4 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002962f119018
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/Messages buildVariants (Ljava/lang/String;)[Ljava/lang/String; 24 <appendix> argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f11a000
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller trackCurrent ()Ljava/util/Optional; 19 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002962f118de0
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller getCurrent ()Ljava/util/Optional; 12 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002962f118ba8
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass org/eclipse/core/runtime/ServiceCaller$ReferenceAndService
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/eclipse/core/internal/runtime/AdapterManager
instanceKlass org/eclipse/core/runtime/IAdapterManager
instanceKlass org/eclipse/core/internal/runtime/PlatformURLConverter
instanceKlass org/eclipse/core/internal/runtime/RuntimeLog
instanceKlass org/eclipse/core/runtime/IStatus
instanceKlass org/eclipse/core/internal/runtime/PlatformLogWriter
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceImpl
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller <init> (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V 39 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002962f113000
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 26 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002962f10b718
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 17 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002962f10b4e8
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 1 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002962f10b2d0
instanceKlass  @bci org/osgi/framework/FrameworkUtil <clinit> ()V 27 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002962f10b0a8
instanceKlass org/osgi/framework/connect/FrameworkUtilHelper
instanceKlass  @bci org/osgi/framework/FrameworkUtil <clinit> ()V 8 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002962f10aca8
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller <init> (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V 31 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002962f112c88
instanceKlass org/osgi/framework/FrameworkUtil
instanceKlass org/eclipse/core/runtime/ServiceCaller
instanceKlass org/eclipse/core/internal/runtime/Activator
instanceKlass org/apache/felix/scr/impl/config/ScrMetaTypeProviderServiceFactory
instanceKlass org/apache/felix/scr/impl/config/ScrManagedServiceServiceFactory
instanceKlass org/apache/felix/scr/impl/ComponentCommands$2
instanceKlass org/apache/felix/scr/impl/ComponentCommands$1
instanceKlass org/apache/felix/scr/impl/ComponentCommands
instanceKlass org/eclipse/osgi/storage/ManifestLocalization$BundleResourceBundle
instanceKlass org/eclipse/osgi/storage/ManifestLocalization
instanceKlass org/apache/felix/scr/impl/Activator$ScrExtension
instanceKlass org/apache/felix/scr/impl/ComponentActorThread$1
instanceKlass org/apache/felix/scr/impl/ComponentActorThread
instanceKlass org/apache/felix/scr/impl/runtime/ServiceComponentRuntimeImpl
instanceKlass org/apache/felix/scr/impl/manager/RegionConfigurationSupport
instanceKlass java/util/TimerTask
instanceKlass org/apache/felix/scr/impl/manager/ComponentHolder
instanceKlass org/apache/felix/scr/impl/ComponentRegistry
instanceKlass org/osgi/util/tracker/BundleTracker
instanceKlass org/apache/felix/scr/impl/logger/ScrLogManager$1
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LogDomain
instanceKlass org/apache/felix/scr/impl/logger/LogManager$Lock
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LoggerFacade
instanceKlass org/apache/felix/scr/impl/logger/BundleLogger
instanceKlass org/apache/felix/scr/impl/logger/ComponentLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLogger
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLoggerFactory
instanceKlass org/osgi/service/component/ComponentContext
instanceKlass org/osgi/service/component/ComponentServiceObjects
instanceKlass org/apache/felix/scr/impl/inject/internal/ClassUtils
instanceKlass org/apache/felix/scr/impl/config/ScrConfigurationImpl
instanceKlass org/osgi/service/component/runtime/ServiceComponentRuntime
instanceKlass org/apache/felix/scr/impl/manager/ScrConfiguration
instanceKlass org/apache/felix/scr/impl/logger/LogConfiguration
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult
instanceKlass org/apache/felix/scr/impl/AbstractExtender
instanceKlass org/osgi/util/tracker/BundleTrackerCustomizer
instanceKlass org/eclipse/osgi/internal/weaving/WeavingHookConfigurator$WovenClassContext
instanceKlass org/eclipse/osgi/internal/weaving/WovenClassImpl
instanceKlass org/osgi/framework/hooks/weaving/WovenClass
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager$DefineContext
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$3
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel$2
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f10c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f10c000
instanceKlass org/osgi/dto/DTO
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread$Queued
instanceKlass  @bci org/eclipse/osgi/framework/eventmgr/EventManager getEventThread ()Lorg/eclipse/osgi/framework/eventmgr/EventManager$EventThread; 24 <appendix> member <vmtarget> ; # org/eclipse/osgi/framework/eventmgr/EventManager$$Lambda+0x000002962f105f10
instanceKlass  @bci org/eclipse/osgi/internal/framework/EquinoxEventPublisher notifyEventHooksPrivileged (Lorg/osgi/framework/BundleEvent;Ljava/util/Collection;)V 98 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/EquinoxEventPublisher$$Lambda+0x000002962f1059c8
instanceKlass  @bci org/eclipse/osgi/internal/framework/BundleContextImpl notifyFindHooksPriviledged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/util/Collection;)V 73 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/BundleContextImpl$$Lambda+0x000002962f105198
instanceKlass org/eclipse/osgi/framework/util/FilePath
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass org/eclipse/core/runtime/internal/adaptor/ConsoleManager
instanceKlass org/eclipse/core/runtime/internal/adaptor/DefaultStartupMonitor
instanceKlass org/eclipse/osgi/service/runnable/StartupMonitor
instanceKlass java/lang/Thread$Builder$OfVirtual
instanceKlass java/lang/Thread$Builder$OfPlatform
instanceKlass java/lang/Thread$Builder
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceFactoryUse$1
instanceKlass java/util/LinkedList$ListItr
instanceKlass java/util/LinkedList$Node
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeDescription
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeSpecification
instanceKlass org/eclipse/osgi/service/resolver/ExportPackageDescription
instanceKlass org/eclipse/osgi/service/resolver/ImportPackageSpecification
instanceKlass org/eclipse/osgi/service/resolver/GenericSpecification
instanceKlass org/eclipse/osgi/service/resolver/GenericDescription
instanceKlass org/eclipse/osgi/service/resolver/HostSpecification
instanceKlass org/eclipse/osgi/service/resolver/BundleDescription
instanceKlass org/eclipse/osgi/service/resolver/BaseDescription
instanceKlass org/eclipse/osgi/service/resolver/BundleSpecification
instanceKlass org/eclipse/osgi/service/resolver/VersionConstraint
instanceKlass org/eclipse/osgi/internal/resolver/StateImpl
instanceKlass org/eclipse/osgi/internal/resolver/StateObjectFactoryImpl
instanceKlass org/eclipse/osgi/service/resolver/Resolver
instanceKlass org/eclipse/osgi/service/resolver/State
instanceKlass org/eclipse/osgi/service/resolver/StateObjectFactory
instanceKlass org/eclipse/osgi/compatibility/state/PlatformAdminImpl
instanceKlass org/eclipse/osgi/service/resolver/PlatformAdmin
instanceKlass org/eclipse/osgi/compatibility/state/Activator
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$2
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer
instanceKlass org/eclipse/osgi/service/security/TrustEngine
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedContentConstants
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook$1
instanceKlass org/eclipse/osgi/internal/framework/XMLParsingServiceFactory
instanceKlass org/eclipse/osgi/storage/BundleLocalizationImpl
instanceKlass org/eclipse/osgi/service/localization/BundleLocalization
instanceKlass org/eclipse/osgi/storage/url/BundleURLConverter
instanceKlass org/eclipse/osgi/service/urlconversion/URLConverter
instanceKlass org/apache/felix/resolver/Logger
instanceKlass org/apache/felix/resolver/util/OpenHashMap
instanceKlass org/apache/felix/resolver/ResolutionError
instanceKlass org/apache/felix/resolver/ResolverImpl
instanceKlass org/osgi/service/resolver/Resolver
instanceKlass org/eclipse/osgi/internal/framework/legacy/StartLevelImpl
instanceKlass org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl
instanceKlass org/osgi/service/condition/ConditionImpl
instanceKlass org/osgi/service/condition/Condition
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl add (Ljava/net/ContentHandlerFactory;)V 13 <appendix> argL0 ; # org/eclipse/equinox/plurl/impl/PlurlImpl$$Lambda+0x000002962f0bf3a0
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl$5 getContent ()Ljava/util/function/Consumer; 115 <appendix> member <vmtarget> ; # org/eclipse/equinox/plurl/impl/PlurlImpl$5$$Lambda+0x000002962f0bef28
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl add (Ljava/net/URLStreamHandlerFactory;)V 13 <appendix> argL0 ; # org/eclipse/equinox/plurl/impl/PlurlImpl$$Lambda+0x000002962f0bece8
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl$PlurlFactoryHolder
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl$5 getContent ()Ljava/util/function/Consumer; 101 <appendix> member <vmtarget> ; # org/eclipse/equinox/plurl/impl/PlurlImpl$5$$Lambda+0x000002962f0be630
instanceKlass org/eclipse/osgi/internal/url/ContentHandlerFactoryImpl
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl$PlurlContentHandlerFactory
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl$LegacyFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0b8800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0b8400
instanceKlass org/eclipse/equinox/plurl/impl/StackWalkerCallStack
instanceKlass org/eclipse/equinox/plurl/impl/URLToHandler
instanceKlass java/net/UrlDeserializedState
instanceKlass java/net/InetAddress
instanceKlass java/net/ContentHandler
instanceKlass org/eclipse/equinox/plurl/PlurlStreamHandler$PlurlSetter
instanceKlass org/eclipse/equinox/plurl/impl/CallStack
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl
instanceKlass org/eclipse/osgi/internal/log/ConfigAdminListener
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f0b8000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f0b0c00
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f0b0800
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000002962f0b1b80
instanceKlass  @cpi org/eclipse/osgi/internal/serviceregistry/ServiceRegistry 1105 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f0b0400
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableCollection
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$Parser
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl
instanceKlass org/osgi/util/tracker/AbstractTracked
instanceKlass org/osgi/util/tracker/ServiceTracker
instanceKlass org/eclipse/osgi/internal/log/EventAdminAdapter
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$2
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot$SnapshotIterator
instanceKlass org/eclipse/osgi/framework/eventmgr/ListenerQueue
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyEventListenerHooksPrivileged (Lorg/osgi/framework/ServiceEvent;Ljava/util/Map;)V 77 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000002962f0b6b28
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyEventHooksPrivileged (Lorg/osgi/framework/ServiceEvent;Ljava/util/Collection;)V 77 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000002962f0b6560
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot
instanceKlass org/osgi/framework/PrototypeServiceFactory
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsensitiveKey
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceReferenceImpl
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceUse
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyListenerHooksPrivileged (Ljava/util/Collection;Z)V 106 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f0b0000
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyListenerHooksPrivileged (Ljava/util/Collection;Z)V 106 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000002962f0adc68
instanceKlass  @cpi org/eclipse/osgi/internal/serviceregistry/ServiceRegistry 1128 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f0acc00
instanceKlass org/eclipse/osgi/internal/serviceregistry/HookContext
instanceKlass org/osgi/framework/UnfilteredServiceListener
instanceKlass org/eclipse/osgi/internal/serviceregistry/FilteredServiceListener
instanceKlass org/osgi/framework/hooks/service/ListenerHook$ListenerInfo
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Entry
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap
instanceKlass org/eclipse/osgi/internal/log/OrderedExecutor
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl$2
instanceKlass org/osgi/service/startlevel/StartLevel
instanceKlass org/osgi/service/packageadmin/PackageAdmin
instanceKlass org/eclipse/equinox/plurl/PlurlContentHandlerFactory
instanceKlass java/net/ContentHandlerFactory
instanceKlass org/eclipse/equinox/plurl/PlurlStreamHandlerFactory
instanceKlass org/eclipse/equinox/plurl/PlurlFactory
instanceKlass org/eclipse/equinox/plurl/Plurl
instanceKlass org/eclipse/osgi/internal/framework/SystemBundleActivator
instanceKlass org/eclipse/osgi/internal/loader/classpath/TitleVersionVendor
instanceKlass org/eclipse/osgi/internal/loader/classpath/ManifestPackageAttributes
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry$PDEData
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry
instanceKlass org/eclipse/osgi/internal/loader/classpath/FragmentClasspath
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$ClassNameLock$1
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$ClassNameLock
instanceKlass org/eclipse/osgi/internal/container/KeyBasedLockStore
instanceKlass org/eclipse/osgi/internal/loader/BundleLoaderSources
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$1
instanceKlass org/eclipse/osgi/internal/loader/sources/PackageSource
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver$StorageSaverTask
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass jdk/internal/vm/ThreadContainers
instanceKlass jdk/internal/vm/StackableScope
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executors
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 92 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x000002962f0ce660
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 76 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002962f0ce430
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 66 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002962f0ce1f0
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 47 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x000002962f0cdfc8
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 31 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002962f0cdd98
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 21 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002962f0cdb58
instanceKlass  @cpi org/eclipse/core/internal/filesystem/local/LocalFile 759 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f0ac800
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 59 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002962f0cd938
instanceKlass  @cpi org/eclipse/core/internal/net/Policy 113 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f0ac400
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 49 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002962f0cd708
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 39 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002962f0cd4d8
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 29 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x000002962f0cd290
instanceKlass  @cpi java/lang/SecurityManager 518 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f0ac000
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 17 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002962f0cd060
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$1
instanceKlass org/osgi/framework/ServiceObjects
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl
instanceKlass org/osgi/framework/hooks/weaving/WovenClassListener
instanceKlass org/osgi/framework/hooks/weaving/WeavingHook
instanceKlass org/osgi/framework/hooks/service/FindHook
instanceKlass org/osgi/framework/hooks/service/EventListenerHook
instanceKlass org/osgi/framework/hooks/service/EventHook
instanceKlass org/osgi/framework/hooks/bundle/FindHook
instanceKlass org/osgi/framework/hooks/bundle/EventHook
instanceKlass org/osgi/framework/hooks/bundle/CollisionHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$FrameworkHookHolder
instanceKlass org/osgi/framework/hooks/service/ListenerHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistrationImpl
instanceKlass org/osgi/framework/ServiceRegistration
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager
instanceKlass org/eclipse/osgi/internal/framework/EquinoxEventPublisher
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0a5c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0a5800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0a5400
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleEntry
instanceKlass java/util/TimSort
instanceKlass org/eclipse/osgi/container/ModuleDatabase$2
instanceKlass java/util/ArrayList$SubList$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0a5000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0a4c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0a4800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f0a4400
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1480 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f0a4000
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass java/lang/reflect/TypeVariable
instanceKlass org/eclipse/osgi/container/ModuleWiring$LoaderInitializer
instanceKlass org/eclipse/osgi/container/ModuleWiring
instanceKlass org/eclipse/osgi/container/ModuleWire
instanceKlass org/osgi/framework/wiring/BundleWire
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/eclipse/osgi/internal/container/Capabilities$NamespaceSet
instanceKlass org/eclipse/osgi/container/ModuleRequirement
instanceKlass org/osgi/framework/wiring/BundleRequirement
instanceKlass org/eclipse/osgi/container/ModuleRevision$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$8
instanceKlass org/eclipse/osgi/container/ModuleCapability
instanceKlass org/osgi/framework/wiring/BundleCapability
instanceKlass org/osgi/resource/Capability
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$3
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$1
instanceKlass org/eclipse/osgi/internal/container/NamespaceList
instanceKlass org/eclipse/osgi/container/ModuleRevision$1
instanceKlass org/osgi/framework/wiring/BundleWiring
instanceKlass org/osgi/resource/Wiring
instanceKlass org/eclipse/osgi/container/ModuleRevision
instanceKlass org/eclipse/osgi/container/ModuleRevisions
instanceKlass org/osgi/framework/wiring/BundleRevisions
instanceKlass org/lombokweb/asm/Opcodes
instanceKlass org/lombokweb/asm/Handler
instanceKlass lombok/patcher/MethodLogistics
instanceKlass org/lombokweb/asm/Label
instanceKlass org/lombokweb/asm/Type
instanceKlass org/lombokweb/asm/Frame
instanceKlass org/lombokweb/asm/Context
instanceKlass org/lombokweb/asm/Attribute
instanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$1
instanceKlass org/lombokweb/asm/ByteVector
instanceKlass org/lombokweb/asm/Symbol
instanceKlass org/lombokweb/asm/SymbolTable
instanceKlass org/lombokweb/asm/FieldVisitor
instanceKlass org/lombokweb/asm/MethodVisitor
instanceKlass org/lombokweb/asm/AnnotationVisitor
instanceKlass org/lombokweb/asm/ModuleVisitor
instanceKlass org/lombokweb/asm/RecordComponentVisitor
instanceKlass org/lombokweb/asm/ClassReader
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$2
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo$1
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder
instanceKlass org/osgi/resource/Wire
instanceKlass org/eclipse/osgi/container/ModuleDatabase$Persistence
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerWiring
instanceKlass org/eclipse/osgi/container/ModuleResolver
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLock
instanceKlass org/eclipse/osgi/internal/container/LockSet
instanceKlass org/osgi/framework/startlevel/FrameworkStartLevel
instanceKlass org/osgi/framework/wiring/FrameworkWiring
instanceKlass org/osgi/resource/Requirement
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport
instanceKlass org/eclipse/osgi/container/ModuleContainer
instanceKlass org/eclipse/osgi/internal/container/Capabilities
instanceKlass org/eclipse/osgi/container/Module
instanceKlass org/osgi/framework/startlevel/BundleStartLevel
instanceKlass org/eclipse/osgi/container/ModuleDatabase
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1
instanceKlass java/util/concurrent/LinkedTransferQueue$DualNode
instanceKlass java/util/concurrent/TransferQueue
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/eclipse/osgi/internal/container/AtomicLazyInitializer
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$BundleCollisionHook
instanceKlass org/osgi/framework/ServiceReference
instanceKlass org/osgi/framework/hooks/resolver/ResolverHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory
instanceKlass org/osgi/framework/hooks/resolver/ResolverHookFactory
instanceKlass org/eclipse/osgi/container/ModuleCollisionHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$1
instanceKlass org/eclipse/osgi/container/ModuleLoader
instanceKlass java/util/concurrent/Callable
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass java/util/concurrent/Executor
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityRow
instanceKlass org/eclipse/osgi/internal/permadmin/PermissionAdminTable
instanceKlass org/osgi/service/permissionadmin/PermissionInfo
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionUpdate
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionInfo
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityAdmin
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionAdmin
instanceKlass org/osgi/service/permissionadmin/PermissionAdmin
instanceKlass org/eclipse/osgi/storage/PermissionData
instanceKlass  @bci org/eclipse/osgi/storage/Storage connectPersistentBundles (Ljava/util/List;)V 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000002962f091668
instanceKlass  @cpi org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler 409 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f094000
instanceKlass org/eclipse/osgi/storage/BundleInfo$Generation
instanceKlass org/eclipse/osgi/storage/BundleInfo
instanceKlass org/eclipse/osgi/framework/util/ObjectPool
instanceKlass jdk/internal/util/ByteArray
instanceKlass org/eclipse/osgi/storagemanager/StorageManager$Entry
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFile$CacheInfo
instanceKlass java/util/ComparableTimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/io/DeleteOnExitHook$1
instanceKlass java/io/DeleteOnExitHook
instanceKlass  @bci org/eclipse/osgi/framework/internal/reliablefile/ReliableFile createTempFile (Ljava/lang/String;Ljava/lang/String;Ljava/io/File;)Ljava/io/File; 61 <appendix> argL0 ; # org/eclipse/osgi/framework/internal/reliablefile/ReliableFile$$Lambda+0x000002962f08c800
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFile
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/FileLock
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass org/eclipse/osgi/internal/location/Locker_JavaNio
instanceKlass org/eclipse/osgi/storagemanager/StorageManager
instanceKlass org/eclipse/osgi/storage/FrameworkExtensionInstaller
instanceKlass org/eclipse/osgi/storage/bundlefile/MRUBundleFileList
instanceKlass org/eclipse/osgi/framework/eventmgr/EventDispatcher
instanceKlass org/osgi/framework/Filter
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleFile
instanceKlass org/eclipse/osgi/storage/ContentProvider
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor
instanceKlass org/eclipse/osgi/storage/Storage
instanceKlass  @bci org/eclipse/osgi/internal/cds/CDSHookConfigurator addHooks (Lorg/eclipse/osgi/internal/hookregistry/HookRegistry;)V 77 <appendix> argL0 ; # org/eclipse/osgi/internal/cds/CDSHookConfigurator$$Lambda+0x000002962f08acf8
instanceKlass org/eclipse/osgi/signedcontent/SignedContent
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f08c400
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory$StorageHook
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory
instanceKlass org/osgi/framework/BundleActivator
instanceKlass org/eclipse/osgi/internal/cds/CDSHookConfigurator
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook
instanceKlass org/eclipse/osgi/signedcontent/SignedContentFactory
instanceKlass org/eclipse/osgi/internal/hookregistry/ActivatorHookFactory
instanceKlass org/osgi/framework/wiring/BundleRevision
instanceKlass org/osgi/resource/Resource
instanceKlass org/eclipse/osgi/internal/connect/ConnectHookConfigurator
instanceKlass org/eclipse/osgi/internal/hookregistry/HookConfigurator
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$ConnectModules
instanceKlass  @bci org/eclipse/osgi/internal/log/ExtendedLogServiceImpl applyLogLevels (Lorg/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext;)V 20 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/log/ExtendedLogServiceImpl$$Lambda+0x000002962f0884a0
instanceKlass  @bci org/eclipse/osgi/internal/log/ExtendedLogServiceImpl applyLogLevels (Lorg/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext;)V 5 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/log/ExtendedLogServiceImpl$$Lambda+0x000002962f088278
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints 160 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f08c000
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory$1
instanceKlass org/eclipse/osgi/framework/log/FrameworkLog
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory
instanceKlass org/osgi/service/log/FormatterLogger
instanceKlass org/eclipse/osgi/internal/log/LoggerImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerAdmin
instanceKlass org/osgi/service/log/admin/LoggerContext
instanceKlass org/eclipse/osgi/internal/log/LoggerContextTargetMap
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager$MockSystemBundle
instanceKlass org/osgi/service/log/admin/LoggerAdmin
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory
instanceKlass org/eclipse/osgi/framework/util/ArrayMap
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory$1
instanceKlass org/osgi/service/log/LogEntry
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory
instanceKlass org/osgi/framework/ServiceFactory
instanceKlass org/eclipse/equinox/log/ExtendedLogReaderService
instanceKlass org/osgi/service/log/LogReaderService
instanceKlass org/eclipse/equinox/log/ExtendedLogService
instanceKlass org/eclipse/equinox/log/Logger
instanceKlass org/osgi/service/log/Logger
instanceKlass org/osgi/service/log/LogService
instanceKlass org/osgi/service/log/LoggerFactory
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager
instanceKlass org/osgi/framework/AllServiceListener
instanceKlass org/osgi/framework/ServiceListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogWriter
instanceKlass org/eclipse/equinox/log/LogFilter
instanceKlass org/eclipse/equinox/log/SynchronousLogListener
instanceKlass org/osgi/service/log/LogListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogServices
instanceKlass org/eclipse/osgi/util/ManifestElement
instanceKlass org/eclipse/osgi/internal/debug/Debug
instanceKlass org/eclipse/osgi/service/debug/DebugOptionsListener
instanceKlass org/eclipse/osgi/service/debug/DebugTrace
instanceKlass org/eclipse/osgi/internal/debug/FrameworkDebugOptions
instanceKlass org/osgi/util/tracker/ServiceTrackerCustomizer
instanceKlass java/nio/file/FileVisitor
instanceKlass org/eclipse/osgi/storage/StorageUtil
instanceKlass org/eclipse/osgi/internal/location/BasicLocation
instanceKlass org/eclipse/osgi/internal/location/EquinoxLocations
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass java/util/UUID
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/eclipse/osgi/internal/container/InternalUtils
instanceKlass org/osgi/framework/Version
instanceKlass org/eclipse/osgi/internal/location/Locker
instanceKlass org/eclipse/osgi/internal/location/LocationHelper
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration$ConfigValues
instanceKlass org/eclipse/osgi/internal/util/Tokenizer
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/util/logging/PlatformLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerAccessor
instanceKlass jdk/internal/logger/LazyLoggers$LoggerAccessor
instanceKlass jdk/internal/logger/AbstractLoggerWrapper
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/lang/System$LoggerFinder
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerFactories
instanceKlass jdk/internal/logger/LazyLoggers$1
instanceKlass jdk/internal/logger/LazyLoggers
instanceKlass jdk/internal/event/EventHelper$ThreadTrackHolder
instanceKlass java/net/URLClassLoader$2
instanceKlass org/eclipse/osgi/internal/framework/AliasMapper
instanceKlass org/eclipse/osgi/framework/util/KeyedElement
instanceKlass org/eclipse/osgi/internal/hookregistry/ClassLoaderHook
instanceKlass org/eclipse/osgi/internal/hookregistry/HookRegistry
instanceKlass org/eclipse/osgi/service/datalocation/Location
instanceKlass org/eclipse/osgi/service/debug/DebugOptions
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration
instanceKlass org/eclipse/osgi/service/environment/EnvironmentInfo
# instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$$InjectedInvoker+0x000002962f02c400
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller$InjectedInvokerHolder
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f02c000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f02ac00
instanceKlass java/lang/annotation/Target
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/Method;Ljava/lang/Class;)V 23 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x000002962f03ce88
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/ProxyGenerator$ProxyMethod;)V 10 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x000002962f03cc58
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass  @bci java/lang/reflect/Proxy getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/lang/reflect/Proxy$$Lambda+0x000002962f03c530
instanceKlass  @bci java/lang/module/ModuleDescriptor$Builder packages (Ljava/util/Set;)Ljava/lang/module/ModuleDescriptor$Builder; 17 <appendix> argL0 ; # java/lang/module/ModuleDescriptor$Builder$$Lambda+0x800000002
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass  @bci java/lang/reflect/Proxy$ProxyBuilder getDynamicModule (Ljava/lang/ClassLoader;)Ljava/lang/Module; 4 <appendix> argL0 ; # java/lang/reflect/Proxy$ProxyBuilder$$Lambda+0x000002962f03c110
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 35 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x000002962f03b8b8
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass org/eclipse/osgi/framework/util/SecureAction$1
instanceKlass org/eclipse/osgi/framework/util/SecureAction
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer
instanceKlass org/eclipse/osgi/launch/Equinox
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f02a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f02a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f02a000
instanceKlass java/util/EventObject
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$InitialBundle
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$StartupEventListener
instanceKlass org/osgi/framework/FrameworkListener
instanceKlass org/osgi/framework/SynchronousBundleListener
instanceKlass java/util/concurrent/Semaphore
instanceKlass org/osgi/framework/BundleContext
instanceKlass org/osgi/framework/BundleReference
instanceKlass org/osgi/framework/launch/Framework
instanceKlass org/osgi/framework/Bundle
instanceKlass org/osgi/framework/BundleListener
instanceKlass java/util/EventListener
instanceKlass org/eclipse/equinox/plurl/PlurlStreamHandler
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter
instanceKlass  @bci java/io/FilePermissionCollection add (Ljava/security/Permission;)V 68 <appendix> argL0 ; # java/io/FilePermissionCollection$$Lambda+0x000002962f035b80
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass java/net/URLClassLoader$1
instanceKlass  @bci org/eclipse/equinox/launcher/Main basicRun ([Ljava/lang/String;)V 162 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f024b38
instanceKlass java/nio/file/FileStore
instanceKlass sun/nio/fs/WindowsSecurity
instanceKlass sun/nio/fs/AbstractAclFileAttributeView
instanceKlass java/nio/file/attribute/AclFileAttributeView
instanceKlass java/nio/file/attribute/FileOwnerAttributeView
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass org/eclipse/equinox/launcher/JNIBridge
instanceKlass  @bci java/util/stream/MatchOps makeRef (Ljava/util/function/Predicate;Ljava/util/stream/MatchOps$MatchKind;)Ljava/util/stream/TerminalOp; 20 <appendix> member <vmtarget> ; # java/util/stream/MatchOps$$Lambda+0x000002962f0333f0
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass  @bci org/eclipse/equinox/launcher/Main extractFromJAR (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 146 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f0246b0
instanceKlass java/util/AbstractList$RandomAccessSpliterator
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryFromFragment (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 96 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f024470
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryFromFragment (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 86 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f024240
instanceKlass  @bci java/util/zip/ZipFile stream ()Ljava/util/stream/Stream; 25 <appendix> member <vmtarget> ; # java/util/zip/ZipFile$$Lambda+0x000002962f0326e8
instanceKlass java/util/Spliterators$AbstractSpliterator
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator initPartialTraversalState ()V 37 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x000002962f0324d0
instanceKlass java/util/function/BooleanSupplier
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator initPartialTraversalState ()V 24 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x000002962f031a40
instanceKlass java/util/stream/StreamSpliterators
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 190 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f024000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 180 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01bbf0
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Streams$2
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass  @bci java/util/stream/AbstractPipeline spliterator ()Ljava/util/Spliterator; 103 <appendix> member <vmtarget> ; # java/util/stream/AbstractPipeline$$Lambda+0x000002962f030f00
instanceKlass java/util/stream/Streams$ConcatSpliterator
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 143 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01b9c0
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 133 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01b7a8
instanceKlass  @cpi org/eclipse/equinox/launcher/Main 2098 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f022c00
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 117 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01b570
instanceKlass java/util/function/IntUnaryOperator
instanceKlass java/util/stream/Streams$RangeIntSpliterator
instanceKlass java/util/stream/IntStream
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/JVMConfigurator setDefaultEnvironmentVM (Lorg/eclipse/jdt/launching/IVMInstall;Ljava/lang/String;)Z 106 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f022800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f022400
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/JVMConfigurator setDefaultEnvironmentVM (Lorg/eclipse/jdt/launching/IVMInstall;Ljava/lang/String;)Z 106 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f022000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f021c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f021800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f021400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f021000
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/JVMConfigurator setDefaultEnvironmentVM (Lorg/eclipse/jdt/launching/IVMInstall;Ljava/lang/String;)Z 106 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f020c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f020800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f020400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f020000
instanceKlass java/io/RandomAccessFile$1
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/Messages initializeMessages (Ljava/lang/String;Ljava/lang/Class;)V 68 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f01fc00
instanceKlass java/lang/invoke/LambdaFormEditor$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f01f800
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/Messages initializeMessages (Ljava/lang/String;Ljava/lang/Class;)V 68 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f01f400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f01f000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f01ec00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f01e800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f01e400
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/Messages initializeMessages (Ljava/lang/String;Ljava/lang/Class;)V 68 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f01e000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f01dc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f01d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f01d400
instanceKlass  @bci org/eclipse/equinox/launcher/Main searchFor (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 95 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01b338
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 34 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01b108
instanceKlass java/util/stream/ReduceOps$2ReducingSink
instanceKlass  @bci java/util/function/BinaryOperator maxBy (Ljava/util/Comparator;)Ljava/util/function/BinaryOperator; 6 <appendix> member <vmtarget> ; # java/util/function/BinaryOperator$$Lambda+0x000002962f07f170
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;Ljava/util/Comparator;)Ljava/util/Comparator; 12 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002962f07eee0
instanceKlass  @cpi java/util/Comparator 256 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f01d000
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002962f07ec50
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 34 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000002962f01aed8
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 18 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000002962f01acc8
instanceKlass  @bci java/util/Comparator thenComparing (Ljava/util/Comparator;)Ljava/util/Comparator; 7 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002962f07e9c0
instanceKlass  @cpi java/util/Comparator 251 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f01cc00
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 8 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000002962f01aab8
instanceKlass  @bci java/util/Comparator comparingInt (Ljava/util/function/ToIntFunction;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002962f07e730
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 462 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f01c800
instanceKlass  @cpi org/eclipse/jdt/internal/core/JavaModelManager$TouchJob 156 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f01c400
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000002962f01a8a8
instanceKlass java/util/function/ToIntFunction
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 18 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01a678
instanceKlass  @cpi org/eclipse/jdt/internal/launching/environments/EnvironmentsManager 654 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f01c000
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 8 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01a240
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass  @bci org/eclipse/debug/internal/core/IInternalDebugCoreConstants <clinit> ()V 3 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f019c00
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass  @bci java/util/stream/ReferencePipeline toArray ()[Ljava/lang/Object; 1 <appendix> argL0 ; # java/util/stream/ReferencePipeline$$Lambda+0x000002962f07d900
instanceKlass java/util/ImmutableCollections$Access$1
instanceKlass jdk/internal/access/JavaUtilCollectionAccess
instanceKlass java/util/ImmutableCollections$Access
instanceKlass  @bci org/eclipse/equinox/launcher/Main getArrayFromList (Ljava/lang/String;)Ljava/util/List; 32 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f01a000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getArrayFromList (Ljava/lang/String;)Ljava/util/List; 22 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f015cb0
instanceKlass java/util/regex/Pattern$1MatcherIterator
instanceKlass sun/net/www/MimeEntry
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder$1
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder
instanceKlass sun/net/www/MimeTable$2
instanceKlass sun/net/www/MimeTable$1
instanceKlass sun/net/www/MimeTable
instanceKlass java/net/URLConnection$1
instanceKlass java/net/FileNameMap
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f019800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f019400
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f019000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f018c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f018800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f018400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f018000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f017c00
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/Messages initializeMessages (Ljava/lang/String;Ljava/lang/Class;)V 68 <appendix> argL1 argL0 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f017800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f017400
instanceKlass  @bci org/eclipse/jdt/internal/compiler/parser/Parser initTables ()V 9 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f017000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f016c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f016800
instanceKlass java/lang/Long$LongCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f016400
instanceKlass java/util/Collections$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass java/net/spi/URLStreamHandlerProvider
instanceKlass java/net/URL$1
instanceKlass java/net/URL$2
instanceKlass java/net/URL$ThreadTrackHolder
instanceKlass  @bci org/eclipse/equinox/launcher/Main basicRun ([Ljava/lang/String;)V 29 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f015aa0
instanceKlass java/util/function/IntFunction
instanceKlass  @bci org/eclipse/equinox/launcher/Main processCommandLine (Ljava/util/List;)Ljava/util/List; 832 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002962f015880
instanceKlass  @cpi org/eclipse/core/internal/events/NotificationManager$NotifyJob 101 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f016000
instanceKlass  @bci java/util/ArrayDeque copyElements (Ljava/util/Collection;)V 2 <appendix> member <vmtarget> ; # java/util/ArrayDeque$$Lambda+0x000002962f07b258
instanceKlass java/lang/Thread$ThreadNumbering
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass java/security/Policy
instanceKlass jdk/internal/misc/PreviewFeatures
instanceKlass jdk/internal/misc/MainMethodFinder
instanceKlass org/eclipse/equinox/launcher/Main
instanceKlass sun/security/util/ManifestEntryVerifier$SunProviderHolder
instanceKlass java/util/Base64$Encoder
instanceKlass java/util/Base64$Decoder
instanceKlass java/util/Base64
instanceKlass javax/crypto/SecretKey
instanceKlass sun/security/util/Length
instanceKlass sun/security/util/KeyUtil
instanceKlass java/security/interfaces/XECKey
instanceKlass java/security/interfaces/ECKey
instanceKlass sun/security/util/JarConstraintsParameters
instanceKlass sun/security/util/ConstraintsParameters
instanceKlass java/security/CodeSigner
instanceKlass java/security/Timestamp
instanceKlass sun/security/timestamp/TimestampToken
instanceKlass java/security/cert/CertPath
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f014c00
instanceKlass java/math/MutableBigInteger
instanceKlass sun/security/rsa/RSAPadding
instanceKlass sun/security/rsa/RSACore
instanceKlass java/security/interfaces/RSAPrivateCrtKey
instanceKlass sun/security/pkcs/PKCS8Key
instanceKlass sun/security/util/InternalPrivateKey
instanceKlass java/security/interfaces/RSAPrivateKey
instanceKlass java/security/PrivateKey
instanceKlass javax/security/auth/Destroyable
instanceKlass jdk/internal/icu/util/CodePointTrie$Data
instanceKlass jdk/internal/icu/util/CodePointMap
instanceKlass jdk/internal/icu/util/VersionInfo
instanceKlass  @bci jdk/internal/module/SystemModuleFinders$SystemModuleReader open (Ljava/lang/String;)Ljava/util/Optional; 6 <appendix> member <vmtarget> ; # jdk/internal/module/SystemModuleFinders$SystemModuleReader$$Lambda+0x000002962f0735d8
instanceKlass jdk/internal/module/Checks
instanceKlass jdk/internal/icu/impl/ICUBinary$1
instanceKlass jdk/internal/icu/impl/ICUBinary
instanceKlass jdk/internal/icu/impl/NormalizerImpl$IsAcceptable
instanceKlass jdk/internal/icu/impl/ICUBinary$Authenticate
instanceKlass jdk/internal/icu/impl/NormalizerImpl
instanceKlass jdk/internal/icu/impl/Norm2AllModes$Norm2AllModesSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes$NFKCSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes
instanceKlass jdk/internal/icu/text/Normalizer2
instanceKlass jdk/internal/icu/text/NormalizerBase$ModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$NFKDModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$1
instanceKlass jdk/internal/icu/text/NormalizerBase$Mode
instanceKlass jdk/internal/icu/text/NormalizerBase
instanceKlass java/text/Normalizer
instanceKlass sun/security/x509/AVAKeyword
instanceKlass java/util/StringJoiner
instanceKlass sun/security/jca/ServiceId
instanceKlass java/security/Signature$1
instanceKlass jdk/internal/access/JavaSecuritySignatureAccess
instanceKlass java/security/SignatureSpi
instanceKlass sun/security/util/SignatureUtil
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/pkcs/SigningCertificateInfo$ESSCertId
instanceKlass sun/security/pkcs/SigningCertificateInfo
instanceKlass sun/security/pkcs/PKCS9Attribute
instanceKlass sun/security/pkcs/PKCS9Attributes
instanceKlass java/time/Instant
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/LocalTime
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/temporal/TemporalField
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass  @bci java/time/ZoneOffset ofTotalSeconds (I)Ljava/time/ZoneOffset; 37 <appendix> argL0 ; # java/time/ZoneOffset$$Lambda+0x80000000e
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/ZoneId
instanceKlass  @bci java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000026
instanceKlass  @bci java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000027
instanceKlass java/util/regex/CharPredicates
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints$Holder
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint
instanceKlass java/util/StringTokenizer
instanceKlass java/security/spec/ECFieldF2m
instanceKlass java/security/spec/ECParameterSpec
instanceKlass java/security/spec/ECPoint
instanceKlass java/security/spec/EllipticCurve
instanceKlass java/security/spec/ECFieldFp
instanceKlass java/security/spec/ECField
instanceKlass sun/security/util/CurveDB
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass sun/security/util/AbstractAlgorithmConstraints$1
instanceKlass sun/security/util/AlgorithmDecomposer
instanceKlass sun/security/util/DisabledAlgorithmConstraints$JarHolder
instanceKlass  @bci java/util/regex/Pattern DOT ()Ljava/util/regex/Pattern$CharPredicate; 0 <appendix> argL0 ; # java/util/regex/Pattern$$Lambda+0x000002962f066e08
instanceKlass java/util/regex/ASCII
instanceKlass sun/security/util/AbstractAlgorithmConstraints
instanceKlass java/security/AlgorithmConstraints
instanceKlass sun/security/pkcs/SignerInfo
instanceKlass java/security/cert/PolicyQualifierInfo
instanceKlass sun/security/x509/CertificatePolicyId
instanceKlass sun/security/x509/PolicyInformation
instanceKlass sun/security/x509/DistributionPoint
instanceKlass sun/security/x509/DNSName
instanceKlass sun/security/x509/URIName
instanceKlass sun/security/x509/GeneralName
instanceKlass sun/security/x509/AccessDescription
instanceKlass sun/security/x509/GeneralNames
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/lang/System$Logger
instanceKlass jdk/internal/event/EventHelper
instanceKlass sun/security/jca/JCAUtil
instanceKlass sun/security/util/MemoryCache$CacheEntry
instanceKlass sun/security/x509/KeyIdentifier
instanceKlass java/util/TreeMap$Entry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f014800
instanceKlass sun/security/x509/OIDMap$OIDInfo
instanceKlass sun/security/x509/PKIXExtensions
instanceKlass sun/security/x509/OIDMap
instanceKlass sun/security/x509/Extension
instanceKlass java/security/cert/Extension
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass sun/security/x509/CertificateExtensions
instanceKlass sun/security/rsa/RSAUtil
instanceKlass java/security/interfaces/RSAPublicKey
instanceKlass java/security/interfaces/RSAKey
instanceKlass java/security/spec/PSSParameterSpec
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass java/security/spec/RSAPrivateKeySpec
instanceKlass java/security/spec/RSAPublicKeySpec
instanceKlass java/security/KeyFactorySpi
instanceKlass sun/security/rsa/SunRsaSignEntries
instanceKlass sun/security/jca/ProviderList$ServiceList$1
instanceKlass java/security/KeyFactory
instanceKlass  @bci java/security/spec/EncodedKeySpec <clinit> ()V 0 <appendix> argL0 ; # java/security/spec/EncodedKeySpec$$Lambda+0x000002962f05d7f8
instanceKlass  @cpi org/eclipse/jdt/internal/core/search/processing/JobManager 551 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f014400
instanceKlass jdk/internal/access/JavaSecuritySpecAccess
instanceKlass java/security/spec/EncodedKeySpec
instanceKlass java/security/spec/KeySpec
instanceKlass sun/security/util/BitArray
instanceKlass sun/security/x509/X509Key
instanceKlass java/security/PublicKey
instanceKlass java/security/Key
instanceKlass sun/security/x509/CertificateX509Key
instanceKlass java/util/Date
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass sun/security/x509/CertificateValidity
instanceKlass sun/security/x509/AVA
instanceKlass sun/security/x509/RDN
instanceKlass javax/security/auth/x500/X500Principal
instanceKlass  @bci sun/security/x509/X500Name <clinit> ()V 153 <appendix> argL0 ; # sun/security/x509/X500Name$$Lambda+0x000002962f05b400
instanceKlass sun/security/x509/X500Name
instanceKlass sun/security/x509/GeneralNameInterface
instanceKlass sun/security/x509/CertificateAlgorithmId
instanceKlass sun/security/x509/SerialNumber
instanceKlass sun/security/x509/CertificateSerialNumber
instanceKlass sun/security/x509/CertificateVersion
instanceKlass sun/security/x509/X509CertInfo
instanceKlass sun/security/util/Cache$EqualByteArray
instanceKlass java/security/cert/X509Extension
instanceKlass java/lang/Byte$ByteCache
instanceKlass  @bci sun/security/util/DerInputStream seeOptionalContextSpecific (I)Z 2 <appendix> member <vmtarget> ; # sun/security/util/DerInputStream$$Lambda+0x000002962f058e30
instanceKlass  @cpi sun/security/util/DerInputStream 295 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f014000
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/util/Cache
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/cert/CertificateFactorySpi
instanceKlass java/security/cert/CertificateFactory
instanceKlass sun/security/x509/AlgorithmId
instanceKlass sun/security/util/ByteArrayTagOrder
instanceKlass sun/security/util/ByteArrayLexOrder
instanceKlass sun/security/util/DerValue
instanceKlass sun/security/util/ObjectIdentifier
instanceKlass sun/security/pkcs/ContentInfo
instanceKlass sun/security/util/DerEncoder
instanceKlass sun/security/util/DerInputStream
instanceKlass sun/security/pkcs/PKCS7
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/security/Security$1
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass  @bci java/security/Security <clinit> ()V 9 <appendix> argL0 ; # java/security/Security$$Lambda+0x80000000b
instanceKlass java/security/Security
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass  @bci sun/security/util/ManifestDigester <init> ([B)V 350 <appendix> argL0 ; # sun/security/util/ManifestDigester$$Lambda+0x000002962f04d550
instanceKlass sun/security/util/ManifestDigester$Section
instanceKlass sun/security/util/ManifestDigester$Entry
instanceKlass sun/security/util/ManifestDigester$Position
instanceKlass sun/security/util/ManifestDigester
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass java/util/jar/JarFile$ThreadTrackHolder
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/launcher/LauncherHelper
instanceKlass jdk/internal/vm/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass jdk/internal/vm/PostVMInitHook$2
instanceKlass jdk/internal/vm/PostVMInitHook
instanceKlass lombok/patcher/scripts/ScriptBuilder$SetSymbolDuringMethodCallBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder$ReplaceMethodCallBuilder
instanceKlass lombok/eclipse/agent/EclipsePatcher$4
instanceKlass lombok/eclipse/agent/EclipsePatcher$3
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapMethodCallBuilder
instanceKlass lombok/patcher/ScriptManager$WitnessAction
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapReturnValueBuilder
instanceKlass java/nio/charset/StandardCharsets
instanceKlass lombok/patcher/ClassRootFinder
instanceKlass lombok/patcher/scripts/ScriptBuilder$AddFieldBuilder
instanceKlass java/util/Collections$1
instanceKlass lombok/patcher/PatchScript$MethodPatcherFactory
instanceKlass org/lombokweb/asm/ClassVisitor
instanceKlass lombok/patcher/Hook
instanceKlass  @bci java/util/regex/Pattern negate (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000032
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass lombok/patcher/MethodTarget
instanceKlass lombok/patcher/scripts/ScriptBuilder$ExitEarlyBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder
instanceKlass lombok/eclipse/agent/EclipseLoaderPatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher$2
instanceKlass lombok/eclipse/agent/EclipsePatcher$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f00d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f00d400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f00d000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f00cc00
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000002962f00c800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f00c400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f00c000
instanceKlass java/lang/instrument/ClassDefinition
instanceKlass lombok/patcher/ScriptManager$OurClassFileTransformer
instanceKlass lombok/patcher/Filter$1
instanceKlass lombok/patcher/TransplantMapper$1
instanceKlass java/lang/instrument/ClassFileTransformer
instanceKlass lombok/patcher/ScriptManager
instanceKlass lombok/patcher/TransplantMapper
instanceKlass lombok/patcher/Filter
instanceKlass lombok/patcher/PatchScript
instanceKlass lombok/patcher/TargetMatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher
instanceKlass lombok/core/AgentLauncher$AgentLaunchable
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f008400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f008000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f007c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f007800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f007400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f007000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f006c00
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f006800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f006400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f006000
instanceKlass  @cpi sun/management/spi/PlatformMBeanProvider$PlatformComponent 113 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f005c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f005800
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000002962f005400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f005000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f004c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f004800
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
instanceKlass java/lang/invoke/MethodHandles$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f004400
instanceKlass sun/invoke/util/ValueConversions$1
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002962f004000
instanceKlass jdk/internal/foreign/MemorySessionImpl
instanceKlass java/lang/foreign/MemorySegment$Scope
instanceKlass lombok/core/AgentLauncher$AgentInfo
instanceKlass lombok/core/AgentLauncher
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass sun/net/www/MessageHeader
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass java/net/URLConnection
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass java/util/WeakHashMap$HashIterator
instanceKlass java/net/URLDecoder
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass  @bci java/util/regex/Pattern Single (I)Ljava/util/regex/Pattern$BmpCharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002a
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/net/URI$Parser
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/util/Arrays$ArrayItr
instanceKlass lombok/launch/PackageShader
instanceKlass java/io/Reader
instanceKlass lombok/launch/Main
instanceKlass  @bci jdk/internal/reflect/DirectMethodHandleAccessor invokeImpl (Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 136 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002962f002c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f002800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f002400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f002000
instanceKlass  @bci org/eclipse/jdt/internal/compiler/classfmt/ClassFileReader toUri (Ljava/lang/String;)Ljava/net/URI; 21 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f001c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f001800
instanceKlass sun/instrument/InstrumentationImpl$1
instanceKlass lombok/launch/Agent
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/zip/ZipFile$2
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/ThreadLocal
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass sun/instrument/TransformerManager$TransformerInfo
instanceKlass sun/instrument/TransformerManager
instanceKlass jdk/internal/loader/NativeLibraries$3
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext
instanceKlass jdk/internal/loader/NativeLibraries$2
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass  @bci sun/instrument/InstrumentationImpl <clinit> ()V 16 <appendix> argL0 ; # sun/instrument/InstrumentationImpl$$Lambda+0x000002962f0430e0
instanceKlass sun/instrument/InstrumentationImpl
instanceKlass java/lang/instrument/Instrumentation
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass jdk/internal/module/ModuleBootstrap$SafeModuleFinder
instanceKlass  @bci java/lang/WeakPairMap computeIfAbsent (Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object; 18 <appendix> member <vmtarget> ; # java/lang/WeakPairMap$$Lambda+0x000002962f042998
instanceKlass  @bci java/lang/Module implAddExportsOrOpens (Ljava/lang/String;Ljava/lang/Module;ZZ)V 145 <appendix> argL0 ; # java/lang/Module$$Lambda+0x000002962f042080
instanceKlass  @bci jdk/internal/module/ModuleBootstrap decode (Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/Map; 193 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002962f041e50
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/StringCoding
instanceKlass java/lang/Readable
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/util/SequencedMap
instanceKlass java/util/SequencedSet
instanceKlass java/lang/ModuleLayer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/module/ModuleFinder$1
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/lang/module/Resolver
instanceKlass java/lang/module/Configuration
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 791 <appendix> member <vmtarget> ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002962f041288
instanceKlass  @cpi org/eclipse/jdt/internal/core/index/MetaIndex 213 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002962f000c00
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 779 <appendix> member <vmtarget> ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002962f041040
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 767 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002962f040e10
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 757 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002962f040be0
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004d
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004f
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004e
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000050
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass  @bci jdk/internal/module/DefaultRoots exportsAPI (Ljava/lang/module/ModuleDescriptor;)Z 9 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000056
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass  @bci java/util/stream/Collectors castingIdentity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000043
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000041
instanceKlass java/util/function/BinaryOperator
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003a
instanceKlass java/util/function/BiConsumer
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000046
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 42 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000053
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 32 <appendix> member <vmtarget> ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000057
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 21 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000054
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass  @bci org/eclipse/jdt/internal/compiler/util/CharDeduplication <clinit> ()V 44 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002962f000800
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 11 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000055
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/util/function/Predicate
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/lang/invoke/LambdaMetafactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002962f000400
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$1
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/Void
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterator
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass jdk/internal/module/DefaultRoots
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil$MASKS
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass java/lang/StringUTF16
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass java/lang/Module$EnableNativeAccess
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/reflect/AccessFlag$18
instanceKlass java/lang/reflect/AccessFlag$17
instanceKlass java/lang/reflect/AccessFlag$16
instanceKlass java/lang/reflect/AccessFlag$15
instanceKlass java/lang/reflect/AccessFlag$14
instanceKlass java/lang/reflect/AccessFlag$13
instanceKlass java/lang/reflect/AccessFlag$12
instanceKlass java/lang/reflect/AccessFlag$11
instanceKlass java/lang/reflect/AccessFlag$10
instanceKlass java/lang/reflect/AccessFlag$9
instanceKlass java/lang/reflect/AccessFlag$8
instanceKlass java/lang/reflect/AccessFlag$7
instanceKlass java/lang/reflect/AccessFlag$6
instanceKlass java/lang/reflect/AccessFlag$5
instanceKlass java/lang/reflect/AccessFlag$4
instanceKlass java/lang/reflect/AccessFlag$3
instanceKlass java/lang/reflect/AccessFlag$2
instanceKlass java/lang/reflect/AccessFlag$1
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$all
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass java/lang/reflect/Modifier
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/StrictMath
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/util/HexFormat
instanceKlass jdk/internal/util/ClassFileDumper
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass jdk/internal/misc/Blocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/Collections
instanceKlass java/lang/Thread$ThreadIdentifiers
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$2
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/io/Writer
instanceKlass java/io/PrintStream$1
instanceKlass jdk/internal/access/JavaIOPrintStreamAccess
instanceKlass jdk/internal/misc/InternalLock
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass jdk/internal/util/Preconditions$3
instanceKlass jdk/internal/util/Preconditions$2
instanceKlass jdk/internal/util/Preconditions$4
instanceKlass java/util/function/BiFunction
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/Function
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/lang/Runtime
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/NativeReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/Math
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/reflect/Reflection
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass jdk/internal/misc/VM
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/FillerObject
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/SequencedCollection
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/lang/Enum
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/foreign/abi/ABIDescriptor
instanceKlass jdk/internal/foreign/abi/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass jdk/internal/vm/StackChunk
instanceKlass jdk/internal/vm/Continuation
instanceKlass jdk/internal/vm/ContinuationScope
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread$Constants
instanceKlass java/lang/Thread$FieldHolder
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 124 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 3 8 1 7 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/System 1 1 834 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 7 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 9 12 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 9 12 1 8 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 9 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 8 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 7 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/ByteArrayInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass com/sun/jna/Native$6
instanceKlass org/eclipse/osgi/internal/permadmin/EquinoxSecurityManager
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$ClassContext
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$Finder
ciInstanceKlass java/lang/SecurityManager 1 1 576 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 11 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 7 12 1 1 10 12 1 10 12 1 18 12 1 18 10 7 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 7 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 7 12 1 1 10 7 1 9 7 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 7 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 7 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 7 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 15 10 7 12 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/SecurityManager packageAccessLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager packageDefinitionLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager nonExportedPkgs Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/security/AccessController 1 1 295 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 7 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$GenerationProtectionDomain
ciInstanceKlass java/security/ProtectionDomain 1 1 348 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 7 1 8 1 10 12 1 10 11 10 7 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 7 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 398 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 7 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 7 1 10 12 10 100 12 1 1 1 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 10 12 10 12 1 1 11 7 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 152 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Comparable 1 0 12 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/constant/Constable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map 1 1 263 11 7 12 1 1 1 11 12 1 1 10 7 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 7 12 1 9 7 12 1 1 1 7 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 11 12 1 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1698 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 8 1 10 12 1 10 11 100 12 1 1 1 11 7 12 1 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 7 1 10 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 10 12 1 1 7 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 7 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 7 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 7 1 10 12 1 1 7 11 7 1 9 12 1 1 9 12 1 7 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 7 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 12 11 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 12 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/AnnotatedElement 1 1 164 11 7 12 1 1 1 11 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 18 12 1 18 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 16 1 16 1 15 11 12 16 16 1 15 10 100 12 1 1 1 16 1 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor 1 0 17 100 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/lang/reflect/GenericDeclaration 1 0 30 7 1 7 1 7 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1
ciInstanceKlass java/lang/reflect/Type 1 1 17 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfField 1 0 21 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/Class isPrimitive ()Z 256 0 128 0 -1
ciInstanceKlass java/lang/StringBuilder 1 1 422 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 605 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 7 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 100 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 10 12 10 12 1 10 10 10 12 1 10 5 0 10 10 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 100 1 10 100 1 10 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 16 1 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 10 0 48655 0 -1
ciInstanceKlass java/lang/Appendable 1 0 14 100 1 100 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 131 11 7 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 11 12 1 1 11 7 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 11 12 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/AutoCloseable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Closeable 1 0 14 100 1 100 1 100 1 1 1 1 100 1 1 1
instanceKlass java/text/ParseException
instanceKlass org/eclipse/equinox/security/storage/StorageException
instanceKlass javax/xml/transform/TransformerException
instanceKlass org/eclipse/jface/text/templates/TemplateException
instanceKlass sun/nio/fs/WindowsException
instanceKlass lombok/eclipse/agent/PatchDelegate$CantMakeDelegates
instanceKlass org/eclipse/jface/text/BadLocationException
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$InvalidBindingException
instanceKlass org/eclipse/jdt/core/compiler/InvalidInputException
instanceKlass org/eclipse/jdt/internal/compiler/classfmt/ClassFormatException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/util/concurrent/TimeoutException
instanceKlass org/osgi/service/application/ApplicationException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass org/eclipse/core/runtime/CoreException
instanceKlass org/osgi/service/prefs/BackingStoreException
instanceKlass org/apache/felix/scr/impl/inject/methods/SuitableMethodNotAccessibleException
instanceKlass org/xml/sax/SAXException
instanceKlass javax/xml/parsers/ParserConfigurationException
instanceKlass org/osgi/service/resolver/ResolutionException
instanceKlass java/security/GeneralSecurityException
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLockException
instanceKlass java/security/PrivilegedActionException
instanceKlass java/lang/InterruptedException
instanceKlass org/osgi/framework/InvalidSyntaxException
instanceKlass org/osgi/framework/BundleException
instanceKlass java/net/URISyntaxException
instanceKlass java/lang/instrument/UnmodifiableClassException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass lombok/eclipse/agent/PatchDelegate$DelegateRecursion
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 404 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 7 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 11 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass org/eclipse/jdt/internal/compiler/util/Messages$MessagesProperties
instanceKlass org/eclipse/core/internal/resources/SaveManager$MasterTable
instanceKlass org/eclipse/osgi/util/NLS$MessagesProperties
instanceKlass org/eclipse/core/internal/preferences/SortedProperties
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 690 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 3 10 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 7 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 9 12 1 1 7 1 7 1 10 12 1 7 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 10 12 1 7 1 10 10 12 1 10 12 1 100 1 10 10 12 1 1 10 7 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 11 7 1 8 1 10 100 1 11 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 10 11 12 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass org/apache/felix/scr/impl/helper/ReadOnlyDictionary
instanceKlass org/osgi/framework/FrameworkUtil$MapAsDictionary
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$UnmodifiableDictionary
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$SystemBundle$SystemBundleHeaders
instanceKlass org/eclipse/osgi/storage/BundleInfo$CachedManifest
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 516 7 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 7 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 7 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 4 10 12 1 1 10 12 1 8 1 4 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1444 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 10 12 1 1 3 10 12 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 10 7 12 1 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 12 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 10 12 10 7 12 1 1 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 7 1 8 1 10 10 10 12 1 10 12 1 1 8 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 7 1 100 1 10 12 7 1 10 10 7 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 8 1 10 12 1 10 12 1 10 9 12 1 10 12 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 7 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 1024 0 8399 0 456
ciMethod java/lang/String toString ()Ljava/lang/String; 512 0 13524 0 80
ciMethod java/lang/String length ()I 702 0 655070 0 112
ciMethod java/lang/String charAt (I)C 538 0 2132530 0 200
ciInstanceKlass java/lang/constant/ConstantDesc 1 0 37 100 1 100 1 1 1 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/InternalError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass javax/xml/parsers/FactoryConfigurationError
instanceKlass java/lang/ThreadDeath
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass sun/nio/ch/ChannelInputStream
instanceKlass org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory$NamedPipeInputStream
instanceKlass org/eclipse/core/internal/resources/ContentDescriptionManager$LazyFileInputStream
instanceKlass java/io/ObjectInputStream
instanceKlass org/eclipse/core/internal/localstore/SafeChunkyInputStream
instanceKlass org/eclipse/core/internal/registry/BufferedRandomInputStream
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$RewindableInputStream
instanceKlass org/eclipse/osgi/storage/url/reference/ReferenceInputStream
instanceKlass java/util/jar/JarVerifier$VerifierStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 195 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 3 7 1 8 1 10 10 7 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 7 1 5 0 10 12 1 100 1 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1287 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 7 1 9 7 1 9 7 1 9 9 7 1 9 7 1 9 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$1
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1108 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 12 1 8 1 10 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 7 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 100 1 10 12 1 1 7 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 439 10 100 12 1 1 1 10 100 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 400 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 12 1 1 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 7 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 1 100 1 8 1 10 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 7 12 1 1 8 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 581 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 12 1 7 1 8 1 10 12 1 8 1 11 100 12 1 1 1 7 1 11 7 12 1 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 7 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 1 9 12 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 16 1 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Member 1 1 37 100 1 10 12 1 1 100 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/debug/core/model/RuntimeProcess$ProcessMonitorThread
instanceKlass com/sun/jna/internal/Cleaner$CleanerThread
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass org/eclipse/core/internal/jobs/InternalWorker
instanceKlass org/eclipse/core/internal/jobs/Worker
instanceKlass java/util/TimerThread
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread
instanceKlass org/eclipse/equinox/launcher/Main$SplashHandler
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
instanceKlass java/lang/BaseVirtualThread
ciInstanceKlass java/lang/Thread 1 1 870 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 12 1 10 100 12 1 1 100 1 8 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 9 12 1 1 10 12 1 7 1 10 12 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 3 8 1 7 1 5 0 10 7 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 8 1 10 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 8 1 9 7 12 1 1 9 12 1 1 5 0 100 1 10 100 1 10 100 1 10 7 1 10 8 1 10 12 1 1 10 7 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 9 12 1 1 100 1 10 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 10 12 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 10 12 9 12 1 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 10 12 1 10 12 1 1 9 12 1 9 12 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 8 1 10 9 12 1 10 12 1 7 1 8 1 10 10 12 1 8 1 10 12 1 1 9 12 10 12 8 1 10 10 12 1 10 12 1 8 1 10 12 1 10 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 10 12 1 100 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread NEW_THREAD_BINDINGS Ljava/lang/Object; java/lang/Class
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciInstanceKlass java/lang/Runnable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 771 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 7 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 10 100 1 10 10 12 1 8 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 7 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 9 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 8 1 7 1 10 10 7 12 1 1 1 10 12 1 8 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/Module 1 1 1070 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 10 12 1 10 7 12 1 1 8 1 8 1 10 8 1 8 1 9 12 1 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 11 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 7 1 11 12 1 7 1 7 1 10 12 1 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 11 7 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 1 7 1 8 1 10 12 1 1 100 1 11 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 8 1 18 12 1 1 100 1 100 1 9 12 1 1 9 12 1 9 12 1 11 100 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 16 1 15 10 12 16 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/lang/StringLatin1 1 1 392 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 7 1 8 1 10 12 1 8 1 10 12 1 1 100 1 10 10 12 10 7 12 1 1 1 8 1 8 1 8 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 460 7 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 5 0 5 0 7 1 3 5 0 3 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 100 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 10 12 1 4 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 9 12 1 1 10 12 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass java/lang/Character 1 1 604 7 1 7 1 100 1 9 12 1 1 8 1 9 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/StringConcatHelper 1 1 313 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 5 0 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 11 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 10 12 1 10 12 1 1 11 12 1 7 1 7 1 100 1 8 1 10 12 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 8 1 9 12 1 1 9 7 12 1 1 1 10 7 12 1 1 1 7 1 9 12 1 1 10 12 1 1 9 100 12 1 1 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/StringConcatHelper UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StringLatin1 equals ([B[B)Z 878 752 7423 0 -1
ciMethod java/lang/StringLatin1 inflate ([BI[BII)V 0 0 96 0 -1
ciMethod java/lang/StringLatin1 charAt ([BI)C 538 0 2132908 0 160
ciMethod java/lang/StringLatin1 replace ([BI[BI[BI)Ljava/lang/String; 0 0 12 0 -1
ciMethod java/lang/StringLatin1 replace ([BCC)Ljava/lang/String; 206 6232 999 0 1256
ciMethod java/lang/StringLatin1 canEncode (I)Z 516 0 48512 0 104
ciInstanceKlass java/lang/StringUTF16 1 1 604 7 1 7 1 10 7 12 1 1 1 100 1 10 7 1 3 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 10 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 10 12 1 3 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 10 12 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 8 1 8 1 10 12 1 1 100 1 10 10 7 12 1 1 1 10 100 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 5 0 5 0 10 12 1 10 12 10 12 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Integer 1 1 453 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
instanceKlass java/math/BigDecimal
instanceKlass com/google/gson/internal/LazilyParsedNumber
instanceKlass com/sun/jna/IntegerType
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StringUTF16 putChar ([BII)V 1024 0 14008 0 -1
ciMethod java/lang/StringUTF16 newBytesFor (I)[B 0 0 91 0 -1
ciMethod java/lang/StringUTF16 charAt ([BI)C 0 0 82 0 -1
ciMethod java/lang/StringUTF16 replace ([BIZ[BIZ[BIZ)Ljava/lang/String; 0 0 1 0 -1
ciMethod java/lang/StringUTF16 replace ([BCC)Ljava/lang/String; 0 0 1 0 -1
ciInstanceKlass java/lang/Thread$FieldHolder 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Thread$Constants 0 0 59 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 7 1 8 1 10 12 1 9 7 12 1 1 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 411 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 18 12 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 11 12 1 11 12 1 1 100 1 10 10 12 1 100 1 10 18 12 1 1 11 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 12 1 1 10 12 1 1 11 7 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 8 1 10 8 1 10 12 1 10 12 1 8 1 9 12 1 1 9 12 1 10 100 12 1 1 1 100 9 12 1 1 7 1 9 12 1 10 12 10 12 1 1 100 10 12 9 12 1 10 12 1 100 1 10 11 12 1 1 7 1 10 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadGroup $assertionsDisabled Z 1
ciInstanceKlass java/lang/Thread$UncaughtExceptionHandler 1 0 16 100 1 100 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 374 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 10 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
instanceKlass java/lang/ThreadBuilders$BoundVirtualThread
instanceKlass java/lang/VirtualThread
ciInstanceKlass java/lang/BaseVirtualThread 0 0 36 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass java/lang/VirtualThread 0 0 907 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 9 12 1 1 9 12 1 100 1 10 10 12 1 10 100 12 1 1 10 9 10 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 1 10 9 10 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 7 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 18 9 100 12 1 1 1 11 100 12 1 1 1 11 100 1 11 12 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 9 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 10 12 1 1 10 12 1 10 12 7 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 10 12 1 10 12 1 10 7 12 1 1 8 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 10 10 10 12 9 12 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 18 12 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 18 12 1 10 100 12 1 1 1 100 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 100 12 1 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 1 18 12 1 1 5 0 9 12 1 10 12 1 18 12 1 100 1 10 12 10 7 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 8 10 12 1 1 8 8 9 12 1 8 10 12 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 16 15 10 12 16 15 10 12 16 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1
ciInstanceKlass java/lang/ThreadBuilders$BoundVirtualThread 0 0 132 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/ContinuationScope 0 0 50 10 100 12 1 1 1 10 100 12 1 1 1 100 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/StackChunk 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 279 7 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 4 7 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 1 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 3 3 100 1 4 4 4 3 10 12 1 1 9 12 1 1 100 1 10 3 3 4 4 10 12 1 3 3 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 4 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Float $assertionsDisabled Z 1
ciInstanceKlass java/lang/Double 1 1 290 7 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 1 10 12 1 1 10 7 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 1 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 6 0 1 6 0 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 213 7 1 100 1 10 7 12 1 1 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 222 7 1 7 1 100 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer$IntegerCache 1 1 95 10 7 12 1 1 1 7 1 10 7 12 1 1 1 9 7 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 3 10 12 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 9 12 1 100 1 10 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 1 1 1 1
staticfield java/lang/Integer$IntegerCache high I 127
staticfield java/lang/Integer$IntegerCache cache [Ljava/lang/Integer; 256 [Ljava/lang/Integer;
staticfield java/lang/Integer$IntegerCache $assertionsDisabled Z 1
ciInstanceKlass java/lang/Long 1 1 524 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 10 12 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 573 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/FillerObject 0 0 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 190 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 7 1 100 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass java/util/ResourceBundle$BundleReference
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$SoftRef
instanceKlass sun/security/util/MemoryCache$SoftCacheEntry
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass org/eclipse/jdt/internal/core/util/WeakHashSetOfCharArray$HashableWeakReference
instanceKlass org/eclipse/jdt/internal/core/util/WeakHashSet$HashableWeakReference
instanceKlass com/sun/jna/CallbackReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass java/util/ResourceBundle$KeyElementReference
instanceKlass org/eclipse/osgi/internal/container/KeyBasedLockStore$LockWeakRef
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
instanceKlass com/sun/jna/internal/Cleaner$CleanerRef
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 155 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 9 12 1 100 1 10 12 1 7 1 11 100 12 1 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 7 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer ENABLED Z 1
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass lombok/core/configuration/CapitalizationStrategy
instanceKlass lombok/core/configuration/NullCheckExceptionType
instanceKlass lombok/core/configuration/CallSuperType
instanceKlass lombok/core/configuration/FlagUsageType
instanceKlass org/eclipse/jdt/internal/compiler/impl/JavaFeature
instanceKlass org/eclipse/jdt/internal/compiler/parser/Scanner$ScanContext
instanceKlass org/eclipse/osgi/container/Module$StopOptions
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$ArchiveValidity
instanceKlass org/eclipse/jdt/internal/compiler/parser/TerminalToken
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ProjectsManager$CHANGE_TYPE
instanceKlass org/eclipse/lsp4j/FileChangeType
instanceKlass org/eclipse/jdt/ls/core/internal/semantictokens/TokenModifier
instanceKlass org/eclipse/jdt/ls/core/internal/semantictokens/TokenType
instanceKlass org/eclipse/lsp4j/TextDocumentSyncKind
instanceKlass java/nio/file/StandardCopyOption
instanceKlass com/google/common/base/CaseFormat
instanceKlass org/eclipse/lsp4j/MessageType
instanceKlass org/eclipse/lsp4j/DiagnosticTag
instanceKlass org/eclipse/lsp4j/PrepareSupportDefaultBehavior
instanceKlass org/eclipse/lsp4j/CompletionItemKind
instanceKlass org/eclipse/lsp4j/CompletionItemTag
instanceKlass org/eclipse/lsp4j/InsertTextMode
instanceKlass org/eclipse/lsp4j/SymbolTag
instanceKlass org/eclipse/lsp4j/SymbolKind
instanceKlass com/google/gson/ReflectionAccessFilter$FilterResult
instanceKlass com/google/gson/stream/JsonToken
instanceKlass com/google/gson/Strictness
instanceKlass com/google/gson/ToNumberPolicy
instanceKlass com/google/gson/FieldNamingPolicy
instanceKlass com/google/gson/LongSerializationPolicy
instanceKlass org/eclipse/jdt/ls/core/internal/ServiceStatus
instanceKlass org/eclipse/jdt/ls/core/internal/DiagnosticsState$ErrorLevel
instanceKlass javax/xml/catalog/CatalogFeatures$State
instanceKlass jdk/xml/internal/XMLSecurityManager$NameMap
instanceKlass jdk/xml/internal/XMLSecurityManager$Processor
instanceKlass jdk/xml/internal/XMLSecurityManager$Limit
instanceKlass com/sun/org/apache/xalan/internal/utils/XMLSecurityPropertyManager$Property
instanceKlass com/sun/org/apache/xalan/internal/utils/FeaturePropertyBase$State
instanceKlass jdk/xml/internal/JdkProperty$ImplPropMap
instanceKlass jdk/xml/internal/JdkXmlFeatures$XmlFeature
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/CodeGenerationTemplate
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences$SearchScope
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/ProjectEncodingMode
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/InlayHintsParameterMode
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionGuessMethodArgumentsMode
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionMatchCaseMode
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences$FeatureStatus
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences$Severity
instanceKlass java/security/DrbgParameters$Capability
instanceKlass lombok/eclipse/agent/PatchDelegate$DelegateReceiver
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$METHOD_TYPE
instanceKlass java/math/RoundingMode
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass java/time/format/TextStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$SettingsParser
instanceKlass java/util/Locale$Category
instanceKlass java/time/format/ResolverStyle
instanceKlass java/time/format/SignStyle
instanceKlass java/time/temporal/JulianFields$Field
instanceKlass java/time/temporal/IsoFields$Unit
instanceKlass java/time/temporal/IsoFields$Field
instanceKlass java/util/Comparators$NaturalOrderComparator
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils$ValueType
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegState
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager$State
instanceKlass org/osgi/util/promise/PromiseFactory$Option
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata$ReferenceScope
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata$Scope
instanceKlass org/apache/felix/scr/impl/metadata/DSVersion
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner$NameType
instanceKlass com/sun/org/apache/xerces/internal/util/Status
instanceKlass javax/xml/catalog/CatalogFeatures$Feature
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$Property
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$State
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$NameMap
instanceKlass jdk/xml/internal/JdkProperty$State
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$Limit
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger$Level
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$ContainerEvent
instanceKlass org/eclipse/osgi/container/Module$StartOptions
instanceKlass java/lang/StackWalker$Option
instanceKlass org/eclipse/osgi/container/ModuleDatabase$Sort
instanceKlass org/eclipse/osgi/container/Module$Settings
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent
instanceKlass org/eclipse/osgi/storage/ContentProvider$Type
instanceKlass org/eclipse/osgi/container/Module$State
instanceKlass org/osgi/service/log/LogLevel
instanceKlass sun/util/logging/PlatformLogger$Level
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass java/nio/file/AccessMode
instanceKlass java/util/stream/MatchOps$MatchKind
instanceKlass java/nio/file/attribute/PosixFilePermission
instanceKlass jdk/internal/icu/util/CodePointTrie$ValueWidth
instanceKlass jdk/internal/icu/util/CodePointTrie$Type
instanceKlass java/text/Normalizer$Form
instanceKlass java/time/temporal/ChronoUnit
instanceKlass java/time/temporal/ChronoField
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint$Operator
instanceKlass java/lang/System$Logger$Level
instanceKlass sun/security/rsa/RSAUtil$KeyType
instanceKlass sun/security/util/KnownOIDs
instanceKlass lombok/patcher/StackRequest
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccess
instanceKlass java/util/zip/ZipCoder$Comparison
instanceKlass java/nio/file/LinkOption
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/concurrent/TimeUnit
instanceKlass java/util/stream/StreamShape
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/reflect/AccessFlag$Location
instanceKlass java/lang/reflect/AccessFlag
instanceKlass java/lang/module/ModuleDescriptor$Modifier
instanceKlass java/lang/reflect/ClassFileFormatVersion
instanceKlass java/lang/Thread$State
ciInstanceKlass java/lang/Enum 1 1 204 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 472 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 7 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 457 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 7 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 1 1 243 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 11 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 12 1 10 12 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 1 1 196 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 483 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 10 12 1 1 9 12 1 1 10 100 12 1 1 10 100 1 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass jdk/internal/loader/BuiltinClassLoader
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass org/eclipse/equinox/launcher/Main$StartupClassLoader
ciInstanceKlass java/net/URLClassLoader 1 1 600 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 7 12 1 1 10 12 1 11 12 1 11 12 1 1 11 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 1 10 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 1 8 1 10 100 1 10 12 1 10 7 12 1 100 1 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/jar/Manifest 1 1 339 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 10 12 1 11 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 117 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 3 10 100 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/ByteArrayInputStream $assertionsDisabled Z 1
instanceKlass java/nio/DoubleBuffer
instanceKlass java/nio/FloatBuffer
instanceKlass java/nio/ShortBuffer
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 256 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/util/Preconditions 1 1 194 10 7 12 1 1 1 11 7 12 1 1 1 11 100 12 1 1 1 7 1 100 1 10 7 12 1 1 1 10 12 1 8 1 7 1 10 7 12 1 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 7 1 10 12 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 11 12 1 8 1 8 1 11 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 7 1 10 10 12 1 1 9 12 1 1 7 1 10 9 12 1 7 1 10 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/util/Preconditions SIOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield jdk/internal/util/Preconditions AIOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield jdk/internal/util/Preconditions IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
instanceKlass java/util/AbstractList$SubList
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$CopyOnFirstWriteList
instanceKlass org/eclipse/osgi/internal/weaving/DynamicImportList
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/Vector
instanceKlass sun/security/jca/ProviderList$ServiceList
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 1 11 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 100 1 10 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 7 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Iterable 1 1 62 10 7 12 1 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 7 12 1 1 1 7 1 11 7 12 1 1 1 10 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/SequencedCollection 1 1 109 100 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/util/List 1 1 251 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 7 1 7 1 10 12 1 1 100 1 10 100 12 1 1 1 11 12 1 1 11 12 1 11 12 1 100 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciMethod java/util/List add (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod java/util/List iterator ()Ljava/util/Iterator; 0 0 1 0 -1
instanceKlass java/util/AbstractMap$2
instanceKlass com/sun/jna/Structure$StructureSet
instanceKlass java/util/IdentityHashMap$Values
instanceKlass java/util/TreeMap$Values
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder
instanceKlass java/util/AbstractQueue
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/AbstractCollection <init> ()V 708 0 86332 0 80
ciMethod java/util/AbstractList <init> ()V 284 0 71057 0 88
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass lombok/core/AnnotationValues$AnnotationValueDecodeFail
instanceKlass org/eclipse/jdt/internal/compiler/problem/ShouldNotImplement
instanceKlass org/w3c/dom/DOMException
instanceKlass java/io/UncheckedIOException
instanceKlass org/eclipse/lsp4j/jsonrpc/ResponseErrorException
instanceKlass org/eclipse/lsp4j/jsonrpc/JsonRpcException
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageIssueException
instanceKlass com/google/gson/JsonParseException
instanceKlass org/eclipse/jdt/internal/core/search/matching/MatchLocator$WrappedCoreException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass com/sun/jna/LastErrorException
instanceKlass org/eclipse/text/edits/MalformedTreeException
instanceKlass org/eclipse/jdt/internal/codeassist/select/SelectionNodeFound
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$1FoundRelevantDeltaException
instanceKlass org/eclipse/jdt/internal/codeassist/complete/InvalidCursorLocation
instanceKlass org/eclipse/jdt/internal/codeassist/complete/CompletionNodeFound
instanceKlass org/eclipse/jdt/internal/compiler/problem/AbortCompilation
instanceKlass org/eclipse/jdt/internal/compiler/lookup/SourceTypeCollisionException
instanceKlass org/eclipse/jdt/internal/core/builder/MissingSourceFileException
instanceKlass org/eclipse/jdt/internal/core/builder/ImageBuilderInternalException
instanceKlass java/lang/NegativeArraySizeException
instanceKlass org/eclipse/jdt/internal/core/ClasspathEntry$AssertionFailedException
instanceKlass org/eclipse/core/internal/events/BuildManager$JobManagerSuspendedException
instanceKlass org/eclipse/core/internal/localstore/IsSynchronizedVisitor$ResourceChangedException
instanceKlass java/lang/IllegalCallerException
instanceKlass org/eclipse/core/internal/dtree/ObjectNotFoundException
instanceKlass org/eclipse/core/internal/utils/WrappedRuntimeException
instanceKlass org/eclipse/core/runtime/OperationCanceledException
instanceKlass org/osgi/util/promise/FailedPromisesException
instanceKlass org/eclipse/core/runtime/InvalidRegistryObjectException
instanceKlass java/util/concurrent/RejectedExecutionException
instanceKlass org/eclipse/core/runtime/AssertionFailedException
instanceKlass java/util/MissingResourceException
instanceKlass org/osgi/service/component/ComponentException
instanceKlass org/osgi/framework/hooks/weaving/WeavingException
instanceKlass java/lang/reflect/InaccessibleObjectException
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/osgi/framework/ServiceException
instanceKlass java/lang/TypeNotPresentException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/eclipse/osgi/framework/util/ThreadInfoReport
instanceKlass org/eclipse/osgi/storage/Storage$StorageException
instanceKlass java/util/NoSuchElementException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/invoke/WrongMethodTypeException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/jdt/internal/core/JarPackageFragmentRootInfo$PackageContent
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$SecondaryTypesCache
instanceKlass java/lang/reflect/Executable$ParameterData
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$ProxyClassContext
instanceKlass org/eclipse/equinox/launcher/Main$Identifier
instanceKlass sun/security/pkcs/SignerInfo$AlgorithmInfo
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass jdk/internal/module/ModuleReferenceImpl$CachedHash
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass jdk/internal/reflect/ReflectionFactory$Config
instanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs
instanceKlass jdk/internal/foreign/abi/VMStorage
ciInstanceKlass java/lang/Record 1 1 22 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 780 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 10 12 1 1 10 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 10 12 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 7 1 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 7 1 1 7 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfMethod 1 0 43 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 733 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 10 9 7 12 1 1 1 9 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 11 12 1 10 100 1 11 12 1 7 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 10 7 12 1 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 12 1 10 12 1 7 1 10 100 12 1 1 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 7 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 7 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 7 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 7 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 7 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 7 1 7 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 16
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 7 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
instanceKlass com/google/gson/internal/LinkedTreeMap
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$ServiceReferenceMap
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableValueCollectionMap
instanceKlass java/util/Collections$SingletonMap
instanceKlass java/util/TreeMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 196 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 11 11 12 1 1 11 12 1 7 1 100 1 11 12 1 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 7 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 7 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
instanceKlass java/nio/file/InvalidPathException
instanceKlass java/lang/IllegalThreadStateException
instanceKlass java/nio/charset/IllegalCharsetNameException
instanceKlass java/nio/charset/UnsupportedCharsetException
instanceKlass java/lang/NumberFormatException
ciInstanceKlass java/lang/IllegalArgumentException 1 1 35 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/ExceptionInInitializerError
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 7 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 1 1 235 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/Continuation 0 0 549 9 100 12 1 1 1 9 12 1 9 12 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 11 100 12 1 1 1 10 7 1 9 12 1 1 9 12 1 1 10 8 1 10 12 1 9 12 1 1 10 11 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 9 12 1 1 11 12 1 1 9 12 1 1 8 1 10 11 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 11 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 11 7 12 1 1 10 7 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 8 1 10 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 9 12 1 1 8 1 10 7 12 1 1 10 100 12 1 1 8 1 8 1 10 12 10 100 12 1 1 1 10 7 1 10 7 12 1 1 1 18 11 100 12 1 1 1 18 12 1 11 12 1 1 7 1 10 7 12 1 1 10 12 1 1 8 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 7 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 15 11 7 12 1 1 1 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1059 7 1 100 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 8 1 8 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 12 1 1 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 10 12 1 1 7 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 7 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 10 12 1 1 8 1 8 1 8 1 7 1 8 1 7 1 8 1 7 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 7 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm DEFAULT_CUSTOMIZED Ljava/lang/invoke/MethodHandle; null
staticfield java/lang/invoke/LambdaForm DEFAULT_KIND Ljava/lang/invoke/LambdaForm$Kind; java/lang/invoke/LambdaForm$Kind
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 724 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 7 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 7 1 8 9 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 7 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 7 1 10 12 1 10 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 8 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 7 1 100 1 7 1 10 100 1 10 7 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldStaticReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 473 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 10 12 1 1 7 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/Collections 1 1 932 10 7 12 1 1 1 11 7 12 1 1 1 7 1 11 12 1 1 7 1 10 12 1 1 10 12 1 11 12 1 1 7 1 11 12 1 1 11 12 1 1 10 12 1 11 100 12 1 1 11 12 1 1 11 12 1 10 12 1 10 12 1 10 12 11 100 12 1 1 1 10 12 1 1 11 12 1 11 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 11 100 12 1 1 1 11 12 1 1 10 12 1 11 12 1 100 1 8 1 10 12 1 11 7 12 1 1 1 11 7 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 1 7 1 10 12 1 11 7 1 100 1 10 12 1 11 7 1 7 1 10 12 1 11 100 1 100 1 10 12 1 11 7 1 7 1 10 12 1 11 100 1 100 1 10 12 1 11 7 1 11 7 1 10 12 10 11 7 1 7 1 10 12 1 11 100 1 100 1 10 11 100 1 100 1 10 12 1 11 100 1 100 1 10 12 1 7 1 10 10 12 1 7 1 10 10 12 1 100 1 10 100 1 10 100 1 10 100 1 10 10 12 1 10 7 1 10 100 1 10 100 1 10 100 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 9 7 12 1 1 1 9 100 12 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 7 1 10 7 1 10 7 1 10 7 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 100 1 10 12 1 9 7 12 1 1 1 9 7 12 1 1 1 7 1 9 12 1 1 10 12 7 1 10 7 1 10 11 100 12 1 1 11 12 1 10 12 1 11 11 12 1 11 11 12 1 8 1 7 1 10 11 100 1 10 12 1 100 1 10 100 12 1 1 1 100 1 10 12 1 7 1 10 7 1 10 7 1 10 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/Collections EMPTY_SET Ljava/util/Set; java/util/Collections$EmptySet
staticfield java/util/Collections EMPTY_LIST Ljava/util/List; java/util/Collections$EmptyList
staticfield java/util/Collections EMPTY_MAP Ljava/util/Map; java/util/Collections$EmptyMap
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectMethodHandleAccessor
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 38 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/MethodAccessor 1 0 17 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstructorAccessor 1 0 16 100 1 100 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 0 0 18 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 1 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 0 0 125 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/MethodHandleFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 269 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 8 1 8 1 8 1 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 62 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 307 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 100 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 10 12 10 12 1 1 7 1 7 1 7 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 923 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 1 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 8 1 9 12 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 7 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 690 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 1 7 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 7 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/NativeEntryPoint 0 0 194 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 15 10 100 12 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/ABIDescriptor 0 0 55 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/foreign/abi/VMStorage 0 0 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 15 15 15 15 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs 0 0 66 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 18 12 1 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 8 1 15 15 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker 1 1 271 9 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 12 1 1 100 1 8 1 10 10 7 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/StackWalker DEFAULT_EMPTY_OPTION Ljava/util/EnumSet; java/util/RegularEnumSet
staticfield java/lang/StackWalker DEFAULT_WALKER Ljava/lang/StackWalker; java/lang/StackWalker
ciInstanceKlass java/lang/StackWalker$StackFrame 1 1 41 100 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 142 10 7 12 1 1 1 9 7 12 1 1 1 9 7 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 12 1 1 9 12 1 1 10 7 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/LiveStackFrame 0 0 135 100 1 10 100 12 1 1 1 11 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 12 1 10 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 375 100 1 7 1 3 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 7 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 7 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 7 1 10 18 12 1 10 12 1 1 7 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/util/ArrayList 1 1 509 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 7 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 7 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/RandomAccess 1 0 7 100 1 100 1 1 1
ciMethod java/util/ArrayList add (Ljava/lang/Object;)Z 522 0 94150 0 1448
ciMethod java/util/ArrayList iterator ()Ljava/util/Iterator; 540 0 5920 0 200
ciMethod java/util/ArrayList remove (I)Ljava/lang/Object; 514 0 2808 0 -1
ciMethod java/util/ArrayList <init> ()V 182 0 17091 0 112
ciMethod java/util/ArrayList add (Ljava/lang/Object;[Ljava/lang/Object;I)V 522 0 94150 0 -1
ciInstanceKlass java/util/regex/Pattern 1 1 1581 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 11 12 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 1 7 1 8 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 10 7 1 3 10 12 1 10 12 1 8 1 10 12 1 10 100 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 7 1 9 12 1 1 10 12 1 9 12 1 9 12 1 10 7 1 100 1 8 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 100 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 10 12 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 10 11 100 1 10 12 1 1 8 1 18 12 1 1 11 12 1 1 10 10 12 1 1 8 1 9 12 1 10 12 1 8 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 7 1 8 1 10 10 10 7 12 1 1 1 10 100 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 9 12 1 9 12 1 10 12 1 10 12 1 9 12 1 7 1 9 12 1 1 9 12 1 1 10 9 12 1 1 10 12 1 1 9 7 12 1 1 10 12 1 1 9 12 1 10 12 1 8 1 8 1 8 1 7 1 10 7 12 1 1 7 1 10 7 1 7 1 9 12 1 11 12 1 1 11 7 12 1 1 11 12 1 7 1 9 12 1 7 1 10 10 12 1 1 11 7 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 3 10 12 1 1 10 12 1 7 1 10 10 7 12 1 10 12 1 10 12 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 100 1 10 10 100 1 10 12 1 7 1 10 7 1 10 12 1 1 10 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 100 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 1 100 1 9 12 1 10 10 7 12 1 1 10 12 1 1 9 12 1 1 11 7 12 1 1 100 1 10 10 12 1 11 7 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 10 12 1 8 1 10 12 1 11 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 8 1 7 1 10 11 12 1 1 8 1 8 1 11 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 9 100 12 1 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 7 1 10 12 1 8 1 10 12 1 8 1 11 10 12 1 1 100 1 10 100 1 10 7 1 9 7 12 1 1 1 10 12 1 11 12 1 8 1 8 1 10 12 1 11 12 1 1 9 7 12 1 1 1 7 1 10 10 12 1 1 9 12 1 8 1 10 12 1 1 100 1 9 12 1 9 12 1 10 12 1 100 1 10 100 1 10 7 1 10 11 11 12 1 8 1 10 12 1 8 1 8 1 10 12 1 9 12 1 9 12 1 9 12 1 7 1 9 7 1 7 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 9 10 12 3 11 100 1 10 7 1 10 12 1 9 9 9 12 1 8 1 10 10 9 12 1 1 10 12 1 9 12 1 10 12 1 1 7 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 3 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 11 100 1 10 12 1 100 1 10 100 1 10 7 1 10 100 1 10 10 9 12 1 9 12 1 10 12 1 18 12 1 1 18 12 1 18 18 18 12 1 18 12 1 18 12 18 12 18 18 12 18 18 18 12 18 12 18 12 18 3 3 18 18 12 18 18 18 12 1 1 18 7 1 10 100 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 12 10 7 12 1 1 10 9 12 7 1 10 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 16 1 15 10 12 16 16 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 16 15 10 12 16 15 10 12 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/util/regex/Pattern accept Ljava/util/regex/Pattern$Node; java/util/regex/Pattern$Node
staticfield java/util/regex/Pattern lastAccept Ljava/util/regex/Pattern$Node; java/util/regex/Pattern$LastNode
staticfield java/util/regex/Pattern $assertionsDisabled Z 1
ciMethod java/util/regex/Pattern matcher (Ljava/lang/CharSequence;)Ljava/util/regex/Matcher; 486 0 5439 0 -1
ciInstanceKlass java/util/regex/Matcher 1 1 489 10 7 12 1 1 1 7 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 12 1 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 11 12 1 10 12 1 100 1 8 1 10 12 1 9 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 8 1 10 10 7 12 1 1 1 7 1 10 10 10 12 1 1 10 12 1 1 10 10 7 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 8 1 11 7 12 1 1 8 1 10 100 12 1 1 10 12 1 10 12 1 8 1 8 1 10 12 1 1 8 1 10 12 1 8 1 11 7 12 1 1 1 7 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 11 12 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 11 100 12 1 1 100 1 10 100 1 10 12 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 8 8 8 1 8 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 8 1 10 12 9 12 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 11 8 1 10 12 1 8 1 8 1 8 1 100 1 8 1 10 10 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/regex/Matcher group (I)Ljava/lang/String; 516 0 6034 0 -1
ciMethod java/util/regex/Matcher matches ()Z 512 0 3475 0 -1
ciMethod java/util/regex/Matcher find ()Z 1024 0 7362 0 -1
ciInstanceKlass java/lang/annotation/Annotation 1 0 17 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/function/BiFunction 1 1 65 10 100 12 1 1 1 18 12 1 1 11 7 12 1 1 11 100 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 100 1 100 1 1
instanceKlass java/util/ArrayList$ListItr
ciInstanceKlass java/util/ArrayList$Itr 1 1 104 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 7 12 1 1 9 12 1 9 12 1 9 12 1 10 12 1 100 1 10 9 12 1 1 100 1 10 100 1 10 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/ArrayList$Itr remove ()V 48 0 24 0 0
ciMethod java/util/ArrayList$Itr hasNext ()Z 512 0 6663 0 120
ciMethod java/util/ArrayList$Itr next ()Ljava/lang/Object; 512 0 5593 0 280
ciMethod java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 540 0 5924 0 0
ciMethod java/util/ArrayList$Itr checkForComodification ()V 512 0 5617 0 0
ciMethod java/util/Iterator remove ()V 0 0 1 0 -1
ciInstanceKlass jdk/internal/util/Preconditions$4 1 1 61 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 100 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1 1 1
ciInstanceKlass java/util/Arrays$ArrayList 1 1 149 10 7 12 1 1 1 10 7 12 1 1 1 7 1 9 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 10 12 1 1 7 1 10 12 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Collections$UnmodifiableRandomAccessList
ciInstanceKlass java/util/Collections$UnmodifiableList 1 1 127 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 100 1 10 12 1 11 12 1 1 11 12 1 10 12 1 1 100 1 10 12 1 11 12 1 1 10 12 1 100 1 100 1 10 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1
ciMethod java/util/Collections$UnmodifiableList <init> (Ljava/util/List;)V 554 0 2870 0 -1
ciInstanceKlass java/util/Collections$UnmodifiableRandomAccessList 1 1 52 10 7 12 1 1 1 7 1 9 12 1 1 11 7 12 1 1 1 10 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciMethod java/util/Collections$UnmodifiableRandomAccessList <init> (Ljava/util/List;)V 554 0 2866 0 -1
ciInstanceKlass java/util/Collections$UnmodifiableCollection$1 1 1 71 9 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 11 12 1 1 100 1 10 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciMethod java/util/Collections unmodifiableList (Ljava/util/List;)Ljava/util/List; 554 0 1893 0 0
ciMethod jdk/internal/util/Preconditions checkIndex (IILjava/util/function/BiFunction;)I 538 0 4460 0 -1
ciMethod java/lang/Integer valueOf (I)Ljava/lang/Integer; 322 0 17484 0 0
ciMethod java/lang/Integer <init> (I)V 660 0 1786 0 0
ciMethod java/lang/Number <init> ()V 800 0 4482 0 0
ciMethod java/lang/OutOfMemoryError <init> (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod java/lang/StringConcatHelper newArray (J)[B 366 0 6744 0 -1
ciMethod java/lang/Math multiplyExact (II)I 0 0 54 0 -1
ciMethod java/lang/Math addExact (II)I 0 0 54 0 -1
ciMethod jdk/internal/misc/Unsafe allocateUninitializedArray (Ljava/lang/Class;I)Ljava/lang/Object; 366 0 6847 0 400
ciMethod jdk/internal/misc/Unsafe allocateUninitializedArray0 (Ljava/lang/Class;I)Ljava/lang/Object; 514 0 6733 0 -1
ciMethod java/util/Iterator next ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Iterator hasNext ()Z 0 0 1 0 -1
ciMethod java/lang/String replace (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String; 528 0 9672 0 0
ciMethod java/lang/String replace (CC)Ljava/lang/String; 566 0 6599 0 1296
ciMethod java/lang/String isLatin1 ()Z 426 0 2254458 0 0
ciMethod java/lang/String coder ()B 638 0 758211 0 88
ciMethod java/lang/String checkIndex (II)V 538 0 2142643 0 128
ciMethod java/lang/String <init> ([BB)V 512 0 30351 0 120
ciMethod java/lang/CharSequence toString ()Ljava/lang/String; 0 0 1 0 -1
ciMethod java/lang/StringBuilder append (C)Ljava/lang/StringBuilder; 772 0 250917 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 10 0 167040 0 -1
ciMethod java/lang/StringBuilder <init> ()V 10 0 29777 0 -1
ciMethod java/lang/StringBuilder <init> (I)V 404 0 45741 0 -1
ciMethod java/lang/Object getClass ()Ljava/lang/Class; 256 0 128 0 -1
ciMethod java/lang/Object <init> ()V 1024 0 606155 0 136
ciInstanceKlass lombok/patcher/TargetMatcher 1 0 15 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/MethodTarget 1 1 305 7 1 7 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 1 1 1 1 1 1 1 10 7 1 12 1 1 9 12 8 1 9 12 8 1 9 12 1 1 1 1 9 12 10 7 1 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 10 12 1 1 1 1 1 1 1 1 1 1 1 9 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 1 1 1 1 8 1 10 12 1 1 10 100 1 12 1 10 12 100 1 8 10 8 8 8 8 1 10 12 1 1 8 1 100 1 8 1 10 10 7 1 12 1 1 10 7 1 12 1 1 1 1 1 10 12 1 1 10 7 1 12 1 8 1 7 1 10 10 12 1 11 7 1 12 1 10 12 1 1 1 1 1 1 1 1 1 10 12 1 1 1 1 1 10 12 1 1 1 10 12 10 12 1 1 10 12 11 12 1 1 11 7 1 12 1 1 10 12 1 11 12 1 1 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 1 1 1 1 1 8 1 10 12 1 1 1 10 12 11 1 10 12 1 1 11 1 1 1 8 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 1 1
staticfield lombok/patcher/MethodTarget PARAM_SPEC Ljava/util/regex/Pattern; java/util/regex/Pattern
staticfield lombok/patcher/MethodTarget COMPLETE_SPEC Ljava/util/regex/Pattern; java/util/regex/Pattern
staticfield lombok/patcher/MethodTarget BRACE_PAIRS Ljava/util/regex/Pattern; java/util/regex/Pattern
ciInstanceKlass lombok/patcher/Hook 1 1 233 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 10 12 1 8 1 8 1 11 7 1 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 7 1 12 1 1 9 12 1 1 1 1 1 10 100 1 8 1 10 12 1 8 8 8 9 12 9 12 9 12 7 1 10 11 7 1 12 1 1 10 12 1 1 9 12 1 1 1 1 1 1 1 8 10 7 1 12 1 1 1 1 1 1 1 1 1 10 12 1 1 1 7 1 10 8 1 10 12 1 1 11 12 1 1 11 7 1 12 1 1 10 12 1 11 12 1 8 1 10 12 1 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 11 12 1 1 8 1 10 12 1 1 8 1 10 12 1 1 10 1 1 10 12 11 1 10 12 1 1 11 1 1 1 8 1 10 8 1 8 1 8 1 10 12 1 8 1 1 1
staticfield lombok/patcher/Hook PRIMITIVES Ljava/util/Map; java/util/Collections$UnmodifiableMap
instanceKlass lombok/patcher/scripts/AddFieldScript$1
instanceKlass lombok/patcher/PatchScript$MethodPatcher
instanceKlass lombok/patcher/PatchScript$NoopClassVisitor
instanceKlass org/lombokweb/asm/ClassWriter
ciInstanceKlass org/lombokweb/asm/ClassVisitor 1 1 175 1 7 1 7 1 1 100 1 100 1 1 1 1 1 1 1 1 12 10 1 1 12 10 3 3 3 3 3 3 3 1 100 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 12 10 1 12 10 1 100 1 1 12 10 12 9 12 9 1 1 1 1 1 3 1 100 1 8 10 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 8 12 10 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 1 1 1 12 10 1 1 1 1 8 12 10 1 1 1 8 12 10 1 1 1 12 10 1 1 1 1 1 8 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1
instanceKlass lombok/patcher/PatchScript$FixedClassWriter
ciInstanceKlass org/lombokweb/asm/ClassWriter 1 1 581 1 7 1 7 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 3 12 10 12 9 1 7 1 12 10 1 12 10 12 9 1 12 10 1 1 1 1 1 1 12 9 12 9 3 1 1 12 10 12 9 1 1 12 10 12 9 1 1 12 10 1 7 1 12 9 12 9 12 9 12 9 12 9 1 1 1 1 1 1 1 1 1 12 9 1 7 1 12 10 3 1 1 12 10 12 9 1 1 1 1 1 100 1 12 10 1 12 10 12 9 1 1 12 9 1 1 1 12 9 1 1 12 10 12 9 1 1 1 1 12 9 1 7 1 1 12 10 12 9 1 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 9 1 12 9 1 1 12 9 12 9 1 1 12 10 1 1 12 9 12 9 1 1 1 12 9 1 12 9 12 9 1 1 1 1 1 1 1 100 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 7 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 1 1 7 1 12 10 12 9 12 9 1 1 12 9 1 1 1 1 1 1 1 12 10 1 12 10 1 12 9 1 8 1 8 1 8 1 8 1 8 1 8 3 1 8 1 8 1 12 10 1 8 1 8 1 8 1 12 10 1 12 10 1 12 10 1 8 1 8 1 8 3 1 12 10 1 8 10 1 12 10 1 12 10 1 12 10 1 100 1 1 12 10 1 12 10 10 3 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 100 1 12 10 1 1 12 10 12 10 1 1 1 10 1 12 10 1 1 12 10 10 10 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 12 10 1 1 12 10 12 10 1 8 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 100 1 12 10 10 1 1 1 1 1
ciInstanceKlass lombok/patcher/PatchScript$FixedClassWriter 1 1 35 100 1 7 1 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 10 12 8 1 100 1 1 1 1 1 1 1 100 1 1
instanceKlass lombok/patcher/scripts/SetSymbolDuringMethodCallScript$2
ciInstanceKlass lombok/patcher/PatchScript$MethodPatcher 1 1 184 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 10 12 1 7 1 10 12 1 9 12 9 12 9 12 9 12 1 1 1 1 1 1 1 1 9 12 1 1 11 7 1 12 1 1 1 1 1 1 9 12 10 12 1 1 1 1 1 1 1 1 1 100 1 8 1 10 12 1 1 1 11 12 1 1 11 7 1 12 1 1 7 1 7 1 8 1 10 10 12 1 10 7 1 12 1 1 8 1 10 12 1 1 10 12 1 11 7 1 12 1 1 9 12 10 7 1 12 1 1 11 12 1 1 1 1 1 10 12 10 12 1 10 12 1 10 12 1 11 12 1 7 1 11 12 1 1 7 1 10 12 1 11 7 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/PatchScript$MethodPatcherFactory 1 0 13 100 1 100 1 1 1 1 1 1 100 1 1
ciInstanceKlass lombok/eclipse/agent/EclipsePatcher$3 1 1 84 100 1 7 1 100 1 1 1 1 10 12 1 1 1 1 1 1 8 1 10 7 1 12 1 1 10 7 1 12 1 1 8 1 11 7 1 12 1 1 11 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 1 1 1 1 8 1 10 7 1 12 1 1 1 1 1 100 1 12 1 1 1
instanceKlass lombok/patcher/scripts/WrapMethodCallScript$WrapMethodCall
instanceKlass lombok/patcher/scripts/SetSymbolDuringMethodCallScript$WrapWithSymbol
instanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$ReplaceMethodCall
instanceKlass lombok/patcher/scripts/WrapReturnValuesScript$WrapReturnValues
instanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$ExitEarly
instanceKlass org/lombokweb/asm/MethodWriter
ciInstanceKlass org/lombokweb/asm/MethodVisitor 1 1 262 1 7 1 7 1 1 100 1 100 1 1 1 1 8 1 1 1 1 1 1 1 12 10 1 1 12 10 3 3 3 3 3 3 3 1 100 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 1 12 10 12 10 1 12 10 1 100 1 1 12 10 12 9 12 9 1 1 1 1 1 1 100 10 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 8 12 10 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 100 1 100 1 1 12 10 1 100 1 8 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/MethodWriter 1 1 809 1 7 1 7 1 1 100 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 12 10 1 7 1 12 10 12 9 12 9 8 1 7 1 1 12 10 3 12 9 1 7 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 1 12 10 1 7 1 12 9 12 9 1 7 1 12 10 12 9 12 9 1 7 10 12 9 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 12 9 1 1 12 9 12 9 1 1 12 10 1 1 12 9 1 7 1 12 10 1 1 12 9 1 1 12 10 12 9 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 9 12 9 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 12 10 12 9 1 12 9 12 9 1 1 1 1 12 9 1 1 12 9 1 100 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 12 9 10 1 12 9 1 1 12 10 12 9 1 1 12 10 1 12 10 1 1 12 10 1 100 1 8 1 12 10 12 9 12 9 1 100 10 1 12 10 1 1 12 10 10 12 9 1 7 1 1 12 9 1 12 9 12 9 12 9 1 7 1 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 1 1 12 10 12 9 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 12 9 1 12 9 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 12 10 12 10 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 12 9 1 1 1 1 1 1 12 9 1 1 12 10 12 9 12 9 12 9 1 12 9 1 1 1 12 10 1 12 9 1 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 3 12 9 12 9 1 1 1 7 1 12 10 12 9 1 12 9 1 1 1 1 1 1 1 12 9 12 9 12 9 12 9 1 1 1 100 1 12 10 1 1 12 9 12 9 1 1 1 12 10 1 12 10 1 12 9 1 8 1 1 12 10 1 12 9 1 12 9 1 12 9 1 100 1 1 12 9 1 12 10 1 12 9 1 12 9 1 12 10 1 12 9 1 12 9 1 1 12 10 1 12 9 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 12 10 1 12 9 1 12 10 1 12 9 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 12 10 1 7 1 12 10 1 12 10 1 1 1 1 12 10 3 1 7 1 1 12 10 1 1 1 1 1 1 1 1 12 9 12 9 1 1 1 3 1 100 1 1 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 8 1 8 1 8 1 12 10 1 8 1 1 12 10 1 8 1 12 10 1 12 10 1 8 1 1 12 10 1 8 1 8 1 8 1 12 10 1 1 1 12 9 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1
staticfield org/lombokweb/asm/MethodWriter STACK_SIZE_DELTA [I 202
ciInstanceKlass org/lombokweb/asm/SymbolTable 1 1 638 1 7 1 7 1 1 7 1 1 100 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 7 10 12 9 1 1 1 1 7 1 1 12 9 1 1 12 10 1 12 9 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 100 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 8 1 7 1 1 12 10 12 9 12 9 1 1 12 10 1 12 10 3 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 12 9 1 1 1 1 12 10 1 7 1 12 9 1 1 1 12 9 1 1 1 1 12 10 1 12 9 1 1 1 12 10 1 1 12 10 1 1 1 1 12 9 12 9 1 1 12 9 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 1 12 10 1 7 10 1 7 1 1 12 10 1 7 10 1 7 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 12 10 1 7 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 100 10 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 100 10 1 8 1 1 12 10 1 12 10 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 1 12 10 12 10 12 9 12 9 12 9 12 9 1 12 10 1 1 12 10 1 12 10 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 9 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 9 12 10 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 12 10 1 12 10 12 10 1 1 1 1 12 10 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 12 10 12 10 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 9 1 1 1 12 9 1 1 12 9 1 12 9 1 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 1 12 10 9 1 1 1 12 9 1 100 1 1 12 10 12 10 1 1 1 1 1 1 100 1 1 12 10 1 12 9 1 1 12 10 1 12 9 12 9 1 12 10 1 1 1 10 1 1 1 1 1 1 1 1 1
instanceKlass org/lombokweb/asm/SymbolTable$Entry
ciInstanceKlass org/lombokweb/asm/Symbol 1 1 93 1 7 1 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 1 1 1 1 12 9 1 7 1 12 10 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/ByteVector 1 1 107 1 7 1 7 1 1 1 1 1 1 1 12 10 12 9 1 1 1 1 1 12 9 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 10 3 1 100 1 8 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 10 1 1 1 100 1 8 1 12 10 1 1 1 1 1 1 1
ciInstanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$1 1 1 90 7 1 7 1 100 1 1 1 1 1 1 1 1 9 12 9 12 10 12 1 1 1 1 1 1 1 10 7 1 12 1 1 10 12 1 1 10 7 1 12 1 1 100 1 100 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 7 1 10 12 1 1 1 1 1 1 1 1 1 1 12 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/util/NoSuchElementException 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/channels/OverlappingFileLockException
ciInstanceKlass java/lang/IllegalStateException 1 0 35 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/lombokweb/asm/Type 1 1 379 1 7 1 7 1 1 100 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 1 1 1 7 1 1 12 10 1 1 12 10 1 1 1 1 100 1 1 12 10 1 100 1 1 12 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 9 12 9 1 100 10 1 1 12 10 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 12 10 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 7 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 12 10 1 1 1 1 1 1 12 10 1 12 10 1 12 10 1 100 1 1 1 100 10 1 8 1 1 12 10 1 12 10 12 10 1 12 10 1 1 1 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 12 10 12 10 10 1 8 1 1 12 10 1 1 12 10 1 1 1 1 1 12 10 1 1 8 1 8 12 10 1 1 12 10 1 1 12 10 1 100 10 1 8 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 100 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield org/lombokweb/asm/Type VOID_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type BOOLEAN_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type CHAR_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type BYTE_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type SHORT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type INT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type FLOAT_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type LONG_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
staticfield org/lombokweb/asm/Type DOUBLE_TYPE Lorg/lombokweb/asm/Type; org/lombokweb/asm/Type
ciInstanceKlass org/lombokweb/asm/Label 1 1 231 1 7 1 7 1 1 100 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 9 1 100 1 8 1 12 10 12 9 1 1 12 9 1 100 1 12 9 1 1 12 9 12 9 1 7 1 1 12 10 1 1 1 1 1 7 1 1 12 10 3 1 1 12 10 1 1 1 1 1 1 1 1 7 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 12 9 1 1 1 1 1 1 1 1 12 9 1 1 1 1 1 1 1 12 9 12 9 12 9 1 1 12 10 1 1 1 1 100 12 9 12 9 1 12 9 1 12 10 1 1 1 1 12 9 1 1 1 1 1 1 1 1 12 10 1 1 1 100 10 1 8 1 1 12 10 1 12 10 12 10 12 10 1 10 1 1 1 1 1 1
staticfield org/lombokweb/asm/Label EMPTY_LIST Lorg/lombokweb/asm/Label; org/lombokweb/asm/Label
ciInstanceKlass lombok/patcher/MethodLogistics 1 1 169 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 9 12 10 7 1 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 7 1 10 12 1 1 9 12 10 12 1 9 12 7 1 10 10 7 1 12 1 1 11 12 1 1 10 12 1 11 12 1 1 10 7 1 12 1 1 9 12 9 12 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 11 12 1 1 1 11 12 1 1 10 12 1 10 7 1 12 1 1 1 1 1 1 10 12 10 12 1 1 1 1 1 1 1 1 10 12 1 1 100 1 8 1 10 12 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 1 10 1 1
ciInstanceKlass lombok/patcher/scripts/WrapReturnValuesScript$1 1 1 54 7 1 7 1 100 1 1 1 1 1 1 1 1 9 12 9 12 10 12 1 1 1 1 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 100 1 12 1 1 1 100 1 100 1 1 1 1
instanceKlass java/lang/StringIndexOutOfBoundsException
instanceKlass org/lombokweb/asm/MethodTooLargeException
instanceKlass org/lombokweb/asm/ClassTooLargeException
instanceKlass java/lang/ArrayIndexOutOfBoundsException
ciInstanceKlass java/lang/IndexOutOfBoundsException 0 0 49 10 100 12 1 1 1 10 12 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/ConcurrentModificationException 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciMethodData java/lang/Object <init> ()V 2 605643 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String isLatin1 ()Z 2 2254644 orig 80 1 0 0 0 1 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30007 0x0 0x58 0x226736 0x80000006000a0007 0x22 0x38 0x226716 0xe0003 0x226716 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String equals (Ljava/lang/Object;)Z 2 7887 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x20007 0x1b11 0x20 0x3bf 0x80104 0x0 0x0 0x296702a70c8 0x1b10 0x0 0x0 0xb0007 0x1 0xe0 0x1b10 0xf0004 0x0 0x0 0x296702a70c8 0x1b10 0x0 0x0 0x160007 0x0 0x40 0x1b10 0x210007 0x0 0x68 0x1b10 0x2c0002 0x1b10 0x2f0007 0x1a34 0x38 0xdc 0x330003 0xdc 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/lang/String 18 java/lang/String methods 0
ciMethodData java/util/AbstractCollection <init> ()V 2 85978 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x14fda 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String coder ()B 2 757894 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x30007 0x0 0x38 0xb9086 0xa0003 0xb9086 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String length ()I 2 654719 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x60005 0x9fd7f 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String charAt (I)C 2 2132657 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x10005 0x208ab1 0x0 0x0 0x0 0x0 0x0 0x40007 0x16 0x30 0x208a9f 0xc0002 0x208a9f 0x150002 0x16 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 charAt ([BI)C 2 2132639 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x30002 0x208a9f 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String checkIndex (II)V 2 2142374 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x50002 0x20b0a6 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 canEncode (I)Z 2 48254 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x8000000600040007 0x14 0x38 0xbc6b 0x80003 0xbc6b 0x18 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String <init> ([BB)V 2 30095 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x10002 0x758f 0x0 0x0 0x0 0x0 0x9 0x3 0xc 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 replace ([BCC)Ljava/lang/String; 2 896 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 85 0x10002 0x380 0x40007 0x0 0x240 0x380 0x130007 0xb0 0x58 0x57ae 0x1c0007 0x54de 0xffffffffffffffe0 0x2d0 0x1f0003 0x2d0 0x18 0x250007 0xb0 0x1c8 0x2d0 0x290002 0x2d0 0x2c0007 0x0 0xe8 0x2d0 0x310002 0x2d0 0x3d0007 0x2d0 0x38 0xd06 0x4c0003 0xd06 0xffffffffffffffe0 0x520007 0x2d0 0x70 0x748f 0x630007 0x6a3c 0x38 0xa53 0x680003 0xa53 0x18 0x710003 0x748f 0xffffffffffffffa8 0x7b0002 0x2d0 0x800002 0x0 0x8c0002 0x0 0x920007 0x0 0x80 0x0 0xa70007 0x0 0x38 0x0 0xab0003 0x0 0x18 0xb00002 0x0 0xb60003 0x0 0xffffffffffffff98 0xc00002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringConcatHelper newArray (J)[B 2 6561 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0xd0007 0x19a1 0x30 0x0 0x160002 0x0 0x210005 0x19a1 0x0 0x0 0x0 0x0 0x0 0x240004 0x0 0x0 0x296702bc9e8 0x19a1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 16 [B methods 0
ciMethodData jdk/internal/misc/Unsafe allocateUninitializedArray (Ljava/lang/Class;I)Ljava/lang/Object; 2 6664 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 43 0x10007 0x1a08 0x30 0x0 0xb0002 0x0 0x100005 0x1a08 0x0 0x0 0x0 0x0 0x0 0x130007 0x1a08 0x30 0x0 0x1d0002 0x0 0x220007 0x1a08 0x30 0x0 0x2c0002 0x0 0x8000000400330005 0x1a0c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData java/lang/String replace (CC)Ljava/lang/String; 2 6316 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 37 0x20007 0x0 0xd0 0x18ac 0x60005 0x18ac 0x0 0x0 0x0 0x0 0x0 0x90007 0x0 0x48 0x18ac 0x120002 0x18ac 0x150003 0x18ac 0x28 0x1e0002 0x0 0x230007 0xb1 0x20 0x17fb 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String toString ()Ljava/lang/String; 2 13268 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod java/lang/IllegalStateException <init> ()V 0 0 1 0 -1
ciMethodData java/util/ArrayList$Itr hasNext ()Z 2 6407 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xb0007 0x5bb 0x38 0x134c 0xf0003 0x134c 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList add (Ljava/lang/Object;)Z 2 93890 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x140005 0x16ec2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList <init> ()V 2 17000 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x4268 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/AbstractList <init> ()V 2 70915 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x11503 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData java/util/ArrayList iterator ()Ljava/util/Iterator; 2 5650 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x50002 0x1612 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 2 5654 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x60002 0x1616 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr next ()Ljava/lang/Object; 2 5337 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0x10005 0x14d9 0x0 0x0 0x0 0x0 0x0 0x110007 0x14d9 0x30 0x0 0x180002 0x0 0x270007 0x14d9 0x30 0x0 0x2e0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData java/util/ArrayList$Itr checkForComodification ()V 2 5361 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0xb0007 0x14f1 0x30 0x0 0x120002 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String replace (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String; 2 9408 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 178 0x10005 0x0 0x0 0x296702a70c8 0x24c0 0x0 0x0 0x80005 0x0 0x0 0x296702a70c8 0x24c0 0x0 0x0 0x100005 0x24c0 0x0 0x0 0x0 0x0 0x0 0x160005 0x24c0 0x0 0x0 0x0 0x0 0x0 0x1d0005 0x24c0 0x0 0x0 0x0 0x0 0x0 0x240007 0x0 0x268 0x24c0 0x80000006002a0007 0xc 0xe8 0x24b5 0x300007 0x0 0xc8 0x24b5 0x360005 0x24b5 0x0 0x0 0x0 0x0 0x0 0x3c0005 0x24b5 0x0 0x0 0x0 0x0 0x0 0x3f0005 0x24b5 0x0 0x0 0x0 0x0 0x0 0x440005 0xc 0x0 0x0 0x0 0x0 0x0 0x4a0005 0xc 0x0 0x0 0x0 0x0 0x0 0x510005 0xc 0x0 0x0 0x0 0x0 0x0 0x580007 0x0 0x88 0xc 0x5d0007 0x0 0x68 0xc 0x620007 0x0 0x48 0xc 0x780002 0xc 0x7b0003 0xc 0x28 0x970002 0x0 0x9e0007 0x1 0x20 0xb 0xab0002 0x0 0xb00002 0x0 0xb30002 0x0 0xb80003 0x0 0x28 0xc40002 0x0 0xce0002 0x0 0xd70005 0x0 0x0 0x0 0x0 0x0 0x0 0xe20007 0x0 0xe0 0x0 0xea0005 0x0 0x0 0x0 0x0 0x0 0x0 0xed0005 0x0 0x0 0x0 0x0 0x0 0x0 0xf20005 0x0 0x0 0x0 0x0 0x0 0x0 0xf90003 0x0 0xffffffffffffff38 0xfe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 3 java/lang/String 10 java/lang/String methods 0
ciMethodData java/util/Collections unmodifiableList (Ljava/util/List;)Ljava/util/List; 2 1616 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x10005 0x21 0x0 0x296702aab28 0x627 0x29676b55040 0x8 0x80007 0x0 0x78 0x650 0xc0005 0x21 0x0 0x296702aab28 0x627 0x29676b55040 0x8 0x130007 0x633 0x20 0x1d 0x190004 0xfffffffffffffffc 0x0 0x296702aab28 0x627 0x29676b55040 0x8 0x1c0007 0x4 0x48 0x62f 0x240002 0x62f 0x270003 0x62f 0x28 0x2f0002 0x4 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 6 3 java/util/ArrayList 5 java/util/Arrays$ArrayList 14 java/util/ArrayList 16 java/util/Arrays$ArrayList 25 java/util/ArrayList 27 java/util/Arrays$ArrayList methods 0
ciMethodData java/lang/Number <init> ()V 2 4082 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0xff2 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Integer valueOf (I)Ljava/lang/Integer; 2 17323 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x30007 0x64 0x40 0x4347 0xa0007 0x546 0x20 0x3e01 0x1c0002 0x5aa 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Integer <init> (I)V 2 1456 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x10002 0x5b0 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethod java/util/ConcurrentModificationException <init> ()V 0 0 1 0 -1
ciMethod lombok/patcher/TargetMatcher matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 0 0 1 0 -1
ciMethod lombok/patcher/MethodTarget decomposeFullDesc (Ljava/lang/String;)Ljava/util/List; 514 644 1906 0 0
ciMethod lombok/patcher/MethodTarget classMatches (Ljava/lang/String;)Z 26 0 1448 0 0
ciMethod lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 1024 0 15026 0 0
ciMethod lombok/patcher/MethodTarget descriptorMatch (Ljava/lang/String;)Z 22 16 1446 0 0
ciMethod lombok/patcher/MethodTarget typeSpecMatch (Ljava/lang/String;Ljava/lang/String;)Z 518 444 1680 0 -1
ciMethod lombok/patcher/MethodTarget typeMatches (Ljava/lang/String;Ljava/lang/String;)Z 538 0 49822 0 1680
ciMethod lombok/patcher/Hook getMethodName ()Ljava/lang/String; 1274 0 637 0 0
ciMethod lombok/patcher/Hook getMethodDescriptor ()Ljava/lang/String; 276 452 587 0 0
ciMethod lombok/patcher/Hook toSpec (Ljava/lang/String;)Ljava/lang/String; 544 26 1671 0 -1
ciMethod org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 928 0 7411 0 456
ciMethod org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 922 0 11729 0 0
ciMethod lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 858 1772 11138 0 -1
ciMethod lombok/patcher/PatchScript$MethodPatcherFactory createMethodVisitor (Ljava/lang/String;Ljava/lang/String;Lorg/lombokweb/asm/MethodVisitor;Llombok/patcher/MethodLogistics;)Lorg/lombokweb/asm/MethodVisitor; 0 0 1 0 -1
ciMethodData lombok/patcher/MethodTarget typeMatches (Ljava/lang/String;Ljava/lang/String;)Z 2 49553 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x60005 0xc191 0x0 0x0 0x0 0x0 0x0 0xa0005 0xc191 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethod org/lombokweb/asm/MethodVisitor <init> (I)V 928 0 7411 0 -1
ciMethod org/lombokweb/asm/MethodWriter <init> (Lorg/lombokweb/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)V 928 304 7411 0 8096
ciMethod org/lombokweb/asm/MethodWriter visitLabel (Lorg/lombokweb/asm/Label;)V 486 0 8783 0 -1
ciMethod org/lombokweb/asm/SymbolTable addConstantClass (Ljava/lang/String;)Lorg/lombokweb/asm/Symbol; 776 0 7017 0 -1
ciMethod org/lombokweb/asm/SymbolTable addConstantUtf8 (Ljava/lang/String;)I 370 120 9247 0 -1
ciMethod org/lombokweb/asm/ByteVector <init> ()V 530 0 8355 0 -1
ciMethod org/lombokweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 316 332 7444 0 -1
ciMethod org/lombokweb/asm/Label <init> ()V 488 0 17309 0 -1
ciMethod lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 6 6 253 0 0
ciMethod lombok/patcher/MethodLogistics loadOpcodeFor (Ljava/lang/String;)I 512 0 297 0 0
ciMethod lombok/patcher/MethodLogistics returnOpcodeFor (Ljava/lang/String;)I 506 0 253 0 0
ciMethod lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 520 0 550 0 0
ciMethodData org/lombokweb/asm/MethodWriter <init> (Lorg/lombokweb/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;I)V 2 6947 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 116 0x30002 0x1b23 0xb0002 0x1b23 0x1a0005 0x1b23 0x0 0x0 0x0 0x0 0x0 0x1d0007 0x1af8 0x38 0x2b 0x240003 0x2b 0x18 0x2e0005 0x1b23 0x0 0x0 0x0 0x0 0x0 0x3d0005 0x1b23 0x0 0x0 0x0 0x0 0x0 0x4c0007 0x20 0x38 0x1b03 0x500003 0x1b03 0x50 0x560005 0x20 0x0 0x0 0x0 0x0 0x0 0x5e0007 0x1ae6 0xc8 0x3d 0x640007 0x0 0xa8 0x3d 0x810007 0x3d 0x70 0x3d 0x900005 0x3d 0x0 0x0 0x0 0x0 0x0 0x9a0003 0x3d 0xffffffffffffffa8 0x9d0003 0x3d 0x18 0xb20007 0xf3 0x98 0x1a30 0xb70002 0x1a30 0xc20007 0x18aa 0x20 0x186 0xd90002 0x1a30 0xe40005 0x1a30 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x8 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 6947 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x130002 0x1b23 0x1c0007 0x1b06 0x38 0x1d 0x250003 0x1d 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 11268 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x40007 0x0 0x58 0x2c04 0x120005 0x0 0x0 0x2967037ba48 0x2c04 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 1 7 lombok/patcher/PatchScript$FixedClassWriter methods 0
ciMethodData lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 10709 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 149 0x80002 0x29d5 0x110005 0x0 0x0 0x296702aab28 0x29d5 0x0 0x0 0x180003 0x29d5 0x1e0 0x1d0005 0x0 0x0 0x2967636b818 0x1dd5 0x0 0x0 0x220004 0x0 0x0 0x2967636b8c8 0x1dd5 0x0 0x0 0x290005 0x0 0x0 0x2967636b8c8 0x1dd5 0x0 0x0 0x2d0005 0x1dd5 0x0 0x0 0x0 0x0 0x0 0x300007 0x1dc4 0xe8 0x11 0x350005 0x0 0x0 0x2967636b8c8 0x11 0x0 0x0 0x390005 0x11 0x0 0x0 0x0 0x0 0x0 0x3c0007 0x0 0x58 0x11 0x410005 0x0 0x0 0x2967636b818 0x11 0x0 0x0 0x480005 0x0 0x0 0x2967636b818 0x47aa 0x0 0x0 0x4d0007 0x1dd5 0xfffffffffffffe00 0x29d5 0x540005 0x0 0x0 0x296702aab28 0x29d5 0x0 0x0 0x5b0003 0x29d5 0x128 0x600005 0x0 0x0 0x2967636b818 0x3a82 0x0 0x0 0x8000000400650004 0x0 0x0 0x2967636b978 0x38c1 0x2967636ba28 0xe2 0x720005 0xe3 0x0 0x2967636b978 0x38c1 0x2967636ba28 0xe2 0x770007 0x398e 0x68 0xf8 0x880002 0xf8 0x8b0005 0x87 0x0 0x2967636bad8 0x9 0x2967636bb88 0x68 0x930005 0x0 0x0 0x2967636b818 0x6363 0x0 0x0 0x980007 0x3a82 0xfffffffffffffeb8 0x28e1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 16 5 java/util/ArrayList 15 java/util/ArrayList$Itr 22 lombok/patcher/Hook 29 lombok/patcher/Hook 47 lombok/patcher/Hook 65 java/util/ArrayList$Itr 72 java/util/ArrayList$Itr 83 java/util/ArrayList 93 java/util/ArrayList$Itr 100 lombok/patcher/MethodTarget 102 lombok/eclipse/agent/EclipsePatcher$3 107 lombok/patcher/MethodTarget 109 lombok/eclipse/agent/EclipsePatcher$3 120 lombok/patcher/scripts/ExitFromMethodEarlyScript$1 122 lombok/patcher/scripts/WrapReturnValuesScript$1 127 java/util/ArrayList$Itr methods 0
ciMethodData lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 2 14514 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x50005 0x38b3 0x0 0x0 0x0 0x0 0x0 0x80007 0x489 0x20 0x342a 0xf0005 0x489 0x0 0x0 0x0 0x0 0x0 0x120007 0x489 0x20 0x0 0x190002 0x489 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 1 250 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 118 0x10002 0xfa 0x90007 0xf6 0x38 0x4 0xd0003 0x4 0x18 0x150002 0xfa 0x1a0005 0x0 0x0 0x296702aab28 0xfa 0x0 0x0 0x230005 0x0 0x0 0x2967636b818 0xfa 0x0 0x0 0x280004 0x0 0x0 0x296702a70c8 0xfa 0x0 0x0 0x300002 0xfa 0x390002 0xfa 0x490002 0xfa 0x520002 0xfa 0x5b0002 0xfa 0x600003 0xfa 0x180 0x650005 0x0 0x0 0x2967636b818 0x126 0x0 0x0 0x6a0004 0x0 0x0 0x296702a70c8 0x126 0x0 0x0 0x710002 0x126 0x7a0002 0x126 0x7d0005 0x0 0x0 0x296702aab28 0x126 0x0 0x0 0x870002 0x126 0x8a0005 0x0 0x0 0x296702aab28 0x126 0x0 0x0 0x940002 0x126 0x970002 0x126 0x9a0005 0x0 0x0 0x296702aab28 0x126 0x0 0x0 0xa90005 0x0 0x0 0x2967636b818 0x220 0x0 0x0 0xae0007 0x126 0xfffffffffffffe60 0xfa 0xb40002 0xfa 0xbd0002 0xfa 0xc60002 0xfa 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 9 14 java/util/ArrayList 21 java/util/ArrayList$Itr 28 java/lang/String 48 java/util/ArrayList$Itr 55 java/lang/String 66 java/util/ArrayList 75 java/util/ArrayList 86 java/util/ArrayList 93 java/util/ArrayList$Itr methods 0
ciMethodData lombok/patcher/Hook getMethodDescriptor ()Ljava/lang/String; 1 449 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 85 0x40002 0x1c1 0xb0005 0x1c1 0x0 0x0 0x0 0x0 0x0 0x130005 0x0 0x0 0x29677c8acb0 0x1c1 0x0 0x0 0x190003 0x1c1 0xd0 0x1d0005 0x0 0x0 0x29677c8ad60 0x35a 0x0 0x0 0x220004 0x0 0x0 0x296702a70c8 0x35a 0x0 0x0 0x280002 0x35a 0x2b0005 0x35a 0x0 0x0 0x0 0x0 0x0 0x300005 0x0 0x0 0x29677c8ad60 0x51b 0x0 0x0 0x350007 0x35a 0xffffffffffffff10 0x1c1 0x3b0005 0x1c1 0x0 0x0 0x0 0x0 0x0 0x440002 0x1c1 0x470005 0x1c1 0x0 0x0 0x0 0x0 0x0 0x4c0005 0x1c1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 4 12 java/util/Collections$UnmodifiableRandomAccessList 22 java/util/Collections$UnmodifiableCollection$1 29 java/lang/String 45 java/util/Collections$UnmodifiableCollection$1 methods 0
ciMethodData lombok/patcher/MethodTarget classMatches (Ljava/lang/String;)Z 2 1435 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x50002 0x59b 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData lombok/patcher/MethodTarget descriptorMatch (Ljava/lang/String;)Z 2 1435 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 131 0x40007 0x595 0x20 0x6 0xa0002 0x595 0xd0005 0x0 0x0 0x296702aab28 0x595 0x0 0x0 0x140005 0x0 0x0 0x2967636b818 0x595 0x0 0x0 0x190004 0x0 0x0 0x296702a70c8 0x595 0x0 0x0 0x200002 0x595 0x230007 0xab 0x20 0x4ea 0x2c0005 0x0 0x0 0x29677c8acb0 0xab 0x0 0x0 0x320003 0xab 0x128 0x360005 0x0 0x0 0x2967636b818 0xe8 0x0 0x0 0x3b0004 0x0 0x0 0x296702a70c8 0xe8 0x0 0x0 0x3f0005 0x0 0x0 0x29677c8ad60 0xe8 0x0 0x0 0x440004 0x0 0x0 0x296702a70c8 0xe8 0x0 0x0 0x470002 0xe8 0x4a0007 0x7d 0x20 0x6b 0x500005 0x0 0x0 0x2967636b818 0x128 0x0 0x0 0x550007 0x3e 0x78 0xea 0x590005 0x0 0x0 0x29677c8ad60 0xea 0x0 0x0 0x5e0007 0xe8 0xfffffffffffffe60 0x2 0x620005 0x0 0x0 0x2967636b818 0x40 0x0 0x0 0x670007 0x2 0x78 0x3e 0x6b0005 0x0 0x0 0x29677c8ad60 0x3e 0x0 0x0 0x700007 0x3 0x20 0x3b 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 12 9 java/util/ArrayList 16 java/util/ArrayList$Itr 23 java/lang/String 36 java/util/Collections$UnmodifiableRandomAccessList 46 java/util/ArrayList$Itr 53 java/lang/String 60 java/util/Collections$UnmodifiableCollection$1 67 java/lang/String 80 java/util/ArrayList$Itr 91 java/util/Collections$UnmodifiableCollection$1 102 java/util/ArrayList$Itr 113 java/util/Collections$UnmodifiableCollection$1 methods 0
ciMethodData lombok/patcher/MethodTarget decomposeFullDesc (Ljava/lang/String;)Ljava/util/List; 2 1649 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 103 0x40005 0x671 0x0 0x0 0x0 0x0 0x0 0x90005 0x671 0x0 0x0 0x0 0x0 0x0 0xc0007 0x671 0xb0 0x0 0x190002 0x0 0x1d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x200005 0x0 0x0 0x0 0x0 0x0 0x0 0x230002 0x0 0x2b0002 0x671 0x320005 0x671 0x0 0x0 0x0 0x0 0x0 0x350005 0x0 0x0 0x296702aab28 0x671 0x0 0x0 0x400005 0x671 0x0 0x0 0x0 0x0 0x0 0x430005 0x671 0x0 0x0 0x0 0x0 0x0 0x470003 0x671 0x88 0x4d0005 0x764 0x0 0x0 0x0 0x0 0x0 0x500005 0x0 0x0 0x296702aab28 0x764 0x0 0x0 0x570005 0xdd5 0x0 0x0 0x0 0x0 0x0 0x5a0007 0x764 0xffffffffffffff58 0x671 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 48 java/util/ArrayList 79 java/util/ArrayList methods 0
ciMethodData lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 1 290 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 26 0x20005 0x122 0x0 0x0 0x0 0x0 0x0 0x50008 0x8 0x119 0x50 0x0 0x50 0x0 0x50 0x9 0x50 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData lombok/patcher/MethodLogistics loadOpcodeFor (Ljava/lang/String;)I 1 41 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 150 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 60 0x20005 0x29 0x0 0x0 0x0 0x0 0x0 0x50008 0x16 0x0 0xd0 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x2 0xc0 0x0 0xc0 0x1d 0xd0 0x0 0xc0 0x0 0xc0 0x2 0xc0 0x8 0xd0 0x720002 0x0 0x830002 0x0 0x870005 0x0 0x0 0x0 0x0 0x0 0x0 0x8a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x8d0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
compile lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; -1 4 inline 72 0 -1 0 lombok/patcher/PatchScript$MethodPatcher visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 1 8 0 org/lombokweb/asm/ClassVisitor visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 2 18 0 org/lombokweb/asm/ClassWriter visitMethod (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/lombokweb/asm/MethodVisitor; 1 17 0 java/util/ArrayList iterator ()Ljava/util/Iterator; 2 5 0 java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 3 6 0 java/lang/Object <init> ()V 1 72 0 java/util/ArrayList$Itr hasNext ()Z 1 29 0 java/util/ArrayList$Itr next ()Ljava/lang/Object; 2 1 0 java/util/ArrayList$Itr checkForComodification ()V 1 41 0 lombok/patcher/Hook getMethodName ()Ljava/lang/String; 1 45 0 java/lang/String equals (Ljava/lang/Object;)Z 1 84 0 java/util/ArrayList iterator ()Ljava/util/Iterator; 2 5 0 java/util/ArrayList$Itr <init> (Ljava/util/ArrayList;)V 3 6 0 java/lang/Object <init> ()V 1 147 0 java/util/ArrayList$Itr hasNext ()Z 1 96 0 java/util/ArrayList$Itr next ()Ljava/lang/Object; 2 1 0 java/util/ArrayList$Itr checkForComodification ()V 1 114 0 lombok/patcher/MethodTarget matches (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Z 2 5 0 java/lang/String equals (Ljava/lang/Object;)Z 2 15 0 lombok/patcher/MethodTarget classMatches (Ljava/lang/String;)Z 3 5 0 lombok/patcher/MethodTarget typeMatches (Ljava/lang/String;Ljava/lang/String;)Z 4 6 0 java/lang/String replace (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String; 5 1 0 java/lang/String toString ()Ljava/lang/String; 5 8 0 java/lang/String toString ()Ljava/lang/String; 5 16 0 java/lang/String length ()I 6 6 0 java/lang/String coder ()B 5 22 0 java/lang/String length ()I 6 6 0 java/lang/String coder ()B 5 29 0 java/lang/String length ()I 6 6 0 java/lang/String coder ()B 5 54 0 java/lang/String charAt (I)C 6 1 0 java/lang/String isLatin1 ()Z 6 12 0 java/lang/StringLatin1 charAt ([BI)C 7 3 0 java/lang/String checkIndex (II)V 5 60 0 java/lang/String charAt (I)C 6 1 0 java/lang/String isLatin1 ()Z 6 12 0 java/lang/StringLatin1 charAt ([BI)C 7 3 0 java/lang/String checkIndex (II)V 5 63 0 java/lang/String replace (CC)Ljava/lang/String; 6 6 0 java/lang/String isLatin1 ()Z 6 18 0 java/lang/StringLatin1 replace ([BCC)Ljava/lang/String; 7 1 0 java/lang/StringLatin1 canEncode (I)Z 7 41 0 java/lang/StringLatin1 canEncode (I)Z 7 49 0 java/lang/StringConcatHelper newArray (J)[B 8 33 0 jdk/internal/misc/Unsafe allocateUninitializedArray (Ljava/lang/Class;I)Ljava/lang/Object; 7 123 0 java/lang/String <init> ([BB)V 8 1 0 java/lang/Object <init> ()V 4 10 0 java/lang/String equals (Ljava/lang/Object;)Z 1 136 0 lombok/patcher/MethodLogistics <init> (ILjava/lang/String;)V 2 1 0 java/lang/Object <init> ()V 2 48 0 lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 2 57 0 lombok/patcher/MethodLogistics returnOpcodeFor (Ljava/lang/String;)I 2 73 0 java/util/ArrayList <init> ()V 3 1 0 java/util/AbstractList <init> ()V 4 1 0 java/util/AbstractCollection <init> ()V 5 1 0 java/lang/Object <init> ()V 2 82 0 java/util/ArrayList <init> ()V 3 1 0 java/util/AbstractList <init> ()V 4 1 0 java/util/AbstractCollection <init> ()V 5 1 0 java/lang/Object <init> ()V 2 91 0 java/util/ArrayList <init> ()V 3 1 0 java/util/AbstractList <init> ()V 4 1 0 java/util/AbstractCollection <init> ()V 5 1 0 java/lang/Object <init> ()V 2 113 0 lombok/patcher/MethodLogistics sizeOf (Ljava/lang/String;)I 2 122 0 java/lang/Integer valueOf (I)Ljava/lang/Integer; 2 135 0 java/lang/Integer valueOf (I)Ljava/lang/Integer; 3 28 0 java/lang/Integer <init> (I)V 4 1 0 java/lang/Number <init> ()V 5 1 0 java/lang/Object <init> ()V 2 148 0 lombok/patcher/MethodLogistics loadOpcodeFor (Ljava/lang/String;)I 2 151 0 java/lang/Integer valueOf (I)Ljava/lang/Integer;
