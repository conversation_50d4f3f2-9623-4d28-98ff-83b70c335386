import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../shared/widgets/custom_text_field.dart';
import '../providers/aes_crypto_providers.dart';

/// 加密输入区域组件
class CryptoInputSection extends ConsumerStatefulWidget {
  const CryptoInputSection({super.key});

  @override
  ConsumerState<CryptoInputSection> createState() => _CryptoInputSectionState();
}

class _CryptoInputSectionState extends ConsumerState<CryptoInputSection> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();

    // 监听焦点变化
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        ref.read(lastActiveFieldProvider.notifier).state = ActiveField.input;
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 监听输入文本变化
    ref.listen<String>(inputTextProvider, (previous, next) {
      if (_controller.text != next) {
        _controller.text = next;
      }
    });

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Text(
                  '输入区域',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                // 清空按钮
                IconButton(
                  onPressed: () {
                    _controller.clear();
                    ref.read(inputTextProvider.notifier).state = '';
                  },
                  icon: const Icon(Icons.clear),
                  tooltip: '清空',
                ),
                // 粘贴按钮
                IconButton(
                  onPressed: _pasteFromClipboard,
                  icon: const Icon(Icons.paste),
                  tooltip: '粘贴',
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 输入框
            CustomTextField(
              controller: _controller,
              focusNode: _focusNode,
              hintText: '请输入要加密的文本...',
              maxLines: 6,
              minLines: 6,
              onChanged: (value) {
                ref.read(inputTextProvider.notifier).state = value;
                ref.read(autoProcessProvider).onInputChanged(value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 操作按钮
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    ref.read(autoProcessProvider).processCurrentField();
                  },
                  icon: const Icon(Icons.lock),
                  label: const Text('加密'),
                ),
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: _copyToClipboard,
                  icon: const Icon(Icons.copy),
                  label: const Text('复制'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 从剪贴板粘贴
  Future<void> _pasteFromClipboard() async {
    try {
      final data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data?.text != null) {
        _controller.text = data!.text!;
        ref.read(inputTextProvider.notifier).state = data.text!;
        ref.read(autoProcessProvider).onInputChanged(data.text!);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('粘贴失败: $e')),
        );
      }
    }
  }

  /// 复制到剪贴板
  Future<void> _copyToClipboard() async {
    try {
      await Clipboard.setData(ClipboardData(text: _controller.text));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('已复制到剪贴板')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('复制失败: $e')),
        );
      }
    }
  }
}
