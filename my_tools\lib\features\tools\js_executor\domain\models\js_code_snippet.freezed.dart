// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'js_code_snippet.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$JsCodeSnippet {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get code => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  bool get isEnabled => throw _privateConstructorUsedError;

  /// Create a copy of JsCodeSnippet
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JsCodeSnippetCopyWith<JsCodeSnippet> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JsCodeSnippetCopyWith<$Res> {
  factory $JsCodeSnippetCopyWith(
          JsCodeSnippet value, $Res Function(JsCodeSnippet) then) =
      _$JsCodeSnippetCopyWithImpl<$Res, JsCodeSnippet>;
  @useResult
  $Res call(
      {int id,
      String name,
      String code,
      String description,
      DateTime createdAt,
      DateTime updatedAt,
      bool isEnabled});
}

/// @nodoc
class _$JsCodeSnippetCopyWithImpl<$Res, $Val extends JsCodeSnippet>
    implements $JsCodeSnippetCopyWith<$Res> {
  _$JsCodeSnippetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JsCodeSnippet
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? code = null,
    Object? description = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isEnabled = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isEnabled: null == isEnabled
          ? _value.isEnabled
          : isEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JsCodeSnippetImplCopyWith<$Res>
    implements $JsCodeSnippetCopyWith<$Res> {
  factory _$$JsCodeSnippetImplCopyWith(
          _$JsCodeSnippetImpl value, $Res Function(_$JsCodeSnippetImpl) then) =
      __$$JsCodeSnippetImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      String code,
      String description,
      DateTime createdAt,
      DateTime updatedAt,
      bool isEnabled});
}

/// @nodoc
class __$$JsCodeSnippetImplCopyWithImpl<$Res>
    extends _$JsCodeSnippetCopyWithImpl<$Res, _$JsCodeSnippetImpl>
    implements _$$JsCodeSnippetImplCopyWith<$Res> {
  __$$JsCodeSnippetImplCopyWithImpl(
      _$JsCodeSnippetImpl _value, $Res Function(_$JsCodeSnippetImpl) _then)
      : super(_value, _then);

  /// Create a copy of JsCodeSnippet
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? code = null,
    Object? description = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isEnabled = null,
  }) {
    return _then(_$JsCodeSnippetImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isEnabled: null == isEnabled
          ? _value.isEnabled
          : isEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$JsCodeSnippetImpl implements _JsCodeSnippet {
  const _$JsCodeSnippetImpl(
      {this.id = 0,
      required this.name,
      required this.code,
      this.description = '',
      required this.createdAt,
      required this.updatedAt,
      this.isEnabled = true});

  @override
  @JsonKey()
  final int id;
  @override
  final String name;
  @override
  final String code;
  @override
  @JsonKey()
  final String description;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  @JsonKey()
  final bool isEnabled;

  @override
  String toString() {
    return 'JsCodeSnippet(id: $id, name: $name, code: $code, description: $description, createdAt: $createdAt, updatedAt: $updatedAt, isEnabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JsCodeSnippetImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, name, code, description,
      createdAt, updatedAt, isEnabled);

  /// Create a copy of JsCodeSnippet
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JsCodeSnippetImplCopyWith<_$JsCodeSnippetImpl> get copyWith =>
      __$$JsCodeSnippetImplCopyWithImpl<_$JsCodeSnippetImpl>(this, _$identity);
}

abstract class _JsCodeSnippet implements JsCodeSnippet {
  const factory _JsCodeSnippet(
      {final int id,
      required final String name,
      required final String code,
      final String description,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      final bool isEnabled}) = _$JsCodeSnippetImpl;

  @override
  int get id;
  @override
  String get name;
  @override
  String get code;
  @override
  String get description;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  bool get isEnabled;

  /// Create a copy of JsCodeSnippet
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JsCodeSnippetImplCopyWith<_$JsCodeSnippetImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
