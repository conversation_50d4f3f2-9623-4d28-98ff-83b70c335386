import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../providers/js_executor_providers.dart';
import '../widgets/code_editor_section.dart';
import '../widgets/code_snippets_section.dart';
import '../widgets/input_output_section.dart';
import '../../../../../shared/utils/app_logger.dart';

/// JavaScript执行器页面
class JsExecutorPage extends ConsumerStatefulWidget {
  const JsExecutorPage({super.key});

  @override
  ConsumerState<JsExecutorPage> createState() => _JsExecutorPageState();
}

class _JsExecutorPageState extends ConsumerState<JsExecutorPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // 初始化自动执行器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(autoExecutorProvider);
      AppLogger.info('JavaScript执行器页面初始化完成');
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          context.go('/');
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('JavaScript执行器'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go('/'),
          ),
          bottom: TabBar(
            controller: _tabController,
            tabs: const [
              Tab(
                icon: Icon(Icons.code),
                text: '代码编辑',
              ),
              Tab(
                icon: Icon(Icons.list),
                text: '代码片段',
              ),
              Tab(
                icon: Icon(Icons.play_arrow),
                text: '执行结果',
              ),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: const [
            CodeEditorSection(),
            CodeSnippetsSection(),
            InputOutputSection(),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return Consumer(
      builder: (context, ref, child) {
        final isExecuting = ref.watch(isExecutingProvider);
        final currentTab = _tabController.index;

        if (currentTab == 0) {
          // 代码编辑页面：保存按钮
          return FloatingActionButton(
            onPressed: () => _showSaveDialog(context, ref),
            tooltip: '保存代码片段',
            child: const Icon(Icons.save),
          );
        } else if (currentTab == 2) {
          // 执行结果页面：手动执行按钮
          return FloatingActionButton(
            onPressed: isExecuting ? null : () => _executeManually(ref),
            tooltip: '手动执行',
            backgroundColor: isExecuting ? Colors.grey : null,
            child: isExecuting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  /// 显示保存对话框
  void _showSaveDialog(BuildContext context, WidgetRef ref) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('保存代码片段'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '名称',
                hintText: '请输入代码片段名称',
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: '描述',
                hintText: '请输入代码片段描述（可选）',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              final name = nameController.text.trim();
              if (name.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入代码片段名称')),
                );
                return;
              }

              final description = descriptionController.text.trim();
              final manager = ref.read(snippetManagerProvider);
              final success = await manager.saveCurrentCode(name, description);

              if (context.mounted) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success ? '保存成功' : '保存失败'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 手动执行代码
  Future<void> _executeManually(WidgetRef ref) async {
    final autoExecutor = ref.read(autoExecutorProvider);
    await autoExecutor.executeManually();
  }
}
