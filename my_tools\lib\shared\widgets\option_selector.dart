import 'package:flutter/material.dart';

/// 选项选择器组件
class OptionSelector<T> extends StatelessWidget {
  final String label;
  final T value;
  final List<T> options;
  final String Function(T) getDisplayText;
  final ValueChanged<T?> onChanged;
  final bool enabled;

  const OptionSelector({
    super.key,
    required this.label,
    required this.value,
    required this.options,
    required this.getDisplayText,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          isExpanded: true,
          items: options.map((option) {
            return DropdownMenuItem<T>(
              value: option,
              child: Text(
                getDisplayText(option),
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: enabled ? onChanged : null,
          decoration: const InputDecoration(
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }
}
