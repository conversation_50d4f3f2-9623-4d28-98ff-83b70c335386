// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'js_execution_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

JsExecutionResult _$JsExecutionResultFromJson(Map<String, dynamic> json) {
  return _JsExecutionResult.fromJson(json);
}

/// @nodoc
mixin _$JsExecutionResult {
  bool get success => throw _privateConstructorUsedError;
  String get result => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  int get executionTimeMs => throw _privateConstructorUsedError;
  DateTime? get executedAt => throw _privateConstructorUsedError;

  /// Serializes this JsExecutionResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JsExecutionResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JsExecutionResultCopyWith<JsExecutionResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JsExecutionResultCopyWith<$Res> {
  factory $JsExecutionResultCopyWith(
          JsExecutionResult value, $Res Function(JsExecutionResult) then) =
      _$JsExecutionResultCopyWithImpl<$Res, JsExecutionResult>;
  @useResult
  $Res call(
      {bool success,
      String result,
      String? error,
      int executionTimeMs,
      DateTime? executedAt});
}

/// @nodoc
class _$JsExecutionResultCopyWithImpl<$Res, $Val extends JsExecutionResult>
    implements $JsExecutionResultCopyWith<$Res> {
  _$JsExecutionResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JsExecutionResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? result = null,
    Object? error = freezed,
    Object? executionTimeMs = null,
    Object? executedAt = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      result: null == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      executionTimeMs: null == executionTimeMs
          ? _value.executionTimeMs
          : executionTimeMs // ignore: cast_nullable_to_non_nullable
              as int,
      executedAt: freezed == executedAt
          ? _value.executedAt
          : executedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JsExecutionResultImplCopyWith<$Res>
    implements $JsExecutionResultCopyWith<$Res> {
  factory _$$JsExecutionResultImplCopyWith(_$JsExecutionResultImpl value,
          $Res Function(_$JsExecutionResultImpl) then) =
      __$$JsExecutionResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success,
      String result,
      String? error,
      int executionTimeMs,
      DateTime? executedAt});
}

/// @nodoc
class __$$JsExecutionResultImplCopyWithImpl<$Res>
    extends _$JsExecutionResultCopyWithImpl<$Res, _$JsExecutionResultImpl>
    implements _$$JsExecutionResultImplCopyWith<$Res> {
  __$$JsExecutionResultImplCopyWithImpl(_$JsExecutionResultImpl _value,
      $Res Function(_$JsExecutionResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of JsExecutionResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? result = null,
    Object? error = freezed,
    Object? executionTimeMs = null,
    Object? executedAt = freezed,
  }) {
    return _then(_$JsExecutionResultImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      result: null == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      executionTimeMs: null == executionTimeMs
          ? _value.executionTimeMs
          : executionTimeMs // ignore: cast_nullable_to_non_nullable
              as int,
      executedAt: freezed == executedAt
          ? _value.executedAt
          : executedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JsExecutionResultImpl implements _JsExecutionResult {
  const _$JsExecutionResultImpl(
      {required this.success,
      this.result = '',
      this.error,
      this.executionTimeMs = 0,
      this.executedAt});

  factory _$JsExecutionResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$JsExecutionResultImplFromJson(json);

  @override
  final bool success;
  @override
  @JsonKey()
  final String result;
  @override
  final String? error;
  @override
  @JsonKey()
  final int executionTimeMs;
  @override
  final DateTime? executedAt;

  @override
  String toString() {
    return 'JsExecutionResult(success: $success, result: $result, error: $error, executionTimeMs: $executionTimeMs, executedAt: $executedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JsExecutionResultImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.result, result) || other.result == result) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.executionTimeMs, executionTimeMs) ||
                other.executionTimeMs == executionTimeMs) &&
            (identical(other.executedAt, executedAt) ||
                other.executedAt == executedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, success, result, error, executionTimeMs, executedAt);

  /// Create a copy of JsExecutionResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JsExecutionResultImplCopyWith<_$JsExecutionResultImpl> get copyWith =>
      __$$JsExecutionResultImplCopyWithImpl<_$JsExecutionResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$JsExecutionResultImplToJson(
      this,
    );
  }
}

abstract class _JsExecutionResult implements JsExecutionResult {
  const factory _JsExecutionResult(
      {required final bool success,
      final String result,
      final String? error,
      final int executionTimeMs,
      final DateTime? executedAt}) = _$JsExecutionResultImpl;

  factory _JsExecutionResult.fromJson(Map<String, dynamic> json) =
      _$JsExecutionResultImpl.fromJson;

  @override
  bool get success;
  @override
  String get result;
  @override
  String? get error;
  @override
  int get executionTimeMs;
  @override
  DateTime? get executedAt;

  /// Create a copy of JsExecutionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JsExecutionResultImplCopyWith<_$JsExecutionResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
