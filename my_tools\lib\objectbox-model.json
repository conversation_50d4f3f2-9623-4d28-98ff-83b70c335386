{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:553209797949817091", "lastPropertyId": "7:929447249616817679", "name": "JsCodeSnippetEntity", "properties": [{"id": "1:1306121217765314246", "name": "id", "type": 6, "flags": 1}, {"id": "2:4618741906803723760", "name": "name", "type": 9}, {"id": "3:309824728779864425", "name": "code", "type": 9}, {"id": "4:2796910781636817368", "name": "description", "type": 9}, {"id": "5:4934880519341885939", "name": "createdAt", "type": 10}, {"id": "6:8343496295349706753", "name": "updatedAt", "type": 10}, {"id": "7:929447249616817679", "name": "isEnabled", "type": 1}], "relations": []}], "lastEntityId": "1:553209797949817091", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}