import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rxdart/rxdart.dart';

import '../../data/services/aes_crypto_service_impl.dart';
import '../../domain/models/crypto_config.dart';
import '../../domain/models/crypto_result.dart';
import '../../domain/services/aes_crypto_service.dart';
import '../../../../../shared/utils/app_logger.dart';

/// AES加密服务Provider
final aesCryptoServiceProvider = Provider<AesCryptoService>((ref) {
  return AesCryptoServiceImpl();
});

/// 加密配置Provider
final cryptoConfigProvider = StateNotifierProvider<CryptoConfigNotifier, CryptoConfig>((ref) {
  return CryptoConfigNotifier();
});

/// 输入文本Provider
final inputTextProvider = StateProvider<String>((ref) => '');

/// 输出文本Provider
final outputTextProvider = StateProvider<String>((ref) => '');

/// 最后活跃的输入框Provider
final lastActiveFieldProvider = StateProvider<ActiveField>((ref) => ActiveField.input);

/// 处理结果Provider
final processResultProvider = StateProvider<CryptoResult?>((ref) => null);

/// 操作模式Provider
final operationModeProvider = StateProvider<OperationMode>((ref) => OperationMode.encrypt);

/// 活跃输入框枚举
enum ActiveField {
  input,
  output,
}

/// 操作模式枚举
enum OperationMode {
  encrypt('加密'),
  decrypt('解密');

  const OperationMode(this.displayName);
  final String displayName;
}

/// 加密配置状态管理
class CryptoConfigNotifier extends StateNotifier<CryptoConfig> {
  CryptoConfigNotifier() : super(const CryptoConfig());

  /// 更新密钥
  void updateKey(String key) {
    state = state.copyWith(key: key);
  }

  /// 更新IV
  void updateIV(String iv) {
    state = state.copyWith(iv: iv);
  }

  /// 更新加密模式
  void updateMode(AesMode mode) {
    state = state.copyWith(mode: mode);
  }

  /// 更新密钥长度
  void updateKeySize(AesKeySize keySize) {
    state = state.copyWith(keySize: keySize);
  }

  /// 更新填充方式
  void updatePadding(AesPadding padding) {
    state = state.copyWith(padding: padding);
  }

  /// 更新输入格式
  void updateInputFormat(EncodingFormat format) {
    state = state.copyWith(inputFormat: format);
  }

  /// 更新输出格式
  void updateOutputFormat(EncodingFormat format) {
    state = state.copyWith(outputFormat: format);
  }

  /// 重置配置
  void reset() {
    state = const CryptoConfig();
  }
}

/// 自动处理Provider
final autoProcessProvider = Provider<AutoProcessor>((ref) {
  return AutoProcessor(ref);
});

/// 自动处理器
class AutoProcessor {
  final Ref _ref;
  final BehaviorSubject<String> _inputSubject = BehaviorSubject<String>();
  final BehaviorSubject<String> _outputSubject = BehaviorSubject<String>();

  AutoProcessor(this._ref) {
    _setupAutoProcessing();
  }

  /// 设置自动处理
  void _setupAutoProcessing() {
    // 监听输入框变化
    _inputSubject
        .debounceTime(const Duration(milliseconds: 500))
        .distinct()
        .listen((input) {
      if (input.isNotEmpty) {
        _ref.read(lastActiveFieldProvider.notifier).state = ActiveField.input;
        _processInputToOutput(input);
      }
    });

    // 监听输出框变化
    _outputSubject
        .debounceTime(const Duration(milliseconds: 500))
        .distinct()
        .listen((output) {
      if (output.isNotEmpty) {
        _ref.read(lastActiveFieldProvider.notifier).state = ActiveField.output;
        _processOutputToInput(output);
      }
    });

    // 监听provider变化（用于移动端组件）
    _ref.listen<String>(inputTextProvider, (previous, next) {
      AppLogger.debug('输入文本变化', {
        'previous_length': previous?.length ?? 0,
        'next_length': next.length,
        'lastActive': _ref.read(lastActiveFieldProvider).name,
      });

      if (next.isNotEmpty && next != previous) {
        final lastActive = _ref.read(lastActiveFieldProvider);
        final mode = _ref.read(operationModeProvider);
        if (lastActive == ActiveField.input) {
          if (mode == OperationMode.encrypt) {
            AppLogger.info('触发自动加密处理');
            _processInputToOutput(next);
          } else {
            AppLogger.info('触发自动解密处理');
            _processInputToOutput(next); // 解密也是输入到输出
          }
        }
      }
    });

    // 监听输出文本变化
    _ref.listen<String>(outputTextProvider, (previous, next) {
      AppLogger.debug('输出文本变化', {
        'previous_length': previous?.length ?? 0,
        'next_length': next.length,
        'lastActive': _ref.read(lastActiveFieldProvider).name,
      });

      if (next.isNotEmpty && next != previous) {
        final lastActive = _ref.read(lastActiveFieldProvider);
        final mode = _ref.read(operationModeProvider);
        if (lastActive == ActiveField.output) {
          if (mode == OperationMode.encrypt) {
            AppLogger.info('触发自动解密处理（输出到输入）');
            _processOutputToInput(next);
          } else {
            AppLogger.info('触发自动加密处理（输出到输入）');
            _processOutputToInput(next); // 加密也是输出到输入
          }
        }
      }
    });

    // 监听配置变化，自动重新处理
    _ref.listen<CryptoConfig>(cryptoConfigProvider, (previous, next) {
      if (previous != null && previous != next) {
        AppLogger.debug('配置变化，触发重新处理');
        final inputText = _ref.read(inputTextProvider);
        final outputText = _ref.read(outputTextProvider);
        final lastActive = _ref.read(lastActiveFieldProvider);

        if (lastActive == ActiveField.input && inputText.isNotEmpty) {
          _processInputToOutput(inputText);
        } else if (lastActive == ActiveField.output && outputText.isNotEmpty) {
          _processOutputToInput(outputText);
        }
      }
    });

    // 监听操作模式变化，自动重新处理
    _ref.listen<OperationMode>(operationModeProvider, (previous, next) {
      if (previous != null && previous != next) {
        AppLogger.debug('操作模式变化，触发重新处理', {
          'previous': previous.displayName,
          'next': next.displayName,
        });

        final inputText = _ref.read(inputTextProvider);
        final outputText = _ref.read(outputTextProvider);
        final lastActive = _ref.read(lastActiveFieldProvider);

        // 根据最后活跃的字段和当前内容决定处理方向
        if (lastActive == ActiveField.input && inputText.isNotEmpty) {
          AppLogger.info('模式切换：重新处理输入内容');
          _processInputToOutput(inputText);
        } else if (lastActive == ActiveField.output && outputText.isNotEmpty) {
          AppLogger.info('模式切换：重新处理输出内容');
          _processOutputToInput(outputText);
        } else if (inputText.isNotEmpty) {
          // 如果没有明确的活跃字段，但有输入内容，则处理输入
          AppLogger.info('模式切换：默认处理输入内容');
          _ref.read(lastActiveFieldProvider.notifier).state = ActiveField.input;
          _processInputToOutput(inputText);
        }
      }
    });
  }

  /// 输入文本变化
  void onInputChanged(String input) {
    _inputSubject.add(input);
  }

  /// 输出文本变化
  void onOutputChanged(String output) {
    _outputSubject.add(output);
  }

  /// 处理输入到输出
  Future<void> _processInputToOutput(String input) async {
    final mode = _ref.read(operationModeProvider);
    final service = _ref.read(aesCryptoServiceProvider);
    final config = _ref.read(cryptoConfigProvider);

    AppLogger.info('开始处理输入到输出', {
      'input_length': input.length,
      'mode': mode.displayName,
    });

    final result = mode == OperationMode.encrypt
        ? await service.encrypt(input, config)
        : await service.decrypt(input, config);

    _ref.read(processResultProvider.notifier).state = result;

    if (result.isSuccess) {
      AppLogger.info('${mode.displayName}处理成功，更新输出');
      _ref.read(outputTextProvider.notifier).state = result.data;
    } else {
      AppLogger.error('${mode.displayName}处理失败', result.error);
    }
  }

  /// 处理输出到输入
  Future<void> _processOutputToInput(String output) async {
    final mode = _ref.read(operationModeProvider);
    final service = _ref.read(aesCryptoServiceProvider);
    final config = _ref.read(cryptoConfigProvider);

    AppLogger.info('开始处理输出到输入', {
      'output_length': output.length,
      'mode': mode.displayName,
    });

    // 输出到输入的逻辑相反：如果当前是加密模式，则对输出进行解密
    final result = mode == OperationMode.encrypt
        ? await service.decrypt(output, config)
        : await service.encrypt(output, config);

    _ref.read(processResultProvider.notifier).state = result;

    if (result.isSuccess) {
      AppLogger.info('${mode == OperationMode.encrypt ? "解密" : "加密"}处理成功，更新输入');
      _ref.read(inputTextProvider.notifier).state = result.data;
    } else {
      AppLogger.error('${mode == OperationMode.encrypt ? "解密" : "加密"}处理失败', result.error);
    }
  }

  /// 手动处理当前活跃字段
  Future<void> processCurrentField() async {
    final lastActive = _ref.read(lastActiveFieldProvider);
    
    if (lastActive == ActiveField.input) {
      final input = _ref.read(inputTextProvider);
      if (input.isNotEmpty) {
        await _processInputToOutput(input);
      }
    } else {
      final output = _ref.read(outputTextProvider);
      if (output.isNotEmpty) {
        await _processOutputToInput(output);
      }
    }
  }

  /// 清理资源
  void dispose() {
    _inputSubject.close();
    _outputSubject.close();
  }
}
