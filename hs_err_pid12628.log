#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:112), pid=12628, tid=50136
#
# JRE version: Java(TM) SE Runtime Environment (21.0.7+8) (build 21.0.7+8-LTS-245)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.7+8-LTS-245, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=true -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx10G -Xms2G -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\02f6bb0736726e084caf6170a8d996d1\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\02f6bb0736726e084caf6170a8d996d1\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-e58ca1cdc559b555988f07c3ffd266f0-sock

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Thu Jul 31 13:34:58 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 25.203306 seconds (0d 0h 0m 25s)

---------------  T H R E A D  ---------------

Current thread (0x000001dff40cdaf0):  JavaThread "Worker-3: Initialize workspace"        [_thread_in_vm, id=50136, stack(0x0000003123100000,0x0000003123200000) (1024K)]

Stack: [0x0000003123100000,0x0000003123200000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cae19]
V  [jvm.dll+0x855741]
V  [jvm.dll+0x857c6e]
V  [jvm.dll+0x858343]
V  [jvm.dll+0x27e0b6]
V  [jvm.dll+0x85135e]
V  [jvm.dll+0x66ea55]
V  [jvm.dll+0x1e410c]
V  [jvm.dll+0x1e3ede]
V  [jvm.dll+0x671383]
V  [jvm.dll+0x6711a2]
V  [jvm.dll+0x66f42e]
V  [jvm.dll+0x27af36]
V  [jvm.dll+0x7207e5]
V  [jvm.dll+0x72132f]
V  [jvm.dll+0x3c26b8]
V  [jvm.dll+0x3c1796]
V  [jvm.dll+0x3c1738]
V  [jvm.dll+0x5d4b09]
V  [jvm.dll+0x5d3a74]
V  [jvm.dll+0x3d6f25]
V  [jvm.dll+0x3d659d]
C  0x000001df8f838080

The last pc belongs to invokestatic (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  org.eclipse.jdt.ls.core.internal.handlers.JDTLanguageServer.syncCapabilitiesToSettings()V+34
j  org.eclipse.jdt.ls.core.internal.handlers.JDTLanguageServer$2.run(Lorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/core/runtime/IStatus;+127
j  org.eclipse.core.internal.jobs.Worker.run()V+32
v  ~StubRoutines::call_stub 0x000001df8f82100d
invokestatic  184 invokestatic  [0x000001df8f837fe0, 0x000001df8f8382a8]  712 bytes
[MachCode]
  0x000001df8f837fe0: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x000001df8f838000: 4424 0800 | 0000 00eb | 0150 4c89 | 6dc0 410f | b755 0148 | 8b4d d0c1 | e202 8b5c | d138 c1eb 
  0x000001df8f838020: 1081 e3ff | 0000 0081 | fbb8 0000 | 000f 84b4 | 0000 00bb | b800 0000 | e805 0000 | 00e9 9900 
  0x000001df8f838040: 0000 488b | d348 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 | 8987 9803 
  0x000001df8f838060: 0000 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 3065 | b449 fd7f | 0000 ffd0 
  0x000001df8f838080: 4883 c408 | e90c 0000 | 0048 b830 | 65b4 49fd | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 
  0x000001df8f8380a0: 0000 0000 | 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 | 7749 837f 
  0x000001df8f8380c0: 0800 0f84 | 0500 0000 | e933 8efe | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c341 | 0fb7 5501 
  0x000001df8f8380e0: 488b 4dd0 | c1e2 0248 | 8b5c d140 | 488b 5b08 | 488b 5b08 | 488b 5b18 | 80bb 2101 | 0000 040f 
  0x000001df8f838100: 840d 0000 | 004c 3bbb | 2801 0000 | 0f85 21ff | ffff 488b | 5cd1 408b | 54d1 50c1 | ea1c 49ba 
  0x000001df8f838120: e0da 3b4a | fd7f 0000 | 498b 14d2 | 5248 8b45 | d848 85c0 | 0f84 1200 | 0000 4883 | 4008 0148 
  0x000001df8f838140: 8358 0800 | 4883 c010 | 4889 45d8 | 488b 45d8 | 4885 c00f | 843d 0100 | 0080 78f0 | 0a0f 8533 
  0x000001df8f838160: 0100 0048 | 83c0 084c | 8b68 f841 | 83ed 0041 | 83fd 020f | 8c12 0100 | 004c 8b6b | 0845 0fb7 
  0x000001df8f838180: 6d2e 4c2b | 2841 83ed | 014e 8b6c | ec08 4d85 | ed75 0ef6 | 4008 0175 | 58f0 4883 | 4808 01eb 
  0x000001df8f8381a0: 5045 8b6d | 0849 ba00 | 0000 acdf | 0100 004d | 03ea 4d8b | d54c 3368 | 0849 f7c5 | fcff ffff 
  0x000001df8f8381c0: 742f 41f6 | c502 7529 | 4883 7808 | 0074 1e48 | 8378 0801 | 7417 4d8b | ea4c 3368 | 0849 f7c5 
  0x000001df8f8381e0: fcff ffff | 740b 4883 | 4808 02eb | 044c 8968 | 0848 83c0 | 104c 8b68 | e841 83ed | 0241 83fd 
  0x000001df8f838200: 020f 8c84 | 0000 004c | 8b6b 0845 | 0fb7 6d2e | 4c2b 2841 | 83ed 014e | 8b6c ec08 | 4d85 ed75 
  0x000001df8f838220: 0ef6 4008 | 0175 58f0 | 4883 4808 | 01eb 5045 | 8b6d 0849 | ba00 0000 | acdf 0100 | 004d 03ea 
  0x000001df8f838240: 4d8b d54c | 3368 0849 | f7c5 fcff | ffff 742f | 41f6 c502 | 7529 4883 | 7808 0074 | 1e48 8378 
  0x000001df8f838260: 0801 7417 | 4d8b ea4c | 3368 0849 | f7c5 fcff | ffff 740b | 4883 4808 | 02eb 044c | 8968 0848 
  0x000001df8f838280: 83c0 104c | 8b68 d841 | 83ed 0441 | c1e5 0349 | 03c5 4889 | 45d8 4c8d | 6c24 084c | 896d f0ff 
  0x000001df8f8382a0: 6350 660f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001dffe973dd0, length=65, elements={
0x000001dff8ef3000, 0x000001dfab5c7c30, 0x000001dfab5cb4a0, 0x000001dfab5cd820,
0x000001dfab5ce730, 0x000001dff8f94a60, 0x000001dff8f98d30, 0x000001dff8f9e7c0,
0x000001dff8f9fe60, 0x000001dff8f5da40, 0x000001dfef5e66d0, 0x000001dfefcc8b00,
0x000001dfefb78ed0, 0x000001dfefb75cd0, 0x000001dff4566660, 0x000001dfefc17080,
0x000001dfefc169e0, 0x000001dff40cb390, 0x000001dff40cba20, 0x000001dff40cc0b0,
0x000001dff40cc740, 0x000001dff40ccdd0, 0x000001dff40cd460, 0x000001dff40cdaf0,
0x000001dff40ca670, 0x000001dff40cad00, 0x000001dffd8a9d00, 0x000001dffd8a4e40,
0x000001dffd8aa390, 0x000001dffd8a8950, 0x000001dffd8a8fe0, 0x000001dffd8aaa20,
0x000001dffd8a9670, 0x000001dffd8a7c30, 0x000001dffd8ab0b0, 0x000001dffd8a82c0,
0x000001dffd8ab740, 0x000001dffd8a47b0, 0x000001dffd8a61f0, 0x000001dffd8abdd0,
0x000001dffd8a6880, 0x000001dffd8a54d0, 0x000001dffd8a6f10, 0x000001dffd8a5b60,
0x000001dffd8a75a0, 0x000001dffd1edb80, 0x000001dffd1f3760, 0x000001dffd1f30d0,
0x000001dffd1f2a40, 0x000001dffd1f0970, 0x000001dffd1eef30, 0x000001dffd1f1000,
0x000001dffd1ed4f0, 0x000001dffd1f1d20, 0x000001dffd1f23b0, 0x000001dffd1ece60,
0x000001dffd1f1690, 0x000001dffce58e10, 0x000001dffd1f3df0, 0x000001dffd1f4480,
0x000001dff48312f0, 0x000001dffce5a950, 0x000001dffce5b020, 0x000001dffce5b6f0,
0x000001dff4835490
}

Java Threads: ( => current thread )
  0x000001dff8ef3000 JavaThread "main"                              [_thread_blocked, id=51900, stack(0x0000003120500000,0x0000003120600000) (1024K)]
  0x000001dfab5c7c30 JavaThread "Reference Handler"          daemon [_thread_blocked, id=50496, stack(0x0000003120900000,0x0000003120a00000) (1024K)]
  0x000001dfab5cb4a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=21356, stack(0x0000003120a00000,0x0000003120b00000) (1024K)]
  0x000001dfab5cd820 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=63344, stack(0x0000003120b00000,0x0000003120c00000) (1024K)]
  0x000001dfab5ce730 JavaThread "Attach Listener"            daemon [_thread_blocked, id=35288, stack(0x0000003120c00000,0x0000003120d00000) (1024K)]
  0x000001dff8f94a60 JavaThread "Service Thread"             daemon [_thread_blocked, id=27940, stack(0x0000003120d00000,0x0000003120e00000) (1024K)]
  0x000001dff8f98d30 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=28564, stack(0x0000003120e00000,0x0000003120f00000) (1024K)]
  0x000001dff8f9e7c0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=30156, stack(0x0000003120f00000,0x0000003121000000) (1024K)]
  0x000001dff8f9fe60 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=52356, stack(0x0000003121000000,0x0000003121100000) (1024K)]
  0x000001dff8f5da40 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=45376, stack(0x0000003121100000,0x0000003121200000) (1024K)]
  0x000001dfef5e66d0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=22280, stack(0x0000003121500000,0x0000003121600000) (1024K)]
  0x000001dfefcc8b00 JavaThread "Active Thread: Equinox Container: b37a52ad-f31b-4001-860f-3225116d09b6"        [_thread_blocked, id=58724, stack(0x0000003121800000,0x0000003121900000) (1024K)]
  0x000001dfefb78ed0 JavaThread "Refresh Thread: Equinox Container: b37a52ad-f31b-4001-860f-3225116d09b6" daemon [_thread_blocked, id=31204, stack(0x0000003121900000,0x0000003121a00000) (1024K)]
  0x000001dfefb75cd0 JavaThread "Framework Event Dispatcher: Equinox Container: b37a52ad-f31b-4001-860f-3225116d09b6" daemon [_thread_blocked, id=35632, stack(0x0000003121a00000,0x0000003121b00000) (1024K)]
  0x000001dff4566660 JavaThread "Start Level: Equinox Container: b37a52ad-f31b-4001-860f-3225116d09b6" daemon [_thread_blocked, id=35684, stack(0x0000003121b00000,0x0000003121c00000) (1024K)]
  0x000001dfefc17080 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=61768, stack(0x0000003121700000,0x0000003121800000) (1024K)]
  0x000001dfefc169e0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=11208, stack(0x0000003121600000,0x0000003121700000) (1024K)]
  0x000001dff40cb390 JavaThread "Worker-JM"                         [_thread_blocked, id=3852, stack(0x0000003121e00000,0x0000003121f00000) (1024K)]
  0x000001dff40cba20 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=58804, stack(0x0000003121d00000,0x0000003121e00000) (1024K)]
  0x000001dff40cc0b0 JavaThread "Worker-0"                          [_thread_blocked, id=39136, stack(0x0000003121f00000,0x0000003122000000) (1024K)]
  0x000001dff40cc740 JavaThread "Worker-1: Validating Gradle wrapper checksum..."        [_thread_blocked, id=9812, stack(0x0000003122000000,0x0000003122100000) (1024K)]
  0x000001dff40ccdd0 JavaThread "Worker-2: Download Gradle Wrapper checksums"        [_thread_in_vm, id=56360, stack(0x0000003122f00000,0x0000003123000000) (1024K)]
  0x000001dff40cd460 JavaThread "Java indexing"              daemon [_thread_blocked, id=59092, stack(0x0000003123000000,0x0000003123100000) (1024K)]
=>0x000001dff40cdaf0 JavaThread "Worker-3: Initialize workspace"        [_thread_in_vm, id=50136, stack(0x0000003123100000,0x0000003123200000) (1024K)]
  0x000001dff40ca670 JavaThread "Worker-4"                          [_thread_blocked, id=30176, stack(0x0000003123400000,0x0000003123500000) (1024K)]
  0x000001dff40cad00 JavaThread "Worker-5: Synchronize Gradle projects with workspace"        [_thread_blocked, id=62236, stack(0x0000003123500000,0x0000003123600000) (1024K)]
  0x000001dffd8a9d00 JavaThread "Thread-2"                   daemon [_thread_in_native, id=57512, stack(0x0000003123600000,0x0000003123700000) (1024K)]
  0x000001dffd8a4e40 JavaThread "Thread-3"                   daemon [_thread_in_native, id=37980, stack(0x0000003123900000,0x0000003123a00000) (1024K)]
  0x000001dffd8aa390 JavaThread "Thread-4"                   daemon [_thread_in_native, id=32728, stack(0x0000003123a00000,0x0000003123b00000) (1024K)]
  0x000001dffd8a8950 JavaThread "Thread-5"                   daemon [_thread_in_native, id=27920, stack(0x0000003123b00000,0x0000003123c00000) (1024K)]
  0x000001dffd8a8fe0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=24712, stack(0x0000003123c00000,0x0000003123d00000) (1024K)]
  0x000001dffd8aaa20 JavaThread "Thread-7"                   daemon [_thread_in_native, id=56264, stack(0x0000003123d00000,0x0000003123e00000) (1024K)]
  0x000001dffd8a9670 JavaThread "Thread-8"                   daemon [_thread_in_native, id=24412, stack(0x0000003123e00000,0x0000003123f00000) (1024K)]
  0x000001dffd8a7c30 JavaThread "Thread-9"                   daemon [_thread_in_native, id=46408, stack(0x0000003123f00000,0x0000003124000000) (1024K)]
  0x000001dffd8ab0b0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=44124, stack(0x0000003124000000,0x0000003124100000) (1024K)]
  0x000001dffd8a82c0 JavaThread "Thread-11"                  daemon [_thread_in_native, id=7320, stack(0x0000003124100000,0x0000003124200000) (1024K)]
  0x000001dffd8ab740 JavaThread "Thread-12"                  daemon [_thread_in_native, id=63944, stack(0x0000003124200000,0x0000003124300000) (1024K)]
  0x000001dffd8a47b0 JavaThread "Thread-13"                  daemon [_thread_in_native, id=45708, stack(0x0000003124300000,0x0000003124400000) (1024K)]
  0x000001dffd8a61f0 JavaThread "Thread-14"                  daemon [_thread_in_native, id=64940, stack(0x0000003124400000,0x0000003124500000) (1024K)]
  0x000001dffd8abdd0 JavaThread "Thread-15"                  daemon [_thread_in_native, id=65180, stack(0x0000003124500000,0x0000003124600000) (1024K)]
  0x000001dffd8a6880 JavaThread "Thread-16"                  daemon [_thread_in_native, id=47848, stack(0x0000003124600000,0x0000003124700000) (1024K)]
  0x000001dffd8a54d0 JavaThread "Thread-17"                  daemon [_thread_in_native, id=59908, stack(0x0000003124700000,0x0000003124800000) (1024K)]
  0x000001dffd8a6f10 JavaThread "Thread-18"                  daemon [_thread_in_native, id=29408, stack(0x0000003124800000,0x0000003124900000) (1024K)]
  0x000001dffd8a5b60 JavaThread "Timer-0"                           [_thread_blocked, id=63180, stack(0x0000003124900000,0x0000003124a00000) (1024K)]
  0x000001dffd8a75a0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=52640, stack(0x0000003124a00000,0x0000003124b00000) (1024K)]
  0x000001dffd1edb80 JavaThread "Timer-1"                           [_thread_blocked, id=10560, stack(0x0000003124b00000,0x0000003124c00000) (1024K)]
  0x000001dffd1f3760 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=65028, stack(0x0000003124d00000,0x0000003124e00000) (1024K)]
  0x000001dffd1f30d0 JavaThread "pool-1-thread-1"                   [_thread_in_vm, id=49280, stack(0x0000003124e00000,0x0000003124f00000) (1024K)]
  0x000001dffd1f2a40 JavaThread "Timer-2"                           [_thread_blocked, id=66592, stack(0x0000003121c00000,0x0000003121d00000) (1024K)]
  0x000001dffd1f0970 JavaThread "Timer-3"                           [_thread_blocked, id=33412, stack(0x0000003122100000,0x0000003122200000) (1024K)]
  0x000001dffd1eef30 JavaThread "Timer-4"                           [_thread_blocked, id=55092, stack(0x0000003122e00000,0x0000003122f00000) (1024K)]
  0x000001dffd1f1000 JavaThread "Timer-5"                           [_thread_blocked, id=57008, stack(0x0000003123200000,0x0000003123300000) (1024K)]
  0x000001dffd1ed4f0 JavaThread "Timer-6"                           [_thread_blocked, id=57580, stack(0x0000003123300000,0x0000003123400000) (1024K)]
  0x000001dffd1f1d20 JavaThread "Exec process"                      [_thread_in_vm, id=1344, stack(0x0000003121300000,0x0000003121400000) (1024K)]
  0x000001dffd1f23b0 JavaThread "Exec process Thread 2"             [_thread_blocked, id=58456, stack(0x0000003121400000,0x0000003121500000) (1024K)]
  0x000001dffd1ece60 JavaThread "Exec process Thread 3"             [_thread_blocked, id=53664, stack(0x0000003124f00000,0x0000003125000000) (1024K)]
  0x000001dffd1f1690 JavaThread "Timer-7"                           [_thread_blocked, id=32296, stack(0x0000003121200000,0x0000003121300000) (1024K)]
  0x000001dffce58e10 JavaThread "C1 CompilerThread1"         daemon [_thread_in_vm, id=19020, stack(0x0000003125000000,0x0000003125100000) (1024K)]
  0x000001dffd1f3df0 JavaThread "Timer-8"                           [_thread_blocked, id=33636, stack(0x0000003124c00000,0x0000003124d00000) (1024K)]
  0x000001dffd1f4480 JavaThread "Timer-9"                           [_thread_blocked, id=28864, stack(0x0000003125100000,0x0000003125200000) (1024K)]
  0x000001dff48312f0 JavaThread "Connection worker"                 [_thread_blocked, id=37196, stack(0x0000003125200000,0x0000003125300000) (1024K)]
  0x000001dffce5a950 JavaThread "C1 CompilerThread2"         daemon [_thread_in_vm, id=37928, stack(0x0000003125300000,0x0000003125400000) (1024K)]
  0x000001dffce5b020 JavaThread "C1 CompilerThread3"         daemon [_thread_in_vm, id=33684, stack(0x0000003125400000,0x0000003125500000) (1024K)]
  0x000001dffce5b6f0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=16036, stack(0x0000003125500000,0x0000003125600000) (1024K)]
  0x000001dff4835490 JavaThread "pool-4-thread-1"                   [_thread_blocked, id=18684, stack(0x0000003125600000,0x0000003125700000) (1024K)]
Total: 65

Other Threads:
  0x000001dfedb24af0 VMThread "VM Thread"                           [id=3460, stack(0x0000003120800000,0x0000003120900000) (1024K)]
  0x000001dfab4aca00 WatcherThread "VM Periodic Task Thread"        [id=46132, stack(0x0000003120700000,0x0000003120800000) (1024K)]
  0x000001dff8f10350 WorkerThread "GC Thread#0"                     [id=61044, stack(0x0000003120600000,0x0000003120700000) (1024K)]
  0x000001dff4693490 WorkerThread "GC Thread#1"                     [id=65808, stack(0x0000003122200000,0x0000003122300000) (1024K)]
  0x000001dfefd2e170 WorkerThread "GC Thread#2"                     [id=60768, stack(0x0000003122300000,0x0000003122400000) (1024K)]
  0x000001dfefd2e510 WorkerThread "GC Thread#3"                     [id=59312, stack(0x0000003122400000,0x0000003122500000) (1024K)]
  0x000001dff4b8ad80 WorkerThread "GC Thread#4"                     [id=11860, stack(0x0000003122500000,0x0000003122600000) (1024K)]
  0x000001dff4b8b120 WorkerThread "GC Thread#5"                     [id=66176, stack(0x0000003122600000,0x0000003122700000) (1024K)]
  0x000001dff410f6b0 WorkerThread "GC Thread#6"                     [id=9116, stack(0x0000003122700000,0x0000003122800000) (1024K)]
  0x000001dff410e830 WorkerThread "GC Thread#7"                     [id=7160, stack(0x0000003122800000,0x0000003122900000) (1024K)]
  0x000001dff410ef70 WorkerThread "GC Thread#8"                     [id=63184, stack(0x0000003122900000,0x0000003122a00000) (1024K)]
  0x000001dff410e490 WorkerThread "GC Thread#9"                     [id=35084, stack(0x0000003122a00000,0x0000003122b00000) (1024K)]
  0x000001dff410f310 WorkerThread "GC Thread#10"                    [id=45516, stack(0x0000003122b00000,0x0000003122c00000) (1024K)]
  0x000001dff410dd50 WorkerThread "GC Thread#11"                    [id=60428, stack(0x0000003122c00000,0x0000003122d00000) (1024K)]
  0x000001dff410ebd0 WorkerThread "GC Thread#12"                    [id=15900, stack(0x0000003122d00000,0x0000003122e00000) (1024K)]
Total: 15

Threads with active compile tasks:
C1 CompilerThread0    25276 7837       3       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$AdaptingMethodInvoker::invoke (48 bytes)
C1 CompilerThread1    25276 7841       3       org.gradle.internal.buildoption.EnabledOnlyBooleanBuildOption::applyFromProperty (26 bytes)
C1 CompilerThread2    25276 7840       3       org.gradle.cli.CommandLineParser::allowOneOf (92 bytes)
C1 CompilerThread3    25276 7839       3       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ChainedMethodInvoker::invoke (39 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd4a3abe50] Metaspace_lock - owner thread: 0x000001dff40cdaf0

Heap address: 0x0000000580000000, size: 10240 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001dfac000000-0x000001dfacc90000-0x000001dfacc90000), size 13172736, SharedBaseAddress: 0x000001dfac000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001dfad000000-0x000001dfed000000, reserved size: 1073741824
Narrow klass base: 0x000001dfac000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 16 total, 16 available
 Memory: 16228M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 2G
 Heap Initial Capacity: 2G
 Heap Max Capacity: 10G
 Pre-touch: Disabled
 Parallel Workers: 13

Heap:
 PSYoungGen      total 611840K, used 254582K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 48% used [0x000000072ab00000,0x000000073a39d8d0,0x000000074ab80000)
  from space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 38265K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 2% used [0x0000000580000000,0x000000058255e5b0,0x00000005d5580000)
 Metaspace       used 63589K, committed 64896K, reserved 1114112K
  class space    used 7758K, committed 8384K, reserved 1048576K

Card table byte_map: [0x000001dff8fb0000,0x000001dffa3c0000] _byte_map_base: 0x000001dff63b0000

Marking Bits: (ParMarkBitMap*) 0x00007ffd4a3b2da0
 Begin Bits: [0x000001df973d0000, 0x000001dfa13d0000)
 End Bits:   [0x000001dfa13d0000, 0x000001dfab3d0000)

Polling page: 0x000001dff6ce0000

Metaspace:

Usage:
  Non-class:     54.52 MB used.
      Class:      7.58 MB used.
       Both:     62.10 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      55.19 MB ( 86%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       8.19 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      63.38 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  7.92 MB
       Class:  7.86 MB
        Both:  15.78 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1076.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1013.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 23.
num_chunks_taken_from_freelist: 3893.
num_chunk_merges: 15.
num_chunk_splits: 2450.
num_chunks_enlarged: 1454.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=4598Kb max_used=4598Kb free=114569Kb
 bounds [0x000001df8ff70000, 0x000001df903f0000, 0x000001df973d0000]
CodeHeap 'profiled nmethods': size=119104Kb used=14363Kb max_used=14363Kb free=104740Kb
 bounds [0x000001df883d0000, 0x000001df891e0000, 0x000001df8f820000]
CodeHeap 'non-nmethods': size=7488Kb used=3074Kb max_used=3177Kb free=4413Kb
 bounds [0x000001df8f820000, 0x000001df8fb60000, 0x000001df8ff70000]
 total_blobs=7702 nmethods=7011 adapters=594
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 25.035 Thread 0x000001dffce5a950 nmethod 7811 0x000001df891d5a10 code [0x000001df891d5d20, 0x000001df891d6ae0]
Event: 25.035 Thread 0x000001dff8f9fe60 nmethod 7820 0x000001df903ea190 code [0x000001df903ea320, 0x000001df903ea3f0]
Event: 25.036 Thread 0x000001dffce5b020 nmethod 7833 0x000001df903ea490 code [0x000001df903ea620, 0x000001df903ea6f0]
Event: 25.036 Thread 0x000001dff8f9fe60 7835       3       sun.reflect.misc.ReflectUtil::checkPackageAccess (14 bytes)
Event: 25.036 Thread 0x000001dff8f9fe60 nmethod 7835 0x000001df891d6f10 code [0x000001df891d70c0, 0x000001df891d7290]
Event: 25.036 Thread 0x000001dffce58e10 7836       3       org.gradle.process.internal.JvmOptions::isDebugArg (36 bytes)
Event: 25.036 Thread 0x000001dffce58e10 nmethod 7836 0x000001df891d7390 code [0x000001df891d7580, 0x000001df891d78c8]
Event: 25.037 Thread 0x000001dff8f9fe60 7837       3       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$AdaptingMethodInvoker::invoke (48 bytes)
Event: 25.039 Thread 0x000001dffce5b020 7839       3       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ChainedMethodInvoker::invoke (39 bytes)
Event: 25.040 Thread 0x000001dff8f9e7c0 nmethod 7802 0x000001df903ea790 code [0x000001df903ea980, 0x000001df903eaff8]
Event: 25.040 Thread 0x000001dff8f9e7c0 7838       4       java.util.HashMap::putVal (300 bytes)
Event: 25.040 Thread 0x000001dffce5b6f0 7818 %     4       java.util.Arrays::fill @ 5 (21 bytes)
Event: 25.042 Thread 0x000001dffce5b6f0 nmethod 7818% 0x000001df903eb410 code [0x000001df903eb5a0, 0x000001df903eb780]
Event: 25.042 Thread 0x000001dffce5b6f0 7825       4       org.eclipse.core.internal.preferences.ImmutableMap$ArrayMap::get (54 bytes)
Event: 25.042 Thread 0x000001dffce5a950 7840       3       org.gradle.cli.CommandLineParser::allowOneOf (92 bytes)
Event: 25.043 Thread 0x000001dffce58e10 7841       3       org.gradle.internal.buildoption.EnabledOnlyBooleanBuildOption::applyFromProperty (26 bytes)
Event: 25.045 Thread 0x000001dffce5b6f0 nmethod 7825 0x000001df903eb890 code [0x000001df903eba40, 0x000001df903ebe38]
Event: 25.045 Thread 0x000001dffce5b6f0 7824       4       java.util.concurrent.locks.ReentrantReadWriteLock$ReadLock::unlock (10 bytes)
Event: 25.047 Thread 0x000001dff8f9e7c0 nmethod 7838 0x000001df903ec090 code [0x000001df903ec2c0, 0x000001df903ecba8]
Event: 25.047 Thread 0x000001dffce5b6f0 nmethod 7824 0x000001df903ed210 code [0x000001df903ed400, 0x000001df903ed7a8]

GC Heap History (12 events):
Event: 9.164 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 611840K, used 336041K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 64% used [0x000000072ab00000,0x000000073f32a658,0x000000074ab80000)
  from space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 0K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x0000000580000000,0x00000005d5580000)
 Metaspace       used 20785K, committed 21504K, reserved 1114112K
  class space    used 2081K, committed 2368K, reserved 1048576K
}
Event: 9.205 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 611840K, used 12862K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 14% used [0x000000074ab80000,0x000000074b80f880,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 112K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x000000058001c010,0x00000005d5580000)
 Metaspace       used 20785K, committed 21504K, reserved 1114112K
  class space    used 2081K, committed 2368K, reserved 1048576K
}
Event: 9.205 GC heap before
{Heap before GC invocations=2 (full 1):
 PSYoungGen      total 611840K, used 12862K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 14% used [0x000000074ab80000,0x000000074b80f880,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 112K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x000000058001c010,0x00000005d5580000)
 Metaspace       used 20785K, committed 21504K, reserved 1114112K
  class space    used 2081K, committed 2368K, reserved 1048576K
}
Event: 9.293 GC heap after
{Heap after GC invocations=2 (full 1):
 PSYoungGen      total 611840K, used 0K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 12656K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x0000000580c5c330,0x00000005d5580000)
 Metaspace       used 20773K, committed 21504K, reserved 1114112K
  class space    used 2078K, committed 2368K, reserved 1048576K
}
Event: 11.883 GC heap before
{Heap before GC invocations=3 (full 1):
 PSYoungGen      total 611840K, used 196834K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 37% used [0x000000072ab00000,0x0000000736b38b78,0x000000074ab80000)
  from space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 12656K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x0000000580c5c330,0x00000005d5580000)
 Metaspace       used 34887K, committed 35840K, reserved 1114112K
  class space    used 3466K, committed 3904K, reserved 1048576K
}
Event: 11.887 GC heap after
{Heap after GC invocations=3 (full 1):
 PSYoungGen      total 611840K, used 5293K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 6% used [0x0000000750080000,0x00000007505ab7d0,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 12664K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x0000000580c5e330,0x00000005d5580000)
 Metaspace       used 34887K, committed 35840K, reserved 1114112K
  class space    used 3466K, committed 3904K, reserved 1048576K
}
Event: 11.887 GC heap before
{Heap before GC invocations=4 (full 2):
 PSYoungGen      total 611840K, used 5293K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 6% used [0x0000000750080000,0x00000007505ab7d0,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 12664K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 0% used [0x0000000580000000,0x0000000580c5e330,0x00000005d5580000)
 Metaspace       used 34887K, committed 35840K, reserved 1114112K
  class space    used 3466K, committed 3904K, reserved 1048576K
}
Event: 11.937 GC heap after
{Heap after GC invocations=4 (full 2):
 PSYoungGen      total 611840K, used 0K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 17278K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 1% used [0x0000000580000000,0x00000005810df8a0,0x00000005d5580000)
 Metaspace       used 34887K, committed 35840K, reserved 1114112K
  class space    used 3466K, committed 3904K, reserved 1048576K
}
Event: 18.151 GC heap before
{Heap before GC invocations=5 (full 2):
 PSYoungGen      total 611840K, used 417801K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 79% used [0x000000072ab00000,0x00000007443027c0,0x000000074ab80000)
  from space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
  to   space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
 ParOldGen       total 1398272K, used 17278K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 1% used [0x0000000580000000,0x00000005810df8a0,0x00000005d5580000)
 Metaspace       used 58485K, committed 59776K, reserved 1114112K
  class space    used 7064K, committed 7616K, reserved 1048576K
}
Event: 18.168 GC heap after
{Heap after GC invocations=5 (full 2):
 PSYoungGen      total 611840K, used 22218K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 25% used [0x000000074ab80000,0x000000074c132a50,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 17286K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 1% used [0x0000000580000000,0x00000005810e18a0,0x00000005d5580000)
 Metaspace       used 58485K, committed 59776K, reserved 1114112K
  class space    used 7064K, committed 7616K, reserved 1048576K
}
Event: 18.168 GC heap before
{Heap before GC invocations=6 (full 3):
 PSYoungGen      total 611840K, used 22218K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 25% used [0x000000074ab80000,0x000000074c132a50,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 17286K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 1% used [0x0000000580000000,0x00000005810e18a0,0x00000005d5580000)
 Metaspace       used 58485K, committed 59776K, reserved 1114112K
  class space    used 7064K, committed 7616K, reserved 1048576K
}
Event: 18.240 GC heap after
{Heap after GC invocations=6 (full 3):
 PSYoungGen      total 611840K, used 0K [0x000000072ab00000, 0x0000000755580000, 0x0000000800000000)
  eden space 524800K, 0% used [0x000000072ab00000,0x000000072ab00000,0x000000074ab80000)
  from space 87040K, 0% used [0x000000074ab80000,0x000000074ab80000,0x0000000750080000)
  to   space 87040K, 0% used [0x0000000750080000,0x0000000750080000,0x0000000755580000)
 ParOldGen       total 1398272K, used 38265K [0x0000000580000000, 0x00000005d5580000, 0x000000072ab00000)
  object space 1398272K, 2% used [0x0000000580000000,0x000000058255e5b0,0x00000005d5580000)
 Metaspace       used 58485K, committed 59776K, reserved 1114112K
  class space    used 7064K, committed 7616K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.021 Loaded shared library D:\Dev\Env\jdk21\bin\java.dll
Event: 0.147 Loaded shared library D:\Dev\Env\jdk21\bin\jsvml.dll
Event: 0.290 Loaded shared library D:\Dev\Env\jdk21\bin\zip.dll
Event: 0.308 Loaded shared library D:\Dev\Env\jdk21\bin\instrument.dll
Event: 0.320 Loaded shared library D:\Dev\Env\jdk21\bin\net.dll
Event: 0.338 Loaded shared library D:\Dev\Env\jdk21\bin\nio.dll
Event: 0.346 Loaded shared library D:\Dev\Env\jdk21\bin\zip.dll
Event: 0.408 Loaded shared library D:\Dev\Env\jdk21\bin\jimage.dll
Event: 0.960 Loaded shared library D:\Dev\Env\jdk21\bin\verify.dll
Event: 4.050 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 7.486 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna8865734636501558395.dll
Event: 13.498 Loaded shared library D:\Dev\Env\repo\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 16.542 Loaded shared library D:\Dev\Env\jdk21\bin\management.dll
Event: 16.559 Loaded shared library D:\Dev\Env\jdk21\bin\management_ext.dll
Event: 21.330 Loaded shared library D:\Dev\Env\jdk21\bin\extnet.dll

Deoptimization events (20 events):
Event: 24.422 Thread 0x000001dff40ca670 Uncommon trap: trap_request=0xfffffff4 fr.pc=0x000001df90289050 relative=0x0000000000000510
Event: 24.422 Thread 0x000001dff40ca670 Uncommon trap: reason=null_check action=make_not_entrant pc=0x000001df90289050 method=java.io.File.equals(Ljava/lang/Object;)Z @ 1 c2
Event: 24.422 Thread 0x000001dff40ca670 DEOPT PACKING pc=0x000001df90289050 sp=0x00000031234feaa0
Event: 24.422 Thread 0x000001dff40ca670 DEOPT UNPACKING pc=0x000001df8f873aa2 sp=0x00000031234fea58 mode 2
Event: 24.938 Thread 0x000001dff40ca670 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001df9039db08 relative=0x0000000000002ce8
Event: 24.938 Thread 0x000001dff40ca670 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001df9039db08 method=java.net.URL.<init>(Ljava/net/URL;Ljava/lang/String;Ljava/net/URLStreamHandler;)V @ 391 c2
Event: 24.938 Thread 0x000001dff40ca670 DEOPT PACKING pc=0x000001df9039db08 sp=0x00000031234fee40
Event: 24.938 Thread 0x000001dff40ca670 DEOPT UNPACKING pc=0x000001df8f873aa2 sp=0x00000031234fee00 mode 2
Event: 24.972 Thread 0x000001dff40cad00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001df9002fd74 relative=0x0000000000000054
Event: 24.972 Thread 0x000001dff40cad00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001df9002fd74 method=sun.nio.fs.WindowsPathParser.isSlash(C)Z @ 9 c2
Event: 24.972 Thread 0x000001dff40cad00 DEOPT PACKING pc=0x000001df9002fd74 sp=0x00000031235fdfb0
Event: 24.972 Thread 0x000001dff40cad00 DEOPT UNPACKING pc=0x000001df8f873aa2 sp=0x00000031235fdf38 mode 2
Event: 24.975 Thread 0x000001dffd1f30d0 Uncommon trap: trap_request=0xffffff76 fr.pc=0x000001df902fd988 relative=0x00000000000001e8
Event: 24.975 Thread 0x000001dffd1f30d0 Uncommon trap: reason=predicate action=maybe_recompile pc=0x000001df902fd988 method=java.util.LinkedHashMap.keysToArray([Ljava/lang/Object;Z)[Ljava/lang/Object; @ 53 c2
Event: 24.976 Thread 0x000001dffd1f30d0 DEOPT PACKING pc=0x000001df902fd988 sp=0x0000003124efe1b0
Event: 24.976 Thread 0x000001dffd1f30d0 DEOPT UNPACKING pc=0x000001df8f873aa2 sp=0x0000003124efe140 mode 2
Event: 25.053 Thread 0x000001dff48312f0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001df90304054 relative=0x0000000000000334
Event: 25.053 Thread 0x000001dff48312f0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001df90304054 method=java.util.Collections$SetFromMap.add(Ljava/lang/Object;)Z @ 8 c2
Event: 25.053 Thread 0x000001dff48312f0 DEOPT PACKING pc=0x000001df90304054 sp=0x00000031252fe290
Event: 25.053 Thread 0x000001dff48312f0 DEOPT UNPACKING pc=0x000001df8f873aa2 sp=0x00000031252fe260 mode 2

Classes loaded (20 events):
Event: 24.861 Loading class sun/security/ssl/SSLContextImpl$CustomizedTLSContext done
Event: 24.861 Loading class sun/security/ssl/SSLContextImpl$DefaultSSLContext done
Event: 24.861 Loading class sun/security/ssl/SSLLogger
Event: 24.873 Loading class sun/nio/cs/Surrogate done
Event: 24.906 Loading class sun/security/ssl/SSLLogger done
Event: 24.906 Loading class sun/security/ssl/ProtocolVersion
Event: 24.908 Loading class sun/nio/ch/ChannelOutputStream
Event: 24.908 Loading class sun/nio/ch/ChannelOutputStream done
Event: 24.908 Loading class sun/nio/fs/WindowsFileCopy
Event: 24.929 Loading class sun/nio/fs/WindowsFileCopy done
Event: 24.940 Loading class java/util/HashMap$EntrySpliterator
Event: 24.949 Loading class sun/security/ssl/ProtocolVersion done
Event: 24.949 Loading class sun/security/ssl/SSLAlgorithmConstraints
Event: 24.970 Loading class java/util/HashMap$EntrySpliterator done
Event: 25.025 Loading class sun/security/ssl/SSLAlgorithmConstraints done
Event: 25.025 Loading class sun/security/ssl/SSLAlgorithmDecomposer
Event: 25.055 Loading class sun/security/ssl/SSLAlgorithmDecomposer done
Event: 25.055 Loading class sun/security/util/DisabledAlgorithmConstraints$jdkCAConstraint
Event: 25.071 Loading class sun/security/util/DisabledAlgorithmConstraints$jdkCAConstraint done
Event: 25.071 Loading class sun/security/util/DisabledAlgorithmConstraints$UsageConstraint

Classes unloaded (7 events):
Event: 9.210 Thread 0x000001dfedb24af0 Unloading class 0x000001dfad1b3400 'java/lang/invoke/LambdaForm$MH+0x000001dfad1b3400'
Event: 9.210 Thread 0x000001dfedb24af0 Unloading class 0x000001dfad1b3000 'java/lang/invoke/LambdaForm$MH+0x000001dfad1b3000'
Event: 9.210 Thread 0x000001dfedb24af0 Unloading class 0x000001dfad1b2c00 'java/lang/invoke/LambdaForm$MH+0x000001dfad1b2c00'
Event: 9.210 Thread 0x000001dfedb24af0 Unloading class 0x000001dfad1b2800 'java/lang/invoke/LambdaForm$MH+0x000001dfad1b2800'
Event: 9.210 Thread 0x000001dfedb24af0 Unloading class 0x000001dfad1b2400 'java/lang/invoke/LambdaForm$BMH+0x000001dfad1b2400'
Event: 9.210 Thread 0x000001dfedb24af0 Unloading class 0x000001dfad1b2000 'java/lang/invoke/LambdaForm$DMH+0x000001dfad1b2000'
Event: 9.210 Thread 0x000001dfedb24af0 Unloading class 0x000001dfad1b1000 'java/lang/invoke/LambdaForm$DMH+0x000001dfad1b1000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 24.459 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739b9af50}> (0x0000000739b9af50) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.479 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739b9b768}> (0x0000000739b9b768) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.479 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739b9bca8}> (0x0000000739b9bca8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.604 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739bb2488}> (0x0000000739bb2488) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.605 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739bb29c8}> (0x0000000739bb29c8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.606 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739bbc350}> (0x0000000739bbc350) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.606 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739bbc890}> (0x0000000739bbc890) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.647 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739c9f2e8}> (0x0000000739c9f2e8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.668 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739c9f828}> (0x0000000739c9f828) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.685 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739ca0040}> (0x0000000739ca0040) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.685 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739ca0580}> (0x0000000739ca0580) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.685 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739ca0e18}> (0x0000000739ca0e18) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.685 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739ca1358}> (0x0000000739ca1358) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.726 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739d8dbd8}> (0x0000000739d8dbd8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.726 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739d8e118}> (0x0000000739d8e118) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.753 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739da21f8}> (0x0000000739da21f8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.932 Thread 0x000001dff40ca670 Exception <a 'sun/nio/fs/WindowsException'{0x0000000739dd0e40}> (0x0000000739dd0e40) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 24.938 Thread 0x000001dff40ca670 Exception <a 'java/lang/ClassNotFoundException'{0x0000000739e08ec8}: sun/net/www/protocol/d/Handler> (0x0000000739e08ec8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 25.031 Thread 0x000001dffd1f30d0 Exception <a 'sun/nio/fs/WindowsException'{0x000000073a1c08b0}> (0x000000073a1c08b0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 25.058 Thread 0x000001dffd1f1d20 Exception <a 'java/io/IOException'{0x0000000738852668}: CreateProcess error=1450, 系统资源不足，无法完成请求的服务。> (0x0000000738852668) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 539]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 18.151 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 18.240 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 18.347 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 18.347 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 19.361 Executing VM operation: Cleanup
Event: 19.362 Executing VM operation: Cleanup done
Event: 20.367 Executing VM operation: Cleanup
Event: 20.367 Executing VM operation: Cleanup done
Event: 21.368 Executing VM operation: Cleanup
Event: 21.384 Executing VM operation: Cleanup done
Event: 22.388 Executing VM operation: Cleanup
Event: 22.388 Executing VM operation: Cleanup done
Event: 23.389 Executing VM operation: Cleanup
Event: 23.389 Executing VM operation: Cleanup done
Event: 23.953 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 23.953 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 24.650 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 24.650 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 24.650 Executing VM operation: Cleanup
Event: 24.650 Executing VM operation: Cleanup done

Events (20 events):
Event: 23.932 Thread 0x000001dffd1f4480 Thread exited: 0x000001dffd1f4480
Event: 23.940 Thread 0x000001dffd1f3df0 Thread exited: 0x000001dffd1f3df0
Event: 23.948 Thread 0x000001dffd1f1690 Thread exited: 0x000001dffd1f1690
Event: 23.992 Thread 0x000001dffd1f1690 Thread added: 0x000001dffd1f1690
Event: 23.994 Thread 0x000001dffd1f4480 Thread added: 0x000001dffd1f4480
Event: 24.025 Thread 0x000001dffd1f4480 Thread exited: 0x000001dffd1f4480
Event: 24.092 Thread 0x000001dffce5a950 Thread added: 0x000001dffce5a950
Event: 24.095 Thread 0x000001dffce58e10 Thread added: 0x000001dffce58e10
Event: 24.251 Thread 0x000001dffce58740 Thread added: 0x000001dffce58740
Event: 24.422 Thread 0x000001dffce58740 Thread exited: 0x000001dffce58740
Event: 24.457 Thread 0x000001dffce5a950 Thread exited: 0x000001dffce5a950
Event: 24.994 Thread 0x000001dffd1f3df0 Thread added: 0x000001dffd1f3df0
Event: 24.995 Thread 0x000001dffd1f4480 Thread added: 0x000001dffd1f4480
Event: 25.010 Thread 0x000001dffd1f4480 Thread exited: 0x000001dffd1f4480
Event: 25.010 Thread 0x000001dffd1f4480 Thread added: 0x000001dffd1f4480
Event: 25.011 Thread 0x000001dff48312f0 Thread added: 0x000001dff48312f0
Event: 25.030 Thread 0x000001dffce5a950 Thread added: 0x000001dffce5a950
Event: 25.032 Thread 0x000001dffce5b020 Thread added: 0x000001dffce5b020
Event: 25.040 Thread 0x000001dffce5b6f0 Thread added: 0x000001dffce5b6f0
Event: 25.045 Thread 0x000001dff4835490 Thread added: 0x000001dff4835490


Dynamic libraries:
0x00007ff7f9770000 - 0x00007ff7f9780000 	D:\Dev\Env\jdk21\bin\java.exe
0x00007ffe013b0000 - 0x00007ffe015c7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffdfffb0000 - 0x00007ffe00074000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffdfe6d0000 - 0x00007ffdfeaa2000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffdfeab0000 - 0x00007ffdfebc1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffdde3b0000 - 0x00007ffdde3cb000 	D:\Dev\Env\jdk21\bin\VCRUNTIME140.dll
0x00007ffde6190000 - 0x00007ffde61a9000 	D:\Dev\Env\jdk21\bin\jli.dll
0x00007ffe010b0000 - 0x00007ffe01161000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffdff340000 - 0x00007ffdff3e7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffdff290000 - 0x00007ffdff338000 	C:\WINDOWS\System32\sechost.dll
0x00007ffdfedc0000 - 0x00007ffdfede8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe00e30000 - 0x00007ffe00f47000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe01180000 - 0x00007ffe01331000 	C:\WINDOWS\System32\USER32.dll
0x00007ffdfe560000 - 0x00007ffdfe586000 	C:\WINDOWS\System32\win32u.dll
0x00007ffde3000000 - 0x00007ffde329b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5697_none_27154c497380d676\COMCTL32.dll
0x00007ffe003b0000 - 0x00007ffe003d9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffdfef10000 - 0x00007ffdff033000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffdfedf0000 - 0x00007ffdfee8a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffdf6990000 - 0x00007ffdf699a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffdfff60000 - 0x00007ffdfff91000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffde6180000 - 0x00007ffde618c000 	D:\Dev\Env\jdk21\bin\vcruntime140_1.dll
0x00007ffda2200000 - 0x00007ffda228e000 	D:\Dev\Env\jdk21\bin\msvcp140.dll
0x00007ffd49770000 - 0x00007ffd4a48a000 	D:\Dev\Env\jdk21\bin\server\jvm.dll
0x00007ffe002a0000 - 0x00007ffe00311000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffdfe1c0000 - 0x00007ffdfe20d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffdea080000 - 0x00007ffdea0b4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffdfe1a0000 - 0x00007ffdfe1b3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffdfd440000 - 0x00007ffdfd458000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffdde8a0000 - 0x00007ffdde8aa000 	D:\Dev\Env\jdk21\bin\jimage.dll
0x00007ffdf2290000 - 0x00007ffdf24c3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe00610000 - 0x00007ffe009a2000 	C:\WINDOWS\System32\combase.dll
0x00007ffdff0f0000 - 0x00007ffdff1c8000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffded700000 - 0x00007ffded732000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffdfed40000 - 0x00007ffdfedbb000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffdde4d0000 - 0x00007ffdde4df000 	D:\Dev\Env\jdk21\bin\instrument.dll
0x00007ffddc010000 - 0x00007ffddc02f000 	D:\Dev\Env\jdk21\bin\java.dll
0x00007ffdff3f0000 - 0x00007ffdffc91000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffdfe590000 - 0x00007ffdfe6cf000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffdfc300000 - 0x00007ffdfcc1a000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffdffe50000 - 0x00007ffdfff5b000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe00080000 - 0x00007ffe000e9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffdfe3d0000 - 0x00007ffdfe3fb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd9a640000 - 0x00007ffd9a717000 	D:\Dev\Env\jdk21\bin\jsvml.dll
0x00007ffddbfb0000 - 0x00007ffddbfc8000 	D:\Dev\Env\jdk21\bin\zip.dll
0x00007ffdde3a0000 - 0x00007ffdde3b0000 	D:\Dev\Env\jdk21\bin\net.dll
0x00007ffdf6ab0000 - 0x00007ffdf6bdc000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffdf3aa0000 - 0x00007ffdf3c1a000 	C:\Program Files (x86)\Sangfor\VDI\ClientComponent2\SangforTcpX64.dll
0x00007ffe00100000 - 0x00007ffe002a0000 	C:\WINDOWS\System32\ole32.dll
0x00007ffdfee90000 - 0x00007ffdfef0c000 	C:\WINDOWS\System32\WINTRUST.dll
0x00007ffdfebd0000 - 0x00007ffdfed38000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffdfe0c0000 - 0x00007ffdfe0d2000 	C:\WINDOWS\SYSTEM32\MSASN1.dll
0x00007ffdfd900000 - 0x00007ffdfd969000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffddbf90000 - 0x00007ffddbfa6000 	D:\Dev\Env\jdk21\bin\nio.dll
0x00007ffddd1b0000 - 0x00007ffddd1c0000 	D:\Dev\Env\jdk21\bin\verify.dll
0x00007ffdad220000 - 0x00007ffdad265000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffdfdc50000 - 0x00007ffdfdc6b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffdfd3a0000 - 0x00007ffdfd3d7000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffdfd9a0000 - 0x00007ffdfd9c8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffdfdb00000 - 0x00007ffdfdb0c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffdfce80000 - 0x00007ffdfcead000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffdfffa0000 - 0x00007ffdfffa9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd9a7f0000 - 0x00007ffd9a839000 	C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna8865734636501558395.dll
0x00007ffe01360000 - 0x00007ffe01368000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffdf69c0000 - 0x00007ffdf69d9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdf69a0000 - 0x00007ffdf69bf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffdda450000 - 0x00007ffdda477000 	D:\Dev\Env\repo\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffddd200000 - 0x00007ffddd20a000 	D:\Dev\Env\jdk21\bin\management.dll
0x00007ffddc000000 - 0x00007ffddc00b000 	D:\Dev\Env\jdk21\bin\management_ext.dll
0x00007ffddbb70000 - 0x00007ffddbb79000 	D:\Dev\Env\jdk21\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Dev\Env\jdk21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5697_none_27154c497380d676;D:\Dev\Env\jdk21\bin\server;C:\Program Files (x86)\Sangfor\VDI\ClientComponent2;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-146731693;D:\Dev\Env\repo\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=true -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx10G -Xms2G -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\02f6bb0736726e084caf6170a8d996d1\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\02f6bb0736726e084caf6170a8d996d1\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-e58ca1cdc559b555988f07c3ffd266f0-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\02f6bb0736726e084caf6170a8d996d1\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 2147483648                                {product} {command line}
   size_t MaxHeapSize                              = 10737418240                               {product} {command line}
   size_t MaxNewSize                               = 3578789888                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 2147483648                                {product} {command line}
   size_t NewSize                                  = 715653120                                 {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 1431830528                                {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 10737418240                            {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Dev\Env\jdk8
CLASSPATH=.;%JAVA_HOME%\lib\dt.jar;%JAVA_HOME%\lib\tools.jar
PATH=D:\Dev\Env\python\Scripts\;D:\Dev\Env\python\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Google\Chrome\Application\;C:\Program File;C:\Program Files\Git\bin;C:\Users\<USER>\fvm\default\bin;D:\Program Files\Xftp 8\;C:\Program Files\CursorModifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files\Microsoft SQL Server\110\Tools\Binn\;D:\Program Files\swigwin-4.3.0;C:\Users\<USER>\.local\bin;D:\Dev\Env\apache-maven-3.6.2\bin;D:\Dev\Env\uv;D:\Program Files\TortoiseGit\bin;C:\Program Files (x86)\Xshell 8\;C:\Program Files (x86)\Xftp\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\tools\neovim\nvim-win64\bin;C:\Users\<USER>\AppData\Roaming\nvm;node_global;node_global\node_modules\yarn\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Fiddler;C:\msys64\mingw64\bin;D:\Program Files\JetBrains\DataGrip 2023.1\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;D:\Program Files\Neovim\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\GoLand 2024.1.4\bin;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;D:\Java\apache-maven-3.9.6\bin;C:\ProgramData\mingw64\mingw64\bin;C:\Program Files\cursor-id-modifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;%IntelliJ IDEA%;C:\Program Files\ai;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 2 days 0:25 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xe2, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 16 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 16228M (2185M free)
TotalPageFile size 51627M (AvailPageFile size 1053M)
current process WorkingSet (physical memory assigned to process): 682M, peak: 699M
current process commit charge ("private bytes"): 2622M, peak: 2645M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.7+8-LTS-245) for windows-amd64 JRE (21.0.7+8-LTS-245), built on 2025-02-21T05:11:27Z by "mach5one" with MS VC++ 17.6 (VS2022)

END.
