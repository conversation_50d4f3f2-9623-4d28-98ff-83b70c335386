import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../widgets/crypto_input_section.dart';
import '../widgets/crypto_options_section.dart';
import '../widgets/crypto_output_section.dart';
import '../widgets/collapsible_config_section.dart';
import '../widgets/compact_input_output_section.dart';
import '../providers/aes_crypto_providers.dart';

/// AES加密工具页面
class AesCryptoPage extends ConsumerWidget {
  const AesCryptoPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // 处理Android返回手势，返回到首页而不是退出应用
          context.go('/');
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('AES加密工具'),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              context.go('/');
            },
          ),
        ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          // 移动端布局（屏幕宽度小于768px）
          if (constraints.maxWidth < 768) {
            return _buildMobileLayout();
          }

          // 桌面端布局
          return _buildDesktopLayout();
        },
      ),
    ),
    );
  }

  /// 移动端布局
  Widget _buildMobileLayout() {
    return Consumer(
      builder: (context, ref, child) {
        // 初始化AutoProcessor
        ref.watch(autoProcessProvider);

        return const SingleChildScrollView(
          child: Column(
            children: [
              // 可折叠的配置区域
              CollapsibleConfigSection(),

              // 紧凑的输入输出区域
              CompactInputOutputSection(),

              SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  /// 桌面端布局
  Widget _buildDesktopLayout() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          // 输入区域
          CryptoInputSection(),
          SizedBox(height: 16),

          // 配置区域
          CryptoOptionsSection(),
          SizedBox(height: 16),

          // 输出区域
          CryptoOutputSection(),
          SizedBox(height: 32),

          // 使用说明
          _UsageInstructions(),
        ],
      ),
    );
  }
}

/// 使用说明组件
class _UsageInstructions extends StatelessWidget {
  const _UsageInstructions();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '使用说明',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            const _InstructionItem(
              icon: Icons.input,
              title: '双向处理',
              description: '在输入框输入明文自动加密，在输出框输入密文自动解密',
            ),
            const _InstructionItem(
              icon: Icons.settings,
              title: '配置选项',
              description: '支持多种加密模式、密钥长度、填充方式和编码格式',
            ),
            const _InstructionItem(
              icon: Icons.security,
              title: '密钥处理',
              description: '密钥长度不足时自动填充null字节，超长时自动截断',
            ),
            const _InstructionItem(
              icon: Icons.shuffle,
              title: '随机IV',
              description: '非ECB模式可点击IV输入框的随机按钮生成随机初始向量',
            ),
            const _InstructionItem(
              icon: Icons.copy,
              title: '便捷操作',
              description: '支持一键复制、粘贴、清空等操作，提高使用效率',
            ),
          ],
        ),
      ),
    );
  }
}

/// 说明项组件
class _InstructionItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const _InstructionItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
